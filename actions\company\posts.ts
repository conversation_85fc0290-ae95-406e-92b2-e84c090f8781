'use server';

import { revalidatePath } from 'next/cache';
import { createClient as createServerClient } from '@/utils/supabase/server';
import type { StartupJob, PaginatedResponse } from '@/types/startups';
import { v4 as uuidv4 } from 'uuid';

interface GetJobsParams {
  page: number;
  searchTerm?: string;
  companyId?: string;
  status?: string;
  pageSize?: number;
}

export async function getCompanyJobs({
  page = 1,
  searchTerm = '',
  companyId,
  status,
  pageSize = 10
}: GetJobsParams): Promise<PaginatedResponse<StartupJob>> {
  const supabase = createServerClient();
  const offset = (page - 1) * pageSize;

  let query = supabase.from('startup_jobs').select('*', { count: 'exact' });

  if (companyId) {
    query = query.eq('company_id', companyId);
  }

  if (status && status !== 'all') {
    query = query.eq('status', status);
  }

  if (searchTerm) {
    query = query.ilike('title', `%${searchTerm}%`);
  }

  const { data, error, count } = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + pageSize - 1);

  if (error) {
    console.error('Error fetching jobs:', error);
    throw new Error(error.message);
  }

  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data as StartupJob[],
    count,
    currentPage: page,
    totalPages
  };
}

export async function getJobById(jobId: string): Promise<StartupJob | null> {
  const supabase = createServerClient();

  const { data, error } = await supabase
    .from('startup_jobs')
    .select('*, startup_companies(*)')
    .eq('name', jobId)
    .single();

  if (error) {
    console.error('Error fetching job:', error);
    return null;
  }

  return data as StartupJob;
}

export async function createJob(
  jobData: Partial<StartupJob>
): Promise<{ jobId: string }> {
  const supabase = createServerClient();

  try {
    // Extract company data that needs to be updated
    const { recruiter_email, recruiter_linkedin, ...jobDataToInsert } = jobData;

    // Ensure required fields are present
    if (!jobDataToInsert.title) {
      throw new Error('Job title is required');
    }

    if (!jobDataToInsert.company_id) {
      throw new Error('Company name is required');
    }

    // First, get the company ID by name
    const { data: company, error: companyError } = await supabase
      .from('startup_companies')
      .select('name')
      .eq('id', jobDataToInsert.company_id)
      .single();

    if (companyError || !company) {
      console.error('Company does not exist:', jobDataToInsert.company_id);
      throw new Error(
        'The company name provided does not exist. Please create a company profile first.'
      );
    }

    // Use the actual company ID from the database
    jobDataToInsert.company = company.name;

    // Add timestamps and default status
    const now = new Date().toISOString();
    jobDataToInsert.created_at = now;
    jobDataToInsert.updated_at = now;
    jobDataToInsert.status = 'under_review'; // New jobs are under review by default

    // Generate a UUID for the job ID
    const jobId = uuidv4();

    // Ensure all fields match the database schema
    // Remove any fields that might not be in the database schema
    const safeJobData = {
      id: jobId, // Add the generated ID
      title: jobDataToInsert.title,
      company_id: jobDataToInsert.company_id,
      company: jobDataToInsert.company,
      description: jobDataToInsert.description || null,
      location: jobDataToInsert.location || null,
      work_type: jobDataToInsert.work_type || null,
      term: jobDataToInsert.term || null,
      salary: jobDataToInsert.salary || null,
      sponsorship: jobDataToInsert.sponsorship || null,
      min_internship_duration: jobDataToInsert.min_internship_duration || null,
      min_weekly_hours: jobDataToInsert.min_weekly_hours || null,
      job_link: jobDataToInsert.job_link || null,
      payment_negotiable: jobDataToInsert.payment_negotiable || false,
      other_compensation: jobDataToInsert.other_compensation || 'no',
      provide_linkedin_endorsement:
        jobDataToInsert.provide_linkedin_endorsement || false,
      live: jobDataToInsert.live || false,
      required_skills: jobDataToInsert.required_skills || [],
      in_other_market: jobDataToInsert.in_other_market || false,
      created_at: jobDataToInsert.created_at,
      modified_at: jobDataToInsert.updated_at,
      status: jobDataToInsert.status
    };

    const { data, error } = await supabase
      .from('startup_jobs')
      .insert(safeJobData)
      .select()
      .single();

    if (error) {
      console.error('Error creating job:', error);
      throw new Error(`Failed to create job: ${error.message}`);
    }

    // Update company table with recruiter information if provided
    // Only update if both values are provided
    if ((recruiter_email || recruiter_linkedin) && jobDataToInsert.company_id) {
      const companyUpdateData: Record<string, any> = {};

      if (recruiter_email) {
        companyUpdateData.contact_email = recruiter_email;
      }

      if (recruiter_linkedin) {
        companyUpdateData.company_linkedin = recruiter_linkedin;
      }

      // Only update if we have data to update
      if (Object.keys(companyUpdateData).length > 0) {
        const { error: companyUpdateError } = await supabase
          .from('startup_companies')
          .update(companyUpdateData)
          .eq('id', jobDataToInsert.company_id);

        if (companyUpdateError) {
          console.error(
            'Error updating company with recruiter info:',
            companyUpdateError
          );
          // We don't throw here to avoid failing the job creation if company update fails
        }
      }
    }

    revalidatePath('/company/posts');
    return { jobId: data.id };
  } catch (error) {
    console.error('Unexpected error in createJob:', error);
    throw error;
  }
}

export async function updateJob(
  jobId: string,
  jobData: Partial<StartupJob>
): Promise<StartupJob> {
  const supabase = createServerClient();

  // Update modified timestamp
  jobData.updated_at = new Date().toISOString();

  const { data, error } = await supabase
    .from('startup_jobs')
    .update(jobData)
    .eq('id', jobId)
    .select()
    .single();

  if (error) {
    console.error('Error updating job:', error);
    throw new Error(error.message);
  }

  revalidatePath('/company/posts');
  return data as StartupJob;
}

export async function deleteJob(jobId: string): Promise<void> {
  const supabase = createServerClient();

  const { error } = await supabase
    .from('startup_jobs')
    .delete()
    .eq('id', jobId);

  if (error) {
    console.error('Error deleting job:', error);
    throw new Error(error.message);
  }

  revalidatePath('/company/posts');
}
