'use client';

import { Badge } from '@/components/ui/badge';
import { Calendar, Building } from 'lucide-react';

export interface UserJobItemProps {
  title: string;
  date: string;
  category: string;
  status: string;
  company?: string;
}

export const UserJobItem = ({
  title,
  date,
  category,
  status,
  company
}: UserJobItemProps) => (
  <div className="flex items-center justify-between py-3 border-b last:border-0">
    <div className="flex-1">
      <h4 className="font-medium text-sm">{title}</h4>
      <div className="flex items-center space-x-2 mt-1">
        <span className="text-xs text-gray-500 flex items-center">
          <Calendar className="h-3 w-3 mr-1" /> {date}
        </span>
        {company && (
          <span className="text-xs text-gray-500 flex items-center">
            <Building className="h-3 w-3 mr-1" /> {company}
          </span>
        )}
        <Badge variant="outline" className="text-xs">
          {category}
        </Badge>
      </div>
    </div>
    <Badge
      className={
        status === 'Applied'
          ? 'bg-green-100 text-green-800'
          : status === 'Clicked'
            ? 'bg-indigo-100 text-indigo-800'
            : 'bg-blue-100 text-blue-800'
      }
    >
      {status}
    </Badge>
  </div>
);
