/**
 * TypeScript types for Startup database schemas
 */

import { User } from './user';

// Startup Jobs Types
export interface StartupJob {
  id: string;
  title: string | null;
  description: string | null;
  experience: string | null;
  expiry_date: string | null;
  favored: string | null;
  in_other_market: boolean | null;
  job_link: string | null;
  live: boolean;
  location: string | null;
  min_internship_duration: string | null;
  min_weekly_hours: string | null;
  work_type: string | null;
  other_compensation: string | null;
  payment_negotiable: boolean | null;
  provide_linkedin_endorsement: boolean | null;
  required_skills: string[] | null;
  salary: string | null;
  sponsorship: string | null;
  term: string | null;
  created_at: string | null;
  updated_at: string | null;
  slug: string | null;
  creator_email: string | null;
  // busy_broker_id: string | null
  company:
    | string
    | null
    | {
        id: string;
        name: string;
        logo_url: string | null;
      };
  company_id: string | null;
  duties?: string[] | null;
  linkedin_required?: boolean;
  startup_company?: {
    id: string;
    name: string;
    logo_url: string | null;
  } | null;
  applications_count?: number;
  is_first_job?: boolean;
  status?: string;
  // These fields are not stored in the job table but used for form submission
  recruiter_email?: string;
  recruiter_linkedin?: string;
  hasApplied?: boolean;
}

export interface StartupJobFormData {
  id?: string;
  title: string;
  company_id: string;
  description: string;
  location: string;
  work_type: string;
  term: string;
  salary: string;
  sponsorship: string;
  min_internship_duration: string;
  min_weekly_hours: string;
  job_link: string;
  recruiter_email?: string;
  recruiter_linkedin?: string;
  payment_negotiable: boolean;
  other_compensation: string;
  in_other_market: boolean;
  provide_linkedin_endorsement: boolean;
  live: boolean;
  required_skills: string[];
}

export interface JobFilters {
  title?: string;
  location?: string;
  sponsorship?: string;
  term?: string;
}

// Startup Companies Types
export interface StartupCompany {
  id: string;
  name: string | null;
  name_lowercase: string | null;
  description: string | null;
  company_crunchbase: string | null;
  company_linkedin: string | null;
  company_website: string | null;
  contact_email: string | null;
  current_funding: string | null;
  direct_contact_email: string | null;
  employees: string | null;
  favored_candidates: string | null;
  founder_linkedin: string | null;
  founder_school: string | null;
  funding: string | null;
  industry: string | null;
  insider: string | null;
  internal_email: string | null;
  job: string | null;
  legally_incorporated: string | null;
  location: string | null;
  logo_url: string | null;
  open_position: string | null;
  parse_resumes_to_csv: string | null;
  source: string | null;
  sponsorship: string | null;
  year_of_incorporation: string | null;
  created_at: string | null;
  modified_at: string | null;
  slug: string | null;
  creator_email: string | null;
  applications_count?: number;
  open_for_reverse_hiring?: boolean;
}

export interface StartupCompanyFormData {
  name: string;
  description: string;
  industry: string;
  founder_school: string;
  contact_email: string;
  internal_email: string;
  founder_linkedin: string;
  company_crunchbase: string;
  company_linkedin: string;
  company_website: string;
  funding: string;
  legally_incorporated: string;
  logo_url: string;
}

// Startup Applications Types
export interface StartupApplication {
  id: string;
  additional_materials: string | null;
  candidate: string | null;
  candidate_name: string | null;
  company: string | null;
  company_name: string | null;
  cover_note_file: string | null;
  incomplete: string | null;
  job: string | null;
  job_title: string | null;
  old_job_title: string | null;
  referral_request: string | null;
  resume: string | null;
  shortlisted: string | null;
  sponsorship: string | null;
  status: string | null;
  unit: string | null;
  yoe_internship: string | null;
  yoe_job: string | null;
  creation_date: string | null;
  modified_date: string | null;
  slug: string | null;
  creator: string | null;
}

// Pagination and Query Types
export interface JobsQueryParams {
  page: number;
  searchTerm?: string;
  companyId?: string;
}

export interface ApplicationsQueryParams {
  page: number;
  searchTerm?: string;
  user: User;
}

export interface CompaniesQueryParams {
  page?: number;
  searchTerm?: string;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number | null;
  currentPage: number;
  totalPages: number;
}
