'use client';

import { marketingConfig } from '@/config/marketing';
import CircularNavigation from '@/components/navigation';
import { useUser } from '@/hooks/useUser';
import FooterPrimary from '@/components/footer-primary';
interface AboutLayoutProps {
  children: React.ReactNode;
}

export default function AboutLayout({ children }: AboutLayoutProps) {
  const { user } = useUser();

  // Pass the user_type if available, otherwise pass false
  const userType = user?.user_type || null;

  return (
    <div className="flex min-h-screen flex-col items-center w-full">
      <CircularNavigation
        items={marketingConfig.mainNav}
        user={user ? true : false}
        userType={userType}
      />
      {children}
      <FooterPrimary />
    </div>
  );
}
