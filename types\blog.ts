// Blog Types
export interface Blog {
    id: string
    author: string | null
    block1: string | null
    block2: string | null
    block3: string | null
    block4: string | null
    creators_logo: string | null
    liked_user: string[] | null
    saved_user: string[] | null
    title: string | null
    creation_date: string | null
    modified_date: string | null
    slug: string | null
    creator: string | null
  }
  
  export interface BlogFormData {
    author: string
    title: string
    block1: string
    block2: string
    block3: string
    block4: string
    creators_logo?: string
  }
  
  export interface BlogsQueryParams {
    page: number
    searchTerm?: string
  }
  
  