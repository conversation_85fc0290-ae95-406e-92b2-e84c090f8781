'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { User<PERSON>heck, X, ArrowUpDown, Mail, Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import { UserJobItem } from './user-job-item';
import { getJobClicksDirectly } from '@/actions/admin/analytics';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Define types for our job click data
interface JobClickData {
  id: string;
  job_id: string;
  job_title: string;
  company: string;
  keyword: string;
  click_date: string;
}

interface KeywordDistribution {
  keyword: string;
  count: number;
  color: string;
}

export interface UserDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  username: string;
  userId: string;
  isVip: boolean;
  fullName?: string;
}

// Colors for the keyword distribution chart
const chartColors = [
  '#4ade80',
  '#60a5fa',
  '#f97316',
  '#8b5cf6',
  '#ec4899',
  '#14b8a6',
  '#eab308',
  '#06b6d4',
  '#c084fc',
  '#fb7185'
];

export const UserDetailModal = ({
  isOpen,
  onClose,
  username,
  userId,
  isVip,
  fullName
}: UserDetailModalProps) => {
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [jobClicks, setJobClicks] = useState<JobClickData[]>([]);
  const [keywordDistribution, setKeywordDistribution] = useState<
    KeywordDistribution[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [totalClicks, setTotalClicks] = useState(0);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadUserJobClickData = async () => {
      if (!isOpen || !userId) {
        return;
      }

      setLoading(true);
      setError(null);

      try {
        let clickData: JobClickData[] = [];

        // Get job clicks using direct query
        clickData = await getJobClicksDirectly(userId);

        // Set results
        setJobClicks(clickData);
        setTotalClicks(clickData.length);

        // Process keyword distribution
        processKeywordDistribution(clickData);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        setError(`Failed to load job click data: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    loadUserJobClickData();
  }, [isOpen, userId, username, isVip]);

  useEffect(() => {
    // Sort the job data based on click date
    if (jobClicks.length > 0) {
      const sorted = [...jobClicks].sort((a, b) => {
        const dateA = new Date(a.click_date).getTime();
        const dateB = new Date(b.click_date).getTime();
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      });
      setJobClicks(sorted);
    }
  }, [sortOrder, jobClicks.length]);

  // Process the keyword distribution from job click data
  const processKeywordDistribution = (clickData: JobClickData[]) => {
    const keywordMap = new Map<string, number>();

    // Count occurrences of each keyword
    clickData.forEach((click) => {
      if (!click.keyword) return;

      const keyword = click.keyword.trim();
      if (keyword) {
        keywordMap.set(keyword, (keywordMap.get(keyword) || 0) + 1);
      }
    });

    // Convert to array and sort by count (descending)
    const sortedKeywords = Array.from(keywordMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10) // Take top 10 keywords
      .map(([keyword, count], index) => ({
        keyword,
        count,
        color: chartColors[index % chartColors.length]
      }));

    setKeywordDistribution(sortedKeywords);
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="flex items-center text-xl">
            <div className="flex-1 flex items-center gap-2">
              User Activity Details
              {isVip && (
                <Badge className="bg-blue-100 text-blue-800">
                  <UserCheck className="h-3 w-3 mr-1" />
                  VIP
                </Badge>
              )}
            </div>
            <DialogClose className="absolute right-4 top-4">
              <X className="h-4 w-4" />
            </DialogClose>
          </DialogTitle>

          <div className="mt-2 space-y-1 text-sm text-zinc-500 dark:text-zinc-400">
            {fullName && fullName !== 'N/A' && (
              <div className="font-semibold text-base">{fullName}</div>
            )}
            <div className="flex items-center gap-1">
              <Mail className="h-3.5 w-3.5" />
              <span className="font-mono">{username}</span>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="py-12 flex justify-center items-center">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        ) : (
          <>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
              {/* Job Click History */}
              <div className="flex flex-col h-full">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-base font-semibold">
                    Job Click History
                    <Badge variant="outline" className="ml-2">
                      {totalClicks} clicks
                    </Badge>
                  </h3>
                  <button
                    className="text-xs flex items-center text-gray-500 hover:text-gray-900"
                    onClick={() =>
                      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                    }
                  >
                    <span>Sort by Date</span>
                    <ArrowUpDown className="h-3 w-3 ml-1" />
                  </button>
                </div>

                {jobClicks.length > 0 ? (
                  <div className="border rounded-lg overflow-hidden flex-1 flex flex-col">
                    <div className="divide-y overflow-y-auto max-h-[400px]">
                      {jobClicks.map((job) => (
                        <UserJobItem
                          key={job.id}
                          title={job.job_title || 'Unknown Title'}
                          company={job.company || 'Unknown Company'}
                          date={job.click_date}
                          category={job.keyword || 'Uncategorized'}
                          status="Clicked"
                        />
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="border rounded-lg p-6 text-center text-muted-foreground">
                    <p>No job click history found for this user.</p>
                  </div>
                )}
              </div>

              {/* Job Interest Charts */}
              <div className="space-y-6">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Total Engagement
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="h-[150px]">
                    <div className="h-full w-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="text-5xl font-bold mb-2">
                          {totalClicks}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Job Listings Clicked
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">
                      Keyword Interests
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {keywordDistribution.length > 0 ? (
                      <div className="space-y-2">
                        {keywordDistribution.map((item) => (
                          <div key={item.keyword} className="flex items-center">
                            <div
                              className="h-3 w-3 rounded-full mr-2"
                              style={{ backgroundColor: item.color }}
                            ></div>
                            <span
                              className="text-sm flex-1 truncate"
                              title={item.keyword}
                            >
                              {item.keyword}
                            </span>
                            <span className="text-sm font-medium">
                              {item.count}
                            </span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-sm text-muted-foreground">
                        No keyword data available
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};
