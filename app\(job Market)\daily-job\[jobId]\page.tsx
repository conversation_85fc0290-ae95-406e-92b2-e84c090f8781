'use client';

import { useEffect, useState } from 'react';
import { useParams, notFound, useRouter } from 'next/navigation';
import { getDailyJobs } from '@/actions/admin/web-crawler';
import { checkExistingFileTracker } from '@/actions/download-tracker';
import type { DailyJob } from '@/types/crawler';
import type { JobDetailRow } from '@/types/google-sheets'; // UPDATED IMPORT
import { fetchGoogleSheetDataByUrl } from '@/app/actions/google-sheets'; // NEW IMPORT
import { format } from 'date-fns';
import { trackFileView } from '@/utils/tracking';
import { trackJobClick } from '@/utils/job-tracking'; // Import job click tracking utility
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  ExternalLink,
  Terminal,
  FileText,
  Crown,
  ChevronLeft
} from 'lucide-react';
import { useUser } from '@/hooks/useUser';
import { toast } from '@/components/ui/use-toast';

// Import our custom components
import { JobDetailDialog } from '@/components/daily-job-analytics/job-detail/JobDetailDialog';
import { JobListingTable } from '@/components/daily-job-analytics/job-detail/JobListingTable';
import { JobFilters } from '@/components/daily-job-analytics/job-detail/JobFilters';
import { JobPagination } from '@/components/daily-job-analytics/job-detail/JobPagination';

// Define the structure of a row within the job sheet (based on the image)
// This is illustrative; actual data parsing from Google Sheet needs backend implementation

// Helper function for fetching and parsing Google Sheet data
const getParsedJobDetails = async (
  fileUrl: string | null,
  currentJobId: string,
  isJobAccessible: boolean,
  fileType: string | undefined,
  notice?: string | null
): Promise<JobDetailRow[]> => {
  // Return empty array if no fileUrl provided
  if (!fileUrl) return [];

  try {
    // For Smart Search files, try multiple sheet names in order
    if (fileType === 'Smart Search') {
      // Special handling for "Operation not supported" error which is common with Smart Search files
      const operationNotSupportedError =
        'This operation is not supported for this document';

      // Check if URL might be a view-only link or other special format
      if (fileUrl.includes('htmlview') || fileUrl.includes('pubhtml')) {
        console.warn(
          'The Google Sheet appears to be in HTML view mode, which is not compatible with the API'
        );
        throw new Error(
          'This Google Sheet link is in a view-only format that cannot be accessed via API. Please view the original sheet directly.'
        );
      }

      // If the sheet is likely a special file type (like a CSV export or filtered view)
      // we should inform the user immediately rather than trying multiple sheet names
      if (
        notice === 'FA' ||
        notice === 'Quant' ||
        notice === 'Supply Chain' ||
        notice === 'UIUX' ||
        notice === 'Traditional Engineering Jobs' ||
        notice === 'Jobs in Small Towns'
      ) {
        // Some sheet formats don't support API access - return empty data array instead
        // This is better than throwing an error, as it allows the UI to display a helpful message
        return [];
      }

      // Regular sheet name fallback process
      const sheetNamesToTry = [
        notice, // First try the notice field
        'All Jobs', // Then try default sheet name
        'Sheet1', // Common first sheet name
        '' // Empty string will use the first sheet in the workbook
      ].filter(Boolean); // Remove any null/undefined values

      // Try each sheet name until one works
      for (const sheetName of sheetNamesToTry) {
        try {
          const sheetData = await fetchGoogleSheetDataByUrl(
            fileUrl,
            'A:L',
            sheetName as string
          );

          // If we get data or at least didn't throw an error, return the results
          return sheetData;
        } catch (sheetError) {
          const errorMessage =
            sheetError instanceof Error ? sheetError.message : 'Unknown error';

          // If we encounter the "operation not supported" error, which is common with certain
          // types of Google Sheets, stop trying and inform the user to use direct view
          if (errorMessage.includes(operationNotSupportedError)) {
            console.warn(
              'This Google Sheet does not support API access - likely a special format'
            );
            // Return empty array rather than throwing - allows us to show a helpful message in the UI
            return [];
          }

          // Continue to the next sheet name for other types of errors
        }
      }

      // If we got here, none of the sheet names worked
      console.error('All attempts to fetch Smart Search sheet data failed');
      return []; // Return empty array instead of throwing - better user experience
    }

    // For other file types, use the standard approach
    let sheetName = fileType === 'Weekly Intern' ? 'Sheet1' : 'All Jobs';

    // Fetch data from Google Sheet (assuming columns A to L)
    const sheetData = await fetchGoogleSheetDataByUrl(
      fileUrl,
      'A:L',
      sheetName
    );

    return sheetData;
  } catch (apiError) {
    console.error(
      `Error fetching Google Sheet data for job ${currentJobId}:`,
      apiError
    );

    // Special handling for "operation not supported" errors
    const errorMessage =
      apiError instanceof Error ? apiError.message : 'Unknown error';
    if (
      errorMessage.includes('This operation is not supported for this document')
    ) {
      console.warn(
        'This Google Sheet does not support API access - likely a special format'
      );
      // Return empty array rather than throwing
      return [];
    }

    // Re-throw other types of errors
    if (apiError instanceof Error) {
      throw new Error(
        `Failed to load details from Google Sheet: ${apiError.message}`
      );
    }

    throw new Error(
      'An unknown error occurred while fetching details from Google Sheet.'
    );
  }
};

export default function JobDetailPage() {
  const params = useParams();
  const router = useRouter();
  const jobId = params.jobId as string;
  const { user, loading: userLoading } = useUser();

  // =========== STATE DECLARATIONS ===========
  // Job data state
  const [jobMetadata, setJobMetadata] = useState<DailyJob | null>(null);
  const [jobDetails, setJobDetails] = useState<JobDetailRow[]>([]);
  const [filteredJobDetails, setFilteredJobDetails] = useState<JobDetailRow[]>(
    []
  );
  const [allJobs, setAllJobs] = useState<DailyJob[]>([]);

  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAccessible, setIsAccessible] = useState(false);
  // Track if we've already tracked the view to prevent duplicates
  const [hasTrackedView, setHasTrackedView] = useState(false);
  // Store the tracker ID after creation to reuse for job clicks
  const [fileTrackerId, setFileTrackerId] = useState<string | null>(null);

  // Pagination state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(50);
  const [totalPages, setTotalPages] = useState(1);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilters, setActiveFilters] = useState<Record<string, string>>(
    {}
  );

  // Detail dialog state
  const [selectedJob, setSelectedJob] = useState<JobDetailRow | null>(null);
  const [detailDialogOpen, setDetailDialogOpen] = useState(false);

  // =========== TABLE CONFIGURATION ===========
  // Define table columns
  const columns: Array<{ key: keyof JobDetailRow; header: string }> = [
    { key: 'Keyword', header: 'Keyword' },
    { key: 'Job_title', header: 'Job Title' },
    { key: 'Company', header: 'Company' },
    { key: 'Location', header: 'Location' },
    { key: 'Qualifications', header: 'Qualifications' },
    { key: 'Apply_links', header: 'Apply Links' },
    { key: 'Work Mode', header: 'Work Mode' }
  ];

  // Filter fields for dropdowns
  const filterableFields = [
    { key: 'Keyword', label: 'Keyword' },
    { key: 'Company', label: 'Company' },
    { key: 'Location', label: 'Location' },
    { key: 'Work Mode', label: 'Work Mode' }
  ];

  // =========== HELPER FUNCTIONS ===========
  // Helper function to get most recent jobs by type
  const getMostRecentJobsByType = (jobs: DailyJob[]) => {
    if (!jobs.length) return [];

    // Create a map to store the most recent job of each type
    const mostRecentJobs = new Map();

    // Find the most recent job of each type
    jobs.forEach((job) => {
      const type = job.file_type || 'Daily Jobs';

      const isMoreRecent =
        !mostRecentJobs.has(type) ||
        (job.posting_date &&
          mostRecentJobs.get(type).posting_date &&
          new Date(job.posting_date) >
            new Date(mostRecentJobs.get(type).posting_date));

      if (isMoreRecent) {
        mostRecentJobs.set(type, job);
      }
    });

    // Return only Daily Jobs and Smart Search types
    const result = [];
    if (mostRecentJobs.has('Daily Jobs')) {
      result.push(mostRecentJobs.get('Daily Jobs'));
    }
    if (mostRecentJobs.has('Smart Search')) {
      result.push(mostRecentJobs.get('Smart Search'));
    }

    return result;
  };

  // Check if the job is accessible for the current user
  const checkJobAccessibility = (job: DailyJob, jobs: DailyJob[]) => {
    // VIP users can access all jobs
    if (user?.vip) return true;

    // Non-registered users can't access any jobs
    if (!user) return false;

    // Non-VIP users can access sample jobs
    if (job.id.startsWith('sample')) return true;

    // Non-VIP users can only access the most recent Daily Job and Smart Search
    const recentJobs = getMostRecentJobsByType(jobs);
    return recentJobs.some((recentJob) => recentJob.id === job.id);
  };

  // Get unique values for filter dropdowns
  const getUniqueValues = (field: keyof JobDetailRow): string[] => {
    const values = new Set<string>();

    jobDetails.forEach((job) => {
      const fieldValue = job[field];
      if (fieldValue && fieldValue.toString().trim() !== '') {
        values.add(fieldValue as string);
      }
    });

    return Array.from(values).sort();
  };

  // Calculate current page rows
  const getCurrentPageRows = () => {
    const startIdx = (page - 1) * pageSize;
    const endIdx = startIdx + pageSize;
    return filteredJobDetails.slice(startIdx, endIdx);
  };

  // =========== EVENT HANDLERS ===========
  // Handler for filter changes
  const handleFilterChange = (field: string, value: string) => {
    setActiveFilters((prev) => {
      const newFilters = { ...prev };

      // If "all" is selected, remove the filter
      if (value === 'all') {
        delete newFilters[field];
      } else {
        newFilters[field] = value;
      }

      return newFilters;
    });
  };

  // Handler to clear all filters
  const clearAllFilters = () => {
    setActiveFilters({});
    setSearchTerm('');
  };

  // Handler to open the original Google Sheet
  const handleOpenSheet = () => {
    if (!jobMetadata?.crawler_file) return;

    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in.',
        variant: 'destructive'
      });
      return;
    }

    if (!isAccessible) {
      toast({
        title: 'Access Restricted',
        description:
          'Only VIP users can access older job listings. Free users can only access the most recent listings.',
        variant: 'destructive'
      });
      return;
    }

    // Remove tracking from here as we already track the page view
    // This prevents duplicate tracking

    window.open(jobMetadata.crawler_file, '_blank');
  };

  // Handler for upgrade button click
  const handleUpgradeClick = () => {
    router.push('/membership');
  };

  // Handler for page navigation
  const handlePageChange = (newPage: number) => {
    setPage(Math.max(1, Math.min(newPage, totalPages)));
  };

  // Handler for page size change
  const handlePageSizeChange = (newSize: string) => {
    setPageSize(parseInt(newSize));
  };

  // Handler for row click to show detail dialog
  const handleRowClick = async (job: JobDetailRow) => {
    setSelectedJob(job);
    setDetailDialogOpen(true);

    // Track the job click if user is logged in and has access to the job
    if (user && isAccessible && jobMetadata) {
      try {
        await trackJobClick(jobMetadata.id, user.id, {
          keyword: job.Keyword,
          jobTitle: job.Job_title,
          company: job.Company
        });
      } catch (error) {
        console.error('Error tracking job click:', error);
        // Continue showing the dialog even if tracking fails
      }
    }
  };

  // =========== EFFECTS ===========
  // Effect to filter jobs when search term or filters change
  useEffect(() => {
    if (jobDetails.length === 0) return;

    let filtered = [...jobDetails];

    // Apply search term if exists (case insensitive)
    if (searchTerm.trim() !== '') {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(
        (job) =>
          (job.Job_title && job.Job_title.toLowerCase().includes(term)) ||
          (job.Company && job.Company.toLowerCase().includes(term)) ||
          (job.Location && job.Location.toLowerCase().includes(term)) ||
          (job.Keyword && job.Keyword.toLowerCase().includes(term))
      );
    }

    // Apply all active filters
    Object.entries(activeFilters).forEach(([field, value]) => {
      if (value !== 'all') {
        filtered = filtered.filter((job) => {
          const fieldValue = job[field as keyof JobDetailRow];
          return (
            fieldValue &&
            fieldValue.toString().toLowerCase().includes(value.toLowerCase())
          );
        });
      }
    });

    setFilteredJobDetails(filtered);
    setTotalPages(Math.ceil(filtered.length / pageSize));
    setPage(1); // Reset to first page when filters change
  }, [searchTerm, activeFilters, jobDetails, pageSize]);

  // Effect to fetch job data
  useEffect(() => {
    const fetchJobData = async () => {
      // Validate input params
      if (!jobId) {
        setError('Job ID not found.');
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Step 1: Fetch all jobs and find the requested one
        const allJobsData = await getDailyJobs();
        setAllJobs(allJobsData);

        const foundJob = allJobsData.find((j) => j.id === jobId);
        if (!foundJob) {
          setError('Job not found.');
          notFound(); // Trigger Next.js not found page
          return;
        }

        setJobMetadata(foundJob);

        // Step 2: Check if user has access to this job
        const jobIsAccessible = checkJobAccessibility(foundJob, allJobsData);
        setIsAccessible(jobIsAccessible);

        // Track the page view if user is logged in, has access, and view hasn't been tracked for this session
        if (user && jobIsAccessible && !hasTrackedView) {
          try {
            // Check if a tracker already exists in the DB for this user/file
            const trackerExistsInDB = await checkExistingFileTracker(
              foundJob.id,
              user.id
            );

            if (!trackerExistsInDB) {
              // Only call trackFileView if no tracker exists in DB for this user/file combination
              // or if it's the first view in this session (hasTrackedView is false)
              await trackFileView(foundJob.id, user.id, user.vip);
            }
            // Mark that we've processed view tracking for this session to prevent multiple calls
            // This covers both cases: tracker existed, or we just created/updated it.
            setHasTrackedView(true);
          } catch (error) {
            console.error('Error during page view tracking logic:', error);
          }
        }

        // Show access restriction notifications if needed
        if (!jobIsAccessible && !foundJob.id.startsWith('sample')) {
          if (!user) {
            toast({
              title: 'Authentication Required',
              description: 'Please sign in to view job details.',
              variant: 'destructive'
            });
          } else {
            toast({
              title: 'VIP Access Required',
              description:
                'Only VIP users can access older job listings. Free users can only access the most recent listings.',
              variant: 'destructive'
            });
          }
        }

        // Step 3: Fetch job details if a Google Sheet URL exists
        if (foundJob.crawler_file) {
          try {
            // For all file types, attempt to fetch and parse
            const details = await getParsedJobDetails(
              foundJob.crawler_file,
              foundJob.id,
              true,
              foundJob.file_type || undefined,
              foundJob.notice || null
            );

            // Update state with fetched data
            setJobDetails(details);
            setFilteredJobDetails(details);
            setTotalPages(Math.ceil(details.length / pageSize));
          } catch (detailError) {
            console.error('Error fetching sheet details:', detailError);

            // Set error but don't block the whole page
            setError(
              detailError instanceof Error
                ? detailError.message
                : 'Failed to load sheet details'
            );

            // Clear job details on error
            setJobDetails([]);
            setFilteredJobDetails([]);
          }
        } else {
          // No crawler_file available
          setJobDetails([]);
          setFilteredJobDetails([]);
        }
      } catch (err) {
        console.error('Error fetching job details:', err);

        // Set appropriate error message
        if (err instanceof Error) {
          setError(`Failed to load job details: ${err.message}`);
        } else {
          setError('An unknown error occurred.');
        }
      } finally {
        setLoading(false);
      }
    };

    // Fetch data only when user loading is finished
    if (!userLoading) {
      fetchJobData();
    }
  }, [jobId, user, userLoading, pageSize, hasTrackedView]);

  // =========== RENDER LOGIC ===========
  if (loading || userLoading) {
    return (
      <div className="container mx-auto py-12 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-40" />
          </div>
          <Skeleton className="h-10 w-40" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-72" />
            <div className="mt-4 space-y-4">
              <Skeleton className="h-10 w-full" />
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Skeleton key={i} className="h-10" />
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <div className="h-8 border-b bg-muted/40" />
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="h-12 border-b last:border-0" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error && !jobMetadata) {
    // Show error only if we couldn't even load metadata
    return (
      <div className="container mx-auto py-12">
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!jobMetadata) {
    // Should be caught by loading or error states, but as a fallback
    return (
      <div className="container mx-auto py-12">Job data not available.</div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* ===== HEADER SECTION ===== */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <Badge variant="secondary" className="mb-2">
            {jobMetadata.file_type || 'Job Details'}
            {jobMetadata.file_type === 'Smart Search' && jobMetadata.notice && (
              <span className="ml-1">: {jobMetadata.notice}</span>
            )}
          </Badge>
          <h1 className="text-3xl font-bold tracking-tight">
            Job Listing Details
          </h1>
          <p className="text-muted-foreground">
            Posted on:{' '}
            {jobMetadata.posting_date
              ? format(new Date(jobMetadata.posting_date), 'PPP')
              : 'N/A'}
          </p>
        </div>
        <div className="flex gap-2">
          {jobMetadata.crawler_file && isAccessible && (
            <Button onClick={handleOpenSheet}>
              <ExternalLink className="mr-2 h-4 w-4" />
              Open Original Sheet
            </Button>
          )}
          {!isAccessible && user && !user.vip && (
            <Button
              variant="default"
              onClick={handleUpgradeClick}
              className="bg-amber-600 hover:bg-amber-700"
            >
              <Crown className="mr-2 h-4 w-4" />
              Upgrade to VIP
            </Button>
          )}
        </div>
      </div>

      {/* ===== NAVIGATION ===== */}
      <div className="mb-4">
        <Button
          variant="outline"
          onClick={() => router.push('/daily-job')}
          className="flex items-center"
        >
          <ChevronLeft className="mr-1 h-4 w-4" />
          Back to Jobs
        </Button>
      </div>

      {/* ===== ALERTS AND NOTICES ===== */}
      {/* Error alert */}
      {error && (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Error Loading Details</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Access Restriction Alert */}
      {!isAccessible && user && !user.vip && (
        <Alert variant="default" className="bg-amber-50 border-amber-300">
          <Crown className="h-4 w-4 text-amber-500" />
          <AlertTitle className="text-amber-700">
            VIP Access Required
          </AlertTitle>
          <AlertDescription className="text-amber-600">
            This is not the most recent {jobMetadata.file_type || 'job listing'}
            . Free users can only access the most recent Daily Job and Smart
            Search listings. Upgrade to VIP for access to all historical job
            listings.
          </AlertDescription>
        </Alert>
      )}

      {/* Not Logged In Alert */}
      {!user && (
        <Alert>
          <Terminal className="h-4 w-4" />
          <AlertTitle>Authentication Required</AlertTitle>
          <AlertDescription>
            Please sign in to view job details.
          </AlertDescription>
        </Alert>
      )}

      {/* Data Source Note */}
      {jobDetails.length > 0 && (
        <Alert variant="default">
          <FileText className="h-4 w-4" />
          <AlertTitle>Data Source Note</AlertTitle>
          <AlertDescription>
            The table below shows data parsed from the linked Google Sheet.
            {!isAccessible &&
              !user?.vip &&
              ' To open the original sheet, you need VIP access or it must be one of the most recent job listings.'}
            {isAccessible &&
              !user?.vip &&
              " You have access to open this sheet because it's one of the most recent in its category."}
          </AlertDescription>
        </Alert>
      )}

      {/* Smart Search Notice */}
      {jobMetadata.file_type === 'Smart Search' && (
        <Alert variant="default" className="bg-blue-50 border-blue-200">
          <FileText className="h-4 w-4 text-blue-500" />
          <AlertTitle>
            Smart Search Results: {jobMetadata.notice || ''}
          </AlertTitle>
          <AlertDescription className="flex flex-col gap-3">
            <p>
              {jobDetails.length > 0 ? (
                <>
                  Successfully loaded {jobDetails.length} job listings from the
                  Smart Search sheet.
                  {!isAccessible &&
                    !user?.vip &&
                    ' VIP access is required to view older Smart Search files.'}
                </>
              ) : (
                <>
                  {jobMetadata.notice === 'FA' ||
                  jobMetadata.notice === 'Quant' ||
                  jobMetadata.notice === 'Supply Chain' ||
                  jobMetadata.notice === 'UIUX' ||
                  jobMetadata.notice === 'Traditional Engineering Jobs' ||
                  jobMetadata.notice === 'Jobs in Small Towns' ? (
                    <>
                      <span className="font-medium">
                        This "{jobMetadata.notice}" sheet uses a special format
                      </span>{' '}
                      that cannot be displayed directly in our interface. These
                      sheets often use custom formatting or filters that are not
                      compatible with our automatic data extraction. Please use
                      the button below to view the sheet directly.
                    </>
                  ) : (
                    <>
                      We tried to access the Smart Search data using multiple
                      sheet names (including "
                      {jobMetadata.notice || 'the sheet name'}"), but couldn't
                      find accessible data. Please view the original Google
                      Sheet directly.
                    </>
                  )}
                  {!isAccessible &&
                    !user?.vip &&
                    ' VIP access is required to view older Smart Search files.'}
                </>
              )}
            </p>
            {isAccessible && jobMetadata.crawler_file && (
              <Button
                variant="outline"
                onClick={handleOpenSheet}
                className="w-fit"
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Open Google Sheet
              </Button>
            )}
            {!isAccessible && !user?.vip && (
              <Button
                variant="default"
                onClick={handleUpgradeClick}
                className="bg-amber-600 hover:bg-amber-700 w-fit"
              >
                <Crown className="mr-2 h-4 w-4" />
                Upgrade to VIP
              </Button>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* ===== JOB DETAILS TABLE ===== */}
      {jobDetails.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Detailed Job Listings</CardTitle>
            <CardDescription>
              Browse the {jobDetails.length} jobs included in this file.
              Currently showing page {page} of {totalPages}.
            </CardDescription>

            {/* Job Filters Component */}
            <JobFilters
              jobDetails={jobDetails}
              filteredJobDetails={filteredJobDetails}
              filterableFields={filterableFields}
              activeFilters={activeFilters}
              searchTerm={searchTerm}
              pageSize={pageSize}
              page={page}
              totalPages={totalPages}
              onSearchChange={setSearchTerm}
              onFilterChange={handleFilterChange}
              onPageSizeChange={handlePageSizeChange}
              clearAllFilters={clearAllFilters}
              getUniqueValues={getUniqueValues}
            />
          </CardHeader>

          <CardContent>
            {/* Job Listing Table Component */}
            <JobListingTable
              columns={columns}
              rows={getCurrentPageRows()}
              onRowClick={handleRowClick}
              fileId={jobMetadata?.id}
            />

            {/* Job Detail Dialog */}
            <JobDetailDialog
              job={selectedJob}
              isOpen={detailDialogOpen}
              onOpenChange={setDetailDialogOpen}
              fileId={jobMetadata?.id}
            />

            {/* Pagination Controls */}
            <JobPagination
              page={page}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </CardContent>
        </Card>
      )}

      {/* ===== UPGRADE PROMPT ===== */}
      {!isAccessible && user && !user.vip && (
        <Card className="overflow-hidden border-amber-200">
          <div className="bg-gradient-to-r from-amber-500 to-amber-600 p-1" />
          <CardContent className="pt-6 text-center">
            <div className="bg-amber-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <Crown className="h-8 w-8 text-amber-600" />
            </div>
            <h3 className="text-xl font-semibold mb-2">
              Unlock All Job Listings with VIP
            </h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Get unlimited access to all job listings, including historical
              data and advanced features. VIP members can access every job in
              our database.
            </p>
            <Button
              onClick={handleUpgradeClick}
              className="bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700"
              size="lg"
            >
              <Crown className="mr-2 h-4 w-4" />
              Upgrade to VIP
            </Button>
          </CardContent>
        </Card>
      )}

      {/* ===== NO DATA STATE ===== */}
      {jobDetails.length === 0 &&
        !loading &&
        !error &&
        jobMetadata.crawler_file && (
          <Card>
            <CardContent className="py-12 text-center">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-semibold mb-2">
                No Job Details Available
              </h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-4">
                {jobMetadata.file_type === 'Weekly Intern'
                  ? 'Weekly Intern sheets use a different format that requires viewing the original Google Sheet.'
                  : jobMetadata.file_type === 'Smart Search' &&
                      (jobMetadata.notice === 'FA' ||
                        jobMetadata.notice === 'Quant' ||
                        jobMetadata.notice === 'Supply Chain' ||
                        jobMetadata.notice === 'UIUX' ||
                        jobMetadata.notice === 'Traditional Engineering Jobs' ||
                        jobMetadata.notice === 'Jobs in Small Towns')
                    ? `This "${jobMetadata.notice}" Smart Search sheet uses a special format that cannot be displayed here. These sheets often contain custom formatting or use Google Sheets features that are not compatible with our automatic data extraction. Please view the original sheet directly.`
                    : jobMetadata.file_type === 'Smart Search'
                      ? `Could not find any data in the Smart Search sheet. We tried using "${jobMetadata.notice || 'the notice field'}" and several fallback sheet names, but none contained accessible data. Try viewing the original sheet to identify the correct sheet name.`
                      : 'No detailed job data could be found or parsed for this file. The original sheet may be empty or in an unexpected format.'}
              </p>
              {jobMetadata.crawler_file && isAccessible && (
                <Button
                  variant="outline"
                  className="mt-2"
                  onClick={handleOpenSheet}
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Original Sheet
                </Button>
              )}
              {!isAccessible && !user?.vip && (
                <Button
                  variant="default"
                  onClick={handleUpgradeClick}
                  className="mt-2 bg-amber-600 hover:bg-amber-700"
                >
                  <Crown className="mr-2 h-4 w-4" />
                  Upgrade to VIP Access
                </Button>
              )}
            </CardContent>
          </Card>
        )}
    </div>
  );
}
