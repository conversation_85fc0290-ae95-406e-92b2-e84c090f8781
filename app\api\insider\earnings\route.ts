import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const insiderEmail = searchParams.get('email');
    const insiderId = searchParams.get('id');

    if (!insiderEmail && !insiderId) {
      return NextResponse.json(
        { error: 'Insider email or ID is required' },
        { status: 400 }
      );
    }

    console.log('Fetching earnings for insider:', insiderEmail || insiderId);
    const supabase = createClient();

    // First check if the insider exists
    let query = supabase.from('insiders').select('id');
    
    if (insiderEmail) {
      query = query.eq('user_email', insiderEmail);
    } else {
      query = query.eq('id', insiderId);
    }
    
    const { data: insider, error: insiderError } = await query.single();

    if (insiderError) {
      console.error('Error finding insider:', insiderError);
      if (insiderError.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Insider not found' },
          { status: 404 }
        );
      }
      throw insiderError;
    }

    // Get the insider's earnings
    let earningsQuery = supabase
      .from('insiders')
      .select('earnings_available, earnings_paid, earnings_pending');
      
    if (insiderEmail) {
      earningsQuery = earningsQuery.eq('user_email', insiderEmail);
    } else {
      earningsQuery = earningsQuery.eq('id', insiderId);
    }
    
    const { data, error } = await earningsQuery.single();

    if (error) {
      console.error('Error fetching insider earnings:', error);
      throw error;
    }

    return NextResponse.json({
      earnings_available: data?.earnings_available || 0,
      earnings_paid: data?.earnings_paid || 0,
      earnings_pending: data?.earnings_pending || 0
    });
  } catch (error) {
    console.error('Unexpected error in earnings endpoint:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
