'use client';

import { useState, useEffect, MouseEvent } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Bookmark, Briefcase, MapPin, DollarSign, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useUser } from '@/hooks/useUser';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { StartupApplication } from '@/types/startups';
import { getStartupApplication } from '@/actions/job-market/startup-applications';
import { EasyApplyForm } from '@/components/job-market/easy-apply-form';
import { ExternalApplyButton } from '@/components/job-market/external-apply-button';
import { ReferralRequestButton } from './referral-request-button';

// Define the form schema
const easyApplyFormSchema = z
  .object({
    resume: z.string().optional(),
    useExistingResume: z.boolean().default(true),
    coverNote: z.string().optional(),
    yoe_internship: z.string().optional(),
    yoe_job: z.string().optional()
  })
  .refine(
    (data) => {
      // Validate that either useExistingResume is true OR resume is provided
      return (
        data.useExistingResume === true ||
        (data.resume && data.resume.trim() !== '')
      );
    },
    {
      message: 'Please provide a resume link or use your existing resume',
      path: ['resume']
    }
  );

type EasyApplyFormValues = z.infer<typeof easyApplyFormSchema>;

export interface JobCardProps {
  id: string;
  title: string;
  companyName: string;
  companyLogo?: string | null;
  location?: string | null;
  term?: string | null;
  salary?: string | null;
  sponsorship?: string | null;
  createdAt: string;
  detailsLink?: string;
  applyLink?: string;
  requestReferralLink?: string;
  showBookmark?: boolean;
  onEasyApply?: (jobId: string) => void;
  company?: string;
  hasApplied?: boolean;
  hideEasyApply?: boolean;
  hideViewDetails?: boolean;
  jobType?: 'startup' | 'publicFirm';
}

export default function JobCard({
  id,
  title,
  companyName,
  companyLogo,
  location,
  term,
  salary,
  sponsorship,
  createdAt,
  detailsLink,
  applyLink,
  requestReferralLink,
  showBookmark = true,
  onEasyApply,
  company,
  hasApplied: initialHasApplied = false,
  hideEasyApply = false,
  hideViewDetails = false,
  jobType = 'startup'
}: JobCardProps) {
  const [bookmarked, setBookmarked] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEasyApplyOpen, setIsEasyApplyOpen] = useState(false);
  const [hasApplied, setHasApplied] = useState(initialHasApplied);
  const [applicationData, setApplicationData] =
    useState<Partial<StartupApplication> | null>(null);
  const [isViewApplicationOpen, setIsViewApplicationOpen] = useState(false);

  const { user, loading } = useUser();
  const router = useRouter();

  // Create form with user data pre-filled
  const form = useForm<EasyApplyFormValues>({
    resolver: zodResolver(easyApplyFormSchema),
    defaultValues: {
      resume: '',
      useExistingResume: !!user?.resume_url,
      coverNote: '',
      yoe_internship: user?.yoe_internship || '',
      yoe_job: user?.yoe_job || ''
    }
  });

  // Update form when user data loads
  useEffect(() => {
    if (user) {
      form.reset({
        resume: '',
        useExistingResume: !!user.resume_url,
        coverNote: '',
        yoe_internship: user.yoe_internship || '',
        yoe_job: user.yoe_job || ''
      });
    }
  }, [user, form]);

  useEffect(() => {
    if (!loading && user) {
      // Check if job is bookmarked in localStorage
      const savedBookmarks = localStorage.getItem('bookmarkedJobs');
      if (savedBookmarks) {
        const bookmarks = JSON.parse(savedBookmarks);
        setBookmarked(bookmarks.includes(id));
      }

      // If we don't already know the application status from props,
      // check if user has already applied for this job
      if (!initialHasApplied) {
        const checkApplicationStatus = async () => {
          try {
            const existingApplication = await getStartupApplication(
              id,
              user.email
            );
            if (existingApplication) {
              setApplicationData(existingApplication);
              setHasApplied(true);
            }
          } catch (error) {
            console.error('Error checking application status:', error);
          }
        };

        checkApplicationStatus();
      }
    }
  }, [id, user, loading, initialHasApplied]);

  // Also update the hasApplied state when the prop changes
  useEffect(() => {
    setHasApplied(initialHasApplied);
  }, [initialHasApplied]);

  const handleAction = (
    e: React.MouseEvent,
    action: 'bookmark' | 'apply' | 'easyApply'
  ) => {
    // Stop propagation to prevent card click event from being triggered
    e.stopPropagation();

    if (loading) return; // Do nothing while loading

    if (!user && action !== 'apply') {
      // Redirect to sign in page if user is not authenticated
      router.push('/signin'); // Adjust this path as needed
      return;
    }

    switch (action) {
      case 'bookmark':
        toggleBookmark();
        break;
      case 'apply':
        if (applyLink) {
          // Instead of direct navigation, show dialog first
          setIsDialogOpen(true);
        }
        break;
      case 'easyApply':
        if (hasApplied) {
          // Open a view-only dialog
          setIsViewApplicationOpen(true);
        } else {
          setIsEasyApplyOpen(true);
        }
        if (onEasyApply) {
          onEasyApply(id);
        }
        break;
    }
  };

  const toggleBookmark = () => {
    const savedBookmarks = localStorage.getItem('bookmarkedJobs');
    let bookmarks: string[] = savedBookmarks ? JSON.parse(savedBookmarks) : [];

    if (bookmarked) {
      bookmarks = bookmarks.filter((jobId) => jobId !== id);
    } else {
      bookmarks.push(id);
    }

    localStorage.setItem('bookmarkedJobs', JSON.stringify(bookmarks));
    setBookmarked(!bookmarked);
  };

  const handleCardClick = (e: MouseEvent) => {
    // Do nothing - navigation disabled
    // You could add a console.log here for debugging
    e.preventDefault();
    return false;
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
    return `${Math.floor(diffDays / 365)} years ago`;
  };

  return (
    <>
      <Card
        className="p-6 hover:shadow-lg transition-shadow duration-300 cursor-pointer"
        onClick={handleCardClick}
      >
        <div className="flex gap-6">
          <div className="hidden sm:block flex-shrink-0">
            <div className="w-16 h-16 rounded-lg bg-gray-100 relative overflow-hidden">
              {companyLogo ? (
                <Image
                  src={
                    companyLogo.startsWith('http')
                      ? companyLogo
                      : `https://${companyLogo}`
                  }
                  alt={companyName}
                  width={64}
                  height={64}
                  sizes="(max-width: 768px) 64px, 64px"
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-2xl font-bold text-gray-400">
                  {companyName?.[0] || title?.[0] || '?'}
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 space-y-4">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <Badge variant="secondary" className="bg-[#36BA98] text-white">
                  {getTimeAgo(createdAt)}
                </Badge>
                <h2 className="text-xl font-semibold text-gray-900">{title}</h2>
                <p className="text-gray-600">{companyName}</p>
              </div>
              {/* {showBookmark && (
                <Button
                  variant="ghost"
                  size="icon"
                  className={`${bookmarked ? 'text-[#36BA98]' : 'text-gray-400 hover:text-[#36BA98]'}`}
                  onClick={(e) => handleAction(e, 'bookmark')}
                >
                  <Bookmark
                    className={`h-5 w-5 ${bookmarked ? 'fill-current' : ''}`}
                  />
                </Button>
              )} */}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-600">
              {term && (
                <div className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4 text-[#36BA98]" />
                  <span>{term}</span>
                </div>
              )}
              {location && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-[#36BA98]" />
                  <span>{location}</span>
                </div>
              )}
              {sponsorship && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-[#36BA98]" />
                  <span>{sponsorship}</span>
                </div>
              )}
              {salary && (
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-[#36BA98]" />
                  <span>{salary}</span>
                </div>
              )}
            </div>

            <div className="flex flex-wrap gap-2 justify-end">
              {!hideEasyApply && (
                <Button
                  className={`${
                    hasApplied
                      ? 'bg-gray-400 hover:bg-gray-500'
                      : 'bg-[#36BA98] hover:bg-[#2da885]'
                  } text-white`}
                  onClick={(e) => handleAction(e, 'easyApply')}
                  disabled={hasApplied}
                >
                  {hasApplied ? 'Already Applied' : 'Easy Apply'}
                </Button>
              )}

              {applyLink && (
                <ExternalApplyButton
                  jobId={id}
                  jobTitle={title}
                  jobLink={applyLink}
                  company={company}
                  companyName={companyName}
                  user={user}
                  hasApplied={hasApplied}
                  jobType={jobType}
                  onApplicationSuccess={(application) => {
                    setApplicationData(application);
                    setHasApplied(true);
                  }}
                />
              )}

              {requestReferralLink && (
                <ReferralRequestButton
                  jobId={id}
                  jobTitle={title}
                  applyLink={applyLink}
                  referralLink={requestReferralLink}
                  companyName={companyName}
                  hasApplied={hasApplied}
                  disabled={loading}
                  user={user}
                  jobType={jobType}
                  onApplicationSuccess={(application) => {
                    setApplicationData(application);
                    setHasApplied(true);
                  }}
                />
              )}

              {!hideViewDetails && (
                <Button
                  variant="outline"
                  className="border-[#36BA98] text-[#36BA98] hover:bg-[#36BA98] hover:text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    if (detailsLink) {
                      router.push(detailsLink);
                    }
                  }}
                >
                  View Details
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Easy Apply Dialog */}
      {!hideEasyApply && (
        <Dialog open={isEasyApplyOpen} onOpenChange={setIsEasyApplyOpen}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Easy Apply to {title}</DialogTitle>
              <DialogDescription>
                Fill out this form to apply for the position at {companyName}.
              </DialogDescription>
            </DialogHeader>

            <EasyApplyForm
              user={user}
              jobId={id}
              jobTitle={title}
              companyId={company}
              companyName={companyName}
              hasApplied={hasApplied}
              applicationData={applicationData}
              onSubmitSuccess={(application: Partial<StartupApplication>) => {
                setApplicationData(application);
                setHasApplied(true);
              }}
              onClose={() => setIsEasyApplyOpen(false)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* View Application Dialog */}
      {hasApplied && (
        <Dialog
          open={isViewApplicationOpen}
          onOpenChange={setIsViewApplicationOpen}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Your Application</DialogTitle>
              <DialogDescription>
                You applied for {title} on{' '}
                {applicationData?.creation_date
                  ? new Date(applicationData.creation_date).toLocaleDateString()
                  : 'recently'}
                .
              </DialogDescription>
            </DialogHeader>

            <DialogFooter>
              <Button onClick={() => setIsViewApplicationOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
}
