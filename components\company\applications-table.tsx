'use client';

import { useState, useEffect } from 'react';
import type { StartupApplication } from '@/types/startups';
import type { User } from '@/types/user';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle
} from '@/components/ui/alert-dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Eye, Trash2 } from 'lucide-react';

interface ApplicationsTableProps {
  applications: StartupApplication[];
  loading: boolean;
  onStatusChange: (id: string, status: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
  onViewCandidate: (application: StartupApplication, candidate: User) => void;
  totalApplications: number;
  currentPage: number;
  onPageChange: (page: number) => void;
}

export function ApplicationsTable({
  applications,
  loading,
  onStatusChange,
  onDelete,
  onViewCandidate,
  totalApplications,
  currentPage,
  onPageChange
}: ApplicationsTableProps) {
  const [selectedRows, setSelectedRows] = useState<string[]>([]);
  const [applicationToDelete, setApplicationToDelete] = useState<string | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);

  // Reset selected rows when applications change
  useEffect(() => {
    setSelectedRows([]);
  }, [applications]);

  const handleDelete = async () => {
    if (!applicationToDelete) return;

    setIsDeleting(true);
    try {
      await onDelete(applicationToDelete);
    } finally {
      setIsDeleting(false);
      setApplicationToDelete(null);
    }
  };

  const handleSelectRow = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedRows([...selectedRows, id]);
    } else {
      setSelectedRows(selectedRows.filter((rowId) => rowId !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedRows(applications.map((app) => app.id));
    } else {
      setSelectedRows([]);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  const totalDisplayedApplications = applications.length;
  const pageSize = 10;
  const totalPages = Math.ceil(totalDisplayedApplications / pageSize);

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox
                  checked={
                    applications.length > 0 &&
                    selectedRows.length === applications.length
                  }
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
              <TableHead>Job title</TableHead>
              <TableHead>Candidate</TableHead>
              <TableHead>Applied Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Operation</TableHead>
              <TableHead>Delete</TableHead>
              <TableHead>View</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  <div className="flex justify-center">
                    <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  </div>
                </TableCell>
              </TableRow>
            ) : applications.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="h-24 text-center">
                  No applications found. Try adjusting your filters.
                </TableCell>
              </TableRow>
            ) : (
              applications.map((application) => (
                <TableRow key={application.id}>
                  <TableCell>
                    <Checkbox
                      checked={selectedRows.includes(application.id)}
                      onCheckedChange={(checked) =>
                        handleSelectRow(application.id, !!checked)
                      }
                    />
                  </TableCell>
                  <TableCell className="font-medium">
                    {application.job_title || 'Unknown'}
                  </TableCell>
                  <TableCell>
                    {application.candidate_name || 'Unknown'}
                  </TableCell>
                  <TableCell>{formatDate(application.creation_date)}</TableCell>
                  <TableCell>
                    <Badge
                      className={`${
                        application.status === 'Pending'
                          ? 'bg-blue-500'
                          : application.status === 'Accepted'
                            ? 'bg-green-500'
                            : 'bg-red-500'
                      } text-white`}
                    >
                      {application.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() =>
                          onStatusChange(application.id, 'Rejected')
                        }
                      >
                        Reject
                      </Button>
                      <Button
                        variant="default"
                        size="sm"
                        className="bg-green-500 hover:bg-green-600"
                        onClick={() =>
                          onStatusChange(application.id, 'Accepted')
                        }
                      >
                        Accept
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setApplicationToDelete(application.id)}
                      className="text-destructive hover:bg-destructive/10 hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() =>
                        onViewCandidate(
                          application,
                          application.candidate as unknown as User
                        )
                      }
                      className="text-primary hover:bg-primary/10 hover:text-primary"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {totalPages > 1 && (
        <div className="mt-4 flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex h-9 items-center px-2">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      <AlertDialog
        open={!!applicationToDelete}
        onOpenChange={(open) => !open && setApplicationToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              application.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
