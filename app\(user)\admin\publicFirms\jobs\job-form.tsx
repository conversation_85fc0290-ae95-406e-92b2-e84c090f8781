"use client"

import { useState } from "react"
import { createPublicFirmJob, updatePublicFirmJob } from "@/actions/admin/public-firms/jobs"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { uploadImage } from "@/utils/supabase/storage/client"
import { CreatePublicFirmJobInput, PublicFirmJob } from "@/types/public-firms"

interface JobFormProps {
  onSuccess: () => void;
  initialData?: Partial<PublicFirmJob> | null;
}

export function JobForm({ onSuccess, initialData = null }: JobFormProps) {
  const [formData, setFormData] = useState<CreatePublicFirmJobInput>({
    company: initialData?.company || "",
    job_title: initialData?.job_title || "",
    term: initialData?.term || "",
    job_link: initialData?.job_link || "",
    number_of_openning: initialData?.number_of_openning || "",
    location: initialData?.location || "",
    expire_date: initialData?.expire_date || "",
    description: initialData?.description || "",
    logo: initialData?.logo || "",
    live: initialData?.live || "yes" // Default to live
    // applied_count: initialData?.applied_count || 0 // Default to 0
  })

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [newCompany, setNewCompany] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setLogoFile(e.target.files[0])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError("")

    try {
      let logoUrl = formData.logo

      if (logoFile) {
        const { imageUrl, error: uploadError } = await uploadImage({
          file: logoFile,
          bucket: "public-firm",
          folder: `companies/${formData.company}`,
        })

        if (uploadError) {
          throw new Error(`Logo upload failed: ${uploadError}`)
        }

        logoUrl = imageUrl
      }

      const jobData: CreatePublicFirmJobInput = {
        ...formData,
        logo: logoUrl
      }

      if (initialData?.id) {
        await updatePublicFirmJob(initialData.id, jobData)
      } else {
        await createPublicFirmJob(jobData)
      }

      onSuccess()
    } catch (err: any) {
      console.error("Error creating/updating job:", err)
      setError(err.message || "Failed to create/update job. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="max-h-[90vh] overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && <div className="p-3 bg-red-100 text-red-700 rounded-md">{error}</div>}

        <div className="space-y-2">
          <Label htmlFor="company">Company</Label>
          <Select
            value={formData.company || "Select company"}
            onValueChange={(value) => {
              if (value === "new") {
                setNewCompany(true)
                setFormData((prev) => ({ ...prev, company: "" }))
              } else {
                handleSelectChange("company", value)
                setNewCompany(false)
              }
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Meta">Meta</SelectItem>
              <SelectItem value="Google">Google</SelectItem>
              <SelectItem value="Amazon">Amazon</SelectItem>
              <SelectItem value="Apple">Apple</SelectItem>
              <SelectItem value="Microsoft">Microsoft</SelectItem>
              <SelectItem value="new">New company? Click Here</SelectItem>
            </SelectContent>
          </Select>

          {newCompany && (
            <div className="mt-2">
              <Input name="company" value={formData.company || ""} onChange={handleChange} placeholder="Enter company name" />
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="job_title">Job Title</Label>
          <Input id="job_title" name="job_title" value={formData.job_title || ""} onChange={handleChange} required />
        </div>

        <div className="space-y-2">
          <Label htmlFor="term">Term</Label>
          <Select value={formData.term || ""} onValueChange={(value) => handleSelectChange("term", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select term" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Full time">Full time</SelectItem>
              <SelectItem value="Part time">Part time</SelectItem>
              <SelectItem value="Contract">Contract</SelectItem>
              <SelectItem value="Internship">Internship</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="job_link">Job Link</Label>
          <Input
            id="job_link"
            name="job_link"
            value={formData.job_link || ''}
            onChange={handleChange}
            placeholder="https://..."
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="number_of_openning">Num. of Openings</Label>
            <Input
              id="number_of_openning"
              name="number_of_openning"
              value={formData.number_of_openning || ''}
              onChange={handleChange}
              type="number"
              min="1"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Location</Label>
            <Select value={formData.location || ''} onValueChange={(value) => handleSelectChange("location", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Remote">Remote</SelectItem>
                <SelectItem value="Hybrid">Hybrid</SelectItem>
                <SelectItem value="On-site">On-site</SelectItem>
                <SelectItem value="New York">New York</SelectItem>
                <SelectItem value="San Francisco">San Francisco</SelectItem>
                <SelectItem value="Seattle">Seattle</SelectItem>
                <SelectItem value="Austin">Austin</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="expire_date">Expire Date</Label>
          <Input id="expire_date" name="expire_date" value={formData.expire_date || ''} onChange={handleChange} type="date" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="logo">Logo</Label>
          <Input id="logo" name="logo" type="file" accept="image/*" onChange={handleLogoChange} />
          {formData.logo && !logoFile && (
            <div className="mt-2">
              <p className="text-sm text-gray-500">Current logo: {formData.logo}</p>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea id="description" name="description" value={formData.description || ''} onChange={handleChange} rows={5} />
        </div>

        <Button type="submit" className="w-full" disabled={isSubmitting}>
          {isSubmitting ? "Saving..." : "Save"}
        </Button>
      </form>
    </div>
  )
}