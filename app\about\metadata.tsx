import { Metadata } from 'next';

// Metadata configuration for about pages
export const metadata: Metadata = {
  title: {
    default: 'About InternUp - Empowering International Students',
    template: '%s | About InternUp'
  },
  description:
    "Learn about InternUp's mission to empower international students in their career journeys. Discover our story, values, and commitment to helping students find meaningful internships and jobs.",
  keywords: [
    'about internup',
    'company mission',
    'international students',
    'career platform',
    'student empowerment',
    'company values',
    'career support',
    'student success',
    'education technology',
    'career development'
  ],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    // url: siteConfig.url, // Uncomment when siteConfig is available
    title: 'About InternUp',
    description:
      'Our mission to empower international students in their career journeys',
    siteName: 'InternUp'
    // images: [
    //   {
    //     url: '/about-og.jpg',
    //     width: 1200,
    //     height: 630,
    //     alt: 'About InternUp - Our Story'
    //   }
    // ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About InternUp',
    description:
      'Our mission to empower international students in their career journeys'
    // images: ['/about-twitter.jpg']
  },
  alternates: {
    // canonical: `${siteConfig.url}/about` // Uncomment when siteConfig is available
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-snippet': -1,
      'max-image-preview': 'large',
      'max-video-preview': -1
    }
  },
  authors: [{ name: 'InternUp Team' }],
  category: 'company',
  classification: 'about us'
};
