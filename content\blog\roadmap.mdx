---
title: What's on the Roadmap for Hikari?
description: Discover the upcoming features and improvements planned for Hikari.
date: 2024-07-19
author: <PERSON>
---

## <PERSON><PERSON>'s Roadmap: What's Coming Next

As the creator of Hikari, I'm excited to share our plans for the future. We're constantly working to improve and expand our Next.js SaaS starter to meet the evolving needs of developers and businesses. Here's what we're currently focusing on:

### Localization and Internationalization

We understand the importance of reaching a global audience. That's why we're considering adding robust localization and internationalization support to Hikari. This feature's priority will largely depend on community demand. With this addition, you'll be able to:

- Easily translate your application into multiple languages
- Adapt your content for different regions and cultures
- Provide a seamless experience for users worldwide

We're eager to hear from our community about how important this feature is to you and your projects.

### Payment Integration

While Hikari currently supports Stripe for payment processing, we recognize the need for more flexibility in payment options. We're excited to announce that we're working on integrating LemonSqueezy as an additional payment provider. This expansion will offer several benefits:

- More choice for developers and businesses
- Simplified setup process for those who prefer LemonSqueezy
- Access to LemonSqueezy's unique features and pricing model

We believe this addition will make Hikari even more versatile and accessible to a wider range of projects. The integration is currently in development, and we're aiming to release it in the near future.

### Community Leaderboard

To foster engagement and showcase the success of projects built with Hikari, we're planning to implement a community leaderboard. This feature could take two potential forms:

- For SaaS projects: A leaderboard tracking revenue generated by applications built with Hikari
- For open-source projects: A leaderboard displaying GitHub stars earned by projects using Hikari

We're still in the early stages of planning this feature and would love to hear your thoughts on which approach would be most valuable to our community.

### Looking Forward

While these are our main focus areas, we're always open to new ideas and suggestions. The development of Hikari is a collaborative effort, and your input is crucial in shaping its future.

If you have thoughts on these planned features or ideas for other improvements, we encourage you to reach out. You can:

- Open an issue on our GitHub repository
- Join our community discussions
- Connect with us on social media

Thank you for being part of the Hikari community. Together, we're building the future of SaaS development!
