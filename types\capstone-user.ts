export type CapstoneUser = {
  id: string;
  capstone_project?: string | null;
  date_started?: Date | null;
  date_submitted?: Date | null;
  email?: string | null;
  end_date?: Date | null;
  file?: string | null;
  knowledge_planet_name?: string | null;
  name?: string | null;
  nda_link?: string | null;
  project_id?: string | null;
  request_endorsement?: string | null;
  reviewers_feedback?: string | null;
  scheduled_capstone_24_id?: string | null;
  scheduled_capstone_48_id?: string | null;
  scheduled_capstone_suspended_id?: string | null;
  status?: string | null;
  teammate?: string | null;
  user_email?: string | null;
  creation_date?: Date | null;
  modified_date?: Date | null;
  slug?: string | null;
  creator?: string | null;
};

export type CapstoneUserFormData = {
  id?: string;
  capstone_project?: string;
  date_started?: string;
  date_submitted?: string;
  email?: string;
  end_date?: string;
  file?: string;
  knowledge_planet_name?: string;
  name?: string;
  nda_link?: string;
  project_id?: string;
  request_endorsement?: string;
  reviewers_feedback?: string;
  scheduled_capstone_24_id?: string;
  scheduled_capstone_48_id?: string;
  scheduled_capstone_suspended_id?: string;
  status?: string;
  teammate?: string;
  user_email?: string;
};
