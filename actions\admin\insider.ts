// Insider related actions
import { createClient } from '@/utils/supabase/client';
// import { revalidatePath } from "next/cache"
import { deleteImage } from '@/utils/supabase/storage/client';
import { bubbleClient } from '@/utils/bubble/client';
import type {
  Insider,
  InsiderFormData,
  InsidersQueryParams
} from '@/types/insider';
import PaginatedResponse from '@/types/pagination';

// Function to generate a random password
function generateRandomPassword(length = 8) {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const special = '!@#$%^&*()_+';

  // Ensure at least one character from each category
  let password =
    lowercase.charAt(Math.floor(Math.random() * lowercase.length)) +
    uppercase.charAt(Math.floor(Math.random() * uppercase.length)) +
    numbers.charAt(Math.floor(Math.random() * numbers.length)) +
    special.charAt(Math.floor(Math.random() * special.length));

  // Add more random characters to reach desired length (12 characters total)
  const allChars = lowercase + uppercase + numbers + special;
  for (let i = 0; i < 8; i++) {
    password += allChars.charAt(Math.floor(Math.random() * allChars.length));
  }

  // Shuffle the password characters
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
}

// Function to generate a unique ID for insiders
function generateInsiderUID() {
  // Use UUID v4 instead of custom format
  return crypto.randomUUID();
}

// Function to generate a slug from the name
function generateSlug(name: string) {
  return name
    .toLowerCase()
    .replace(/[^\w\s]/gi, '')
    .replace(/\s+/g, '-');
}

export async function getInsiders({
  page = 1,
  searchTerm = '',
  userType = ''
}: InsidersQueryParams): Promise<PaginatedResponse<Insider>> {
  const supabase = createClient();
  const pageSize = 10;
  const startIndex = (page - 1) * pageSize;

  let query = supabase
    .from('insiders')
    .select('*, public_firm_companies (logo)', { count: 'exact' });

  if (searchTerm) {
    query = query.or(
      `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,public_firm.ilike.%${searchTerm}%`
    );
  }

  if (userType) {
    query = query.eq('account_status', userType);
  }

  const { data, error, count } = await query
    .order('creation_date', { ascending: false })
    .range(startIndex, startIndex + pageSize - 1);

  if (error) {
    console.error('Error fetching insiders:', error);
    throw new Error('Failed to fetch insiders');
  }

  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data as Insider[],
    totalPages,
    totalCount: count ?? 0
  };
}

export async function getInsider(id: string): Promise<Insider> {
  const supabase = createClient();

  // First, try to find the insider by checking the user_id against the given id
  // This helps when the id is a Bubble ID format like "1744730593152x451214929730949300"
  const { data: userIdData, error: userIdError } = await supabase
    .from('users')
    .select('id, email')
    .eq('id', id)
    .single();

  // If we found a user with this ID (Bubble ID format), look up the insider by email
  if (userIdData && userIdData.email) {
    const { data: insiderByEmail, error: emailError } = await supabase
      .from('insiders')
      .select('*')
      .eq('user_email', userIdData.email)
      .single();

    if (!emailError && insiderByEmail) {
      return insiderByEmail as Insider;
    }
  }

  // Fallback: try direct lookup by ID (for UUID format IDs)
  const { data, error } = await supabase
    .from('insiders')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching insider:', error, 'with ID:', id);
    throw new Error('Failed to fetch insider');
  }

  return data as Insider;
}

export async function createInsider(
  insiderData: InsiderFormData
): Promise<Insider> {
  const supabase = createClient();

  // Generate a slug from the name
  const slug = generateSlug(
    `${insiderData.first_name}-${insiderData.last_name || ''}`
  );

  const { data, error } = await supabase
    .from('insiders')
    .insert({
      ...insiderData,
      slug,
      account_status: 'Pending',
      creation_date: new Date().toISOString(),
      modified_date: new Date().toISOString()
    })
    .select();

  if (error) {
    console.error('Error creating insider:', error);
    throw new Error('Failed to create insider');
  }

  // revalidatePath("/startup/insiders")
  return data[0] as Insider;
}

export async function updateInsider(
  id: string,
  insiderData: Partial<InsiderFormData>
): Promise<Insider> {
  const supabase = createClient();

  const updatedData = {
    ...insiderData,
    modified_date: new Date().toISOString()
  };

  const { data, error } = await supabase
    .from('insiders')
    .update(updatedData)
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating insider:', error);
    throw new Error('Failed to update insider');
  }

  // revalidatePath("/startup/insiders")
  return data[0] as Insider;
}

export async function deleteInsider(id: string): Promise<{ success: boolean }> {
  const supabase = createClient();

  // First, get the insider to check if it has an avatar
  const { data: insider, error: fetchError } = await supabase
    .from('insiders')
    .select('avatar')
    .eq('id', id)
    .single();

  if (fetchError) {
    console.error('Error fetching insider for deletion:', fetchError);
    throw new Error('Failed to fetch insider for deletion');
  }

  // If the insider has an avatar, delete it from storage
  if (insider?.avatar) {
    await deleteImage(insider.avatar);
  }

  const { error } = await supabase.from('insiders').delete().eq('id', id);

  if (error) {
    console.error('Error deleting insider:', error);
    throw new Error('Failed to delete insider');
  }

  // revalidatePath("/startup/insiders")
  return { success: true };
}

export async function createInsiderWithAccount(
  insiderData: InsiderFormData
): Promise<{ insider: Insider; password: string }> {
  try {
    const supabase = createClient();

    // 1. Create a Bubble user account for the insider
    const password = generateRandomPassword();
    const bubbleResponse = await bubbleClient.signUp(
      insiderData.user_email,
      password,
      'Insider'
    );

    if (bubbleResponse.status !== 'success') {
      throw new Error(
        'Failed to create Bubble user: ' + bubbleResponse.message
      );
    }

    // Generate a UUID for the insider
    const insiderUID = generateInsiderUID();

    // Get the current user's email (creator)
    const {
      data: { user }
    } = await supabase.auth.getUser();
    const creatorEmail = user?.email || '';

    // 2. Create the insider record with the Bubble user ID and generated UID
    // Generate a slug from the name
    const slug = generateSlug(
      `${insiderData.first_name}-${insiderData.last_name || ''}`
    );
    //Temp Password-jo*y9a
    const { data, error } = await supabase
      .from('insiders')
      .insert({
        ...insiderData,
        slug,
        id: insiderUID,
        // bubble_user_id: bubbleResponse.response.user_id,
        account_status: 'Pending',
        creation_date: new Date().toISOString(),
        modified_date: new Date().toISOString(),
        creator: creatorEmail // Add creator's email
      })
      .select();

    if (error) {
      console.error('Error creating insider:', error);
      throw new Error('Failed to create insider');
    }

    const insider = data[0] as Insider;

    // 3. Create a Supabase user with user_type Insider
    const { error: supabaseError } = await supabase.from('users').insert({
      id: bubbleResponse.response.user_id,
      email: insiderData.user_email,
      user_type: 'Insider',
      verified: true // Auto-verify the insider account
    });

    if (supabaseError) {
      console.error('Error creating Supabase user:', supabaseError);
      throw new Error('Failed to create Supabase user');
    }

    // 4. Send welcome email with login details to the insider
    const insiderSubject =
      ' Invitation to Join InternUp as a Referee and Shape the Future of Talent';
    const insiderBody = `
Dear ${insiderData.first_name},

I hope this message finds you well. As a respected professional in your field, we'd like to invite you to become a Referee on InternUp, our job referral platform that connects promising talent with exceptional career opportunities—both at your firm and beyond.
Why Join InternUp as a Referee?
Amplify Great Talent: Help outstanding candidates in your network get noticed by reputable employers, including your own firm.
Influence Team Growth: As a Referee, you can make a real impact by guiding top performers toward roles where they'll thrive.
Support the Community: By sharing your insights and recommendations, you actively contribute to raising the bar in talent acquisition and workforce quality.
Reward for Your Future Career:
Your referrals are more than just a valuable contribution to the talent pool—they're an investment in your own professional growth. Each recommendation you make on InternUp is meticulously recorded, creating a visible track record of your ability to identify and champion great talent. In turn, you'll gain access to our global network of forward-thinking managers and strategic recruiters who can support your career ambitions whenever you're ready to make a move. Moreover, the credit you earn by guiding entry-level candidates can be redeemed for career coaching and access to top-tier talent and recruiters. Your efforts today will open doors to opportunities tomorrow.
How It Works:
Create Your Referee Profile: Use the secure link below to set up your Referee account.
Refer Top Candidates: When candidates request referrals, you can review their profiles and resumes without the need for a personal connection. If a candidate isn't the right fit, you may simply decline with no moral obligation. In fact, every decision—even a rejection—is valued as a contribution to our community's integrity.
Track Progress: Follow your referrals as they advance through the hiring process with top employers, and stay informed every step of the way.

Get Started:

Please visit the website to login as an insider and start managing job applications 
website link: https://www.internup.org/
Your account email: ${insiderData.user_email} 
Your Account password: ${password}  
[color=#111111][size=2][font=Verdana, Arial, Helvetica, sans-serif]Your expert judgment can shape the careers of talented individuals and strengthen the broader professional community. If you have any questions or need assistance, please reach out to <NAME_EMAIL>.[/font][/size][/color]
[color=#111111][size=2][font=Verdana, Arial, Helvetica, sans-serif]Thank you for considering this opportunity. We look forward to your participation as a Referee on InternUp.[/font][/size][/color]
[color=#111111][size=2][font=Verdana, Arial, Helvetica, sans-serif]Warm regards,[/font][/size][/color]
[color=#111111][size=2][font=Verdana, Arial, Helvetica, sans-serif]InternUp Team[/font][/size][/color]
    `;

    await bubbleClient.sendEmail(
      insiderData.user_email,
      insiderSubject,
      insiderBody
    );

    // 5. Send notification email to the admin/creator
    const adminSubject = 'An insider has been invited';
    const adminBody = `You have invited the insider with email ${insiderData.user_email} of firm ${insiderData.public_firm}`;

    if (creatorEmail) {
      await bubbleClient.sendEmail(creatorEmail, adminSubject, adminBody);
    }

    return { insider, password };
  } catch (error) {
    console.error('Error in createInsiderWithAccount:', error);
    throw error;
  }
}
