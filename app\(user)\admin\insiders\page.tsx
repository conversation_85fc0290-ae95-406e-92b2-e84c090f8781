'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  PencilIcon,
  XCircleIcon,
  Check,
  ChevronsUpDown,
  ExternalLink
} from 'lucide-react';
import {
  getInsiders,
  createInsider,
  deleteInsider,
  updateInsider,
  getInsider,
  createInsiderWithAccount
} from '@/actions/admin/insider';
import { getPublicFirmCompanies } from '@/actions/admin/public-firms/companies';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import { uploadImage } from '@/utils/supabase/storage/client';
import type { Insider, InsiderFormData } from '@/types/insider';
import type { PublicFirmCompany } from '@/types/public-firms';
import { cn } from '@/utils/cn';

export default function InsidersPage() {
  const [insiders, setInsiders] = useState<Insider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isCompanyComboboxOpen, setIsCompanyComboboxOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUserType, setSelectedUserType] = useState('');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [editingInsiderId, setEditingInsiderId] = useState<string | null>(null);
  const [publicFirmCompanies, setPublicFirmCompanies] = useState<
    PublicFirmCompany[]
  >([]);
  const [formData, setFormData] = useState<InsiderFormData>({
    first_name: '',
    last_name: '',
    position: '',
    public_firm: '',
    linkedin: '',
    services: [],
    user_email: '',
    start_up_company: ''
  });
  const [creationSuccess, setCreationSuccess] = useState<{
    email: string;
    password: string;
    insiderUID: string;
  } | null>(null);

  useEffect(() => {
    fetchInsiders();
    fetchPublicFirmCompanies();
  }, [currentPage, searchTerm, selectedUserType]);

  const fetchInsiders = async () => {
    setIsLoading(true);
    try {
      const { data, totalPages: pages } = await getInsiders({
        page: currentPage,
        searchTerm,
        userType: selectedUserType
      });
      setInsiders(data || []);
      setTotalPages(pages || 1);
    } catch (error) {
      console.error('Error fetching insiders:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchPublicFirmCompanies = async () => {
    try {
      const { data } = await getPublicFirmCompanies(1, 1000);
      setPublicFirmCompanies(data || []);
    } catch (error) {
      console.error('Error fetching public firm companies:', error);
    }
  };

  const handleCreateInsider = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      let avatarUrl = '';

      if (avatarFile) {
        const { imageUrl, error } = await uploadImage({
          file: avatarFile,
          bucket: 'avatars',
          folder: formData.user_email || `temp-${Date.now()}`
        });

        if (error) {
          throw new Error(`Failed to upload avatar: ${error}`);
        }

        avatarUrl = imageUrl;
      }

      const insiderData = {
        ...formData,
        avatar: avatarUrl
      };

      // Use the new function to create insider with account
      const { insider, password } = await createInsiderWithAccount(insiderData);

      // Store the credentials to show in success message
      setCreationSuccess({
        email: insider.user_email || '',
        password: password,
        insiderUID: insider.id || ''
      });

      // Don't close the dialog yet, show success message first
      resetForm();
      fetchInsiders();
    } catch (error) {
      console.error('Error creating insider:', error);
      // Display a more informative error message
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      alert(`Error creating insider: ${errorMessage}`);
    }
  };

  const handleEditInsider = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!editingInsiderId) return;

    try {
      let avatarUrl = formData.avatar || '';

      if (avatarFile) {
        const { imageUrl, error } = await uploadImage({
          file: avatarFile,
          bucket: 'avatars',
          folder: formData.user_email
        });

        if (error) {
          throw new Error(`Failed to upload avatar: ${error}`);
        }

        avatarUrl = imageUrl;
      }

      await updateInsider(editingInsiderId, {
        ...formData,
        avatar: avatarUrl
      });

      setIsEditDialogOpen(false);
      resetForm();
      fetchInsiders();
    } catch (error) {
      console.error('Error updating insider:', error);
    }
  };

  const handleDeleteInsider = async (id: string) => {
    if (confirm('Are you sure you want to delete this insider?')) {
      try {
        await deleteInsider(id);
        fetchInsiders();
      } catch (error) {
        console.error('Error deleting insider:', error);
      }
    }
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleEditClick = async (insider: Insider) => {
    setEditingInsiderId(insider.id);

    setFormData({
      first_name: insider.first_name || '',
      last_name: insider.last_name || '',
      position: insider.position || '',
      public_firm: insider.public_firm || '',
      linkedin: insider.linkedin || '',
      services: insider.services || [],
      user_email: insider.user_email || '',
      avatar: insider.avatar || '',
      avalable_time: insider.avalable_time || '',
      avalilable_day: insider.avalilable_day || '',
      worktype: insider.worktype || [],
      requirements: insider.requirements || '',
      start_up_company: insider.start_up_company || ''
    });

    if (insider.avatar) {
      setAvatarPreview(insider.avatar);
    }

    setIsEditDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      first_name: '',
      last_name: '',
      position: '',
      public_firm: '',
      linkedin: '',
      services: [],
      user_email: '',
      start_up_company: ''
    });
    setAvatarFile(null);
    setAvatarPreview(null);
    setEditingInsiderId(null);
  };

  const renderInsiderForm = (isEdit: boolean) => (
    <form
      onSubmit={isEdit ? handleEditInsider : handleCreateInsider}
      className="space-y-4"
    >
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">First Name</label>
          <Input
            value={formData.first_name}
            onChange={(e) =>
              setFormData({ ...formData, first_name: e.target.value })
            }
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Last Name</label>
          <Input
            value={formData.last_name}
            onChange={(e) =>
              setFormData({ ...formData, last_name: e.target.value })
            }
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Position</label>
          <Input
            value={formData.position}
            onChange={(e) =>
              setFormData({ ...formData, position: e.target.value })
            }
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">LinkedIn Profile</label>
          <Input
            value={formData.linkedin}
            onChange={(e) =>
              setFormData({ ...formData, linkedin: e.target.value })
            }
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Email</label>
          <Input
            type="email"
            value={formData.user_email}
            onChange={(e) =>
              setFormData({ ...formData, user_email: e.target.value })
            }
            required
          />
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Company</label>
          <Popover
            open={isCompanyComboboxOpen}
            onOpenChange={setIsCompanyComboboxOpen}
          >
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={isCompanyComboboxOpen}
                className="w-full justify-between"
                onClick={() => setIsCompanyComboboxOpen(!isCompanyComboboxOpen)}
              >
                {formData.public_firm
                  ? publicFirmCompanies.find(
                      (company) => company.name === formData.public_firm
                    )?.name || formData.public_firm
                  : 'Select or type company...'}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
              <Command>
                <CommandInput
                  placeholder="Search company or add new..."
                  value={formData.public_firm}
                  onValueChange={(value) => {
                    setFormData({ ...formData, public_firm: value });
                  }}
                />
                <CommandList>
                  <CommandEmpty>No company found. Type to add.</CommandEmpty>
                  <CommandGroup>
                    {publicFirmCompanies.map((company) => (
                      <CommandItem
                        key={company.id}
                        value={company.name || ''}
                        onSelect={(currentValue) => {
                          setFormData({
                            ...formData,
                            public_firm:
                              currentValue === formData.public_firm
                                ? ''
                                : currentValue
                          });
                          setIsCompanyComboboxOpen(false);
                        }}
                      >
                        <Check
                          className={cn(
                            'mr-2 h-4 w-4',
                            formData.public_firm === company.name
                              ? 'opacity-100'
                              : 'opacity-0'
                          )}
                        />
                        {company.name}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>

          {/* Company guidance message */}
          {formData.public_firm &&
            !publicFirmCompanies.some(
              (company) => company.name === formData.public_firm
            ) && (
              <div className="mt-2 space-y-2">
                <p className="text-amber-600 text-sm">
                  This company doesn't exist in our database. You should create
                  it first.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1"
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    window.open('/admin/publicFirms/companies', '_blank');
                  }}
                >
                  <ExternalLink className="h-4 w-4" />
                  Create New Company
                </Button>
              </div>
            )}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <DialogClose asChild>
          <Button type="button" variant="outline" onClick={resetForm}>
            Cancel
          </Button>
        </DialogClose>
        <Button type="submit">{isEdit ? 'Update' : 'Save'}</Button>
      </div>
    </form>
  );

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-2">Insiders</h1>
      <p className="text-gray-600 mb-6">Here are your latest sign ups</p>

      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-gray-500">
          {insiders.length > 0 ? insiders.length : 0} results
        </p>

        <div className="flex gap-4">
          <div className="flex gap-2">
            <Input
              placeholder="Search for a user"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[400px]"
            />
            <Select
              value={selectedUserType}
              onValueChange={setSelectedUserType}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select user type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All Users</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Accepted">Accepted</SelectItem>
                <SelectItem value="Rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => fetchInsiders()}>Search</Button>
          </div>
          <Button
            onClick={() => {
              resetForm();
              setCreationSuccess(null);
              setIsCreateDialogOpen(true);
            }}
          >
            Add Insider
          </Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 text-left font-medium text-gray-500">
                Avatar
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Company
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Name</th>
              <th className="p-3 text-left font-medium text-gray-500">
                Email address
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Last Active
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Referrals Approved
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Referrals Declined
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Pending referrals
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Delete
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Edit</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : insiders.length === 0 ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  No insiders found
                </td>
              </tr>
            ) : (
              insiders.map((insider) => (
                <tr key={insider.id} className="border-b">
                  <td className="p-3">
                    {insider.public_firm_companies?.logo ? (
                      <div className="w-10 h-10 relative">
                        <Image
                          src={
                            insider.public_firm_companies?.logo ||
                            '/placeholder.svg'
                          }
                          alt={`${insider.public_firm}'s logo`}
                          fill
                          className="object-cover "
                        />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-xl text-gray-500">
                          {insider.public_firm?.[0]?.toUpperCase() || '?'}
                        </span>
                      </div>
                    )}
                  </td>
                  <td className="p-3">{insider.public_firm || 'N/A'}</td>
                  <td className="p-3">
                    {insider.first_name} {insider.last_name}
                  </td>
                  <td className="p-3">{insider.user_email || 'N/A'}</td>
                  <td className="p-3">{insider.last_active || 'N/A'}</td>
                  <td className="p-3">
                    {insider.referral_approved_count || '0'}
                  </td>
                  <td className="p-3">
                    {insider.referral_decline_count || '0'}
                  </td>
                  <td className="p-3">0</td>
                  <td className="p-3">
                    <button
                      onClick={() => handleDeleteInsider(insider.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <XCircleIcon className="h-5 w-5" />
                    </button>
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleEditClick(insider)}
                      className="text-[#11] hover:text-green-700"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Create Insider Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create New Insider</DialogTitle>
          </DialogHeader>

          {creationSuccess ? (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="text-lg font-medium text-green-800 mb-2">
                Insider Created Successfully!
              </h3>
              <p className="mb-2">
                An account has been created and login details have been sent to
                the insider's email.
              </p>
              <div className="bg-white p-3 rounded border mb-3">
                <p>
                  <strong>Email:</strong> {creationSuccess.email}
                </p>
                <p>
                  <strong>Password:</strong> {creationSuccess.password}
                </p>
                <p>
                  <strong>Insider ID:</strong> {creationSuccess.insiderUID}
                </p>
              </div>
              <Button
                onClick={() => {
                  setCreationSuccess(null);
                  setIsCreateDialogOpen(false);
                }}
                className="w-full"
              >
                Close
              </Button>
            </div>
          ) : (
            renderInsiderForm(false)
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Insider Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Insider</DialogTitle>
          </DialogHeader>
          {renderInsiderForm(true)}
        </DialogContent>
      </Dialog>
    </div>
  );
}
