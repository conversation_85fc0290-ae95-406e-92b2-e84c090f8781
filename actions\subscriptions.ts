'use server';

import { createClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';
import { revalidatePath } from 'next/cache';
import {
  Subscription,
  CreateSubscriptionInput,
  UpdateSubscriptionInput,
  SubscriptionStatus
} from '@/types/subscription';

/**
 * Create a new subscription
 * @param data - CreateSubscriptionInput
 * @returns Subscription
 */
export async function createSubscription(
  data: CreateSubscriptionInput
): Promise<Subscription> {
  const supabase = createClient();

  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('id', data.owner_id)
    .single();

  if (userError) {
    throw new Error(userError.message);
  }

  try {
    // Prepare subscription data
    const now = new Date().toISOString();
    const subscriptionData = {
      id: uuidv4(),
      creator: user.id,
      owner_email: data.owner_email,
      owner_id: data.owner_id,
      plan: data.plan,
      status: data.status,
      start_date: data.start_date,
      end_date: data.end_date || null,
      cancelable_date: data.cancelable_date || null,
      subscription_id: data.subscription_id,
      creation_date: now,
      modified_date: now,
      slug: data.slug || `sub_${Date.now()}`,
      bubble_id: data.bubble_id || null
    };

    // Insert subscription
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .insert(subscriptionData)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    revalidatePath('/admin/subscriptions');
    return subscription;
  } catch (error) {
    throw error;
  }
}

/**
 * Get subscription by ID
 * @param id - Subscription ID
 * @returns Subscription
 */
export async function getSubscription(
  id: string
): Promise<Subscription | null> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Subscription not found
      }
      throw error;
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Get subscription by external subscription ID
 * @param subscriptionId - External subscription ID
 * @returns Subscription
 */
export async function getSubscriptionByExternalId(
  subscriptionId: string
): Promise<Subscription | null> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('subscription_id', subscriptionId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Subscription not found
      }
      throw error;
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * Get subscriptions for a user
 * @param userId - User ID
 * @returns Subscription[]
 */
export async function getUserSubscriptions(
  userId: string
): Promise<Subscription[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('owner_id', userId)
      .order('creation_date', { ascending: false });

    if (error) {
      throw error;
    }

    return data || [];
  } catch (error) {
    throw error;
  }
}

/**
 * Get active or trailing subscription for a user
 * @param userId - User ID
 * @returns Subscription
 */
export async function getActiveAndTrailingSubscription(
  userId: string
): Promise<Subscription | null> {
  const supabase = createClient();

  try {
    // First try with owner_id
    let { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('owner_id', userId)
      .in('status', ['active', 'trialing'])
      .order('creation_date', { ascending: false })
      .limit(1)
      .single();

    return data || null;
  } catch (error) {
    return null;
  }
}

/**
 * Update subscription
 * @param id - Subscription ID
 * @param data - UpdateSubscriptionInput
 * @returns Subscription
 */
export async function updateSubscription(
  id: string,
  data: UpdateSubscriptionInput
): Promise<Subscription> {
  const supabase = createClient();

  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('id', data.owner_id)
    .single();

  if (userError) {
    throw new Error(userError.message);
  }

  try {
    const updateData = {
      ...data,
      modified_date: new Date().toISOString()
    };

    const { data: updatedSubscription, error } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    revalidatePath('/admin/subscriptions');
    return updatedSubscription;
  } catch (error) {
    throw error;
  }
}

/**
 * Cancel subscription
 */
export async function cancelSubscription(
  userId: string
): Promise<Subscription> {
  const supabase = createClient();

  const { data: user, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single();

  if (userError) {
    throw new Error(userError.message);
  }

  const { data: subscription, error: fetchError } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('id', user.subscription_id)
    .single();

  if (fetchError) {
    throw fetchError;
  }

  // Check if the user is the owner or has admin rights
  const isAdmin = await checkIfUserIsAdmin(user.id);
  if (subscription.owner_id !== user.id && !isAdmin) {
    throw new Error('Not authorized to cancel this subscription');
  }

  // Update the subscription
  const now = new Date().toISOString();
  const { data: updatedSubscription, error } = await supabase
    .from('subscriptions')
    .update({
      status: 'canceled',
      end_date: now,
      modified_date: now
    })
    .eq('id', subscription.id)
    .select()
    .single();

  if (error) {
    throw error;
  }

  revalidatePath('/admin/subscriptions');
  revalidatePath(`/account`);

  return updatedSubscription;
}

/**
 * Check if user is an administrator
 * @param userId - User ID to check
 * @returns Boolean indicating if the user has admin rights
 */
async function checkIfUserIsAdmin(userId: string): Promise<boolean> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      return false;
    }

    return data?.role === 'admin';
  } catch (error) {
    return false;
  }
}

/**
 * List all subscriptions (admin only)
 */
export async function listAllSubscriptions(
  page: number = 1,
  limit: number = 20,
  status?: SubscriptionStatus
): Promise<{ data: Subscription[]; count: number }> {
  const supabase = createClient();

  // Get the current user's session
  const {
    data: { session }
  } = await supabase.auth.getSession();
  const user = session?.user;

  if (!user) {
    throw new Error('Authentication required');
  }

  const isAdmin = await checkIfUserIsAdmin(user.id);
  if (!isAdmin) {
    throw new Error('Admin access required');
  }

  const offset = (page - 1) * limit;

  try {
    let query = supabase.from('subscriptions').select('*', { count: 'exact' });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error, count } = await query
      .order('creation_date', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    return {
      data: data || [],
      count: count || 0
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Get current user's active or trailing subscription
 * @param userId - User ID
 * @returns Subscription
 */
export async function getCurrentUserSubscription(
  userId: string
): Promise<Subscription | null> {
  try {
    const supabase = createClient();

    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('subscription_id, is_subscription_active, vip')
      .eq('id', userId)
      .single();

    if (userError) {
      return null;
    }

    // If user is VIP, we can create a special VIP subscription object
    if (userData?.vip) {
      return {
        id: `vip-${userId}`,
        subscription_id: `vip-${userId}`,
        owner_id: userId,
        owner_email: '',
        status: 'active',
        plan: 'vip',
        start_date: new Date().toISOString(),
        end_date: null, // No end date for VIP
        creation_date: new Date().toISOString(),
        modified_date: new Date().toISOString(),
        cancelable_date: null,
        creator: userId,
        slug: `vip-${userId}`
      };
    }

    // If user has a subscription_id and is_subscription_active is true,
    // try to find the actual subscription to verify its status
    if (userData?.subscription_id && userData?.is_subscription_active) {
      // Try to find the subscription in the database
      const { data: subscription, error: subError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('id', userData.subscription_id)
        .single();

      // If found in database, verify it has active or trialing status and end_date is in future
      if (subscription) {
        const isValidStatus =
          subscription.status === 'active' ||
          subscription.status === 'trialing';

        // Check if end_date is null or in the future
        const now = new Date();
        let validEndDate = true;

        if (subscription.end_date) {
          const endDate = new Date(subscription.end_date);
          validEndDate = endDate > now;
        }

        if (isValidStatus && validEndDate) {
          return subscription;
        } else {
          return null;
        }
      }

      // If no subscription record found but user has active flag,
      // create a synthetic subscription with active status
      return {
        id: `synthetic-${userData.subscription_id}`,
        subscription_id: userData.subscription_id,
        owner_id: userId,
        owner_email: '',
        status: 'active', // Default to active for synthetic subscriptions
        plan: 'default',
        start_date: new Date().toISOString(),
        end_date: null, // No end date means it's active indefinitely
        creation_date: new Date().toISOString(),
        modified_date: new Date().toISOString(),
        cancelable_date: null,
        creator: userId,
        slug: `synthetic-${Date.now()}`
      };
    }

    // Final fallback: check for any active/trialing subscriptions by user ID with valid end_date
    const now = new Date();
    const { data: activeSubscription, error: activeError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('owner_id', userId)
      .in('status', ['active', 'trialing'])
      .or(`end_date.is.null,end_date.gt.${now.toISOString()}`) // End date is null or greater than now
      .order('creation_date', { ascending: false })
      .limit(1)
      .single();

    return activeSubscription;
  } catch (error) {
    return null;
  }
}

/**
 * Check if a user has an active or trailing subscription
 * @param userId - User ID to check
 * @returns Boolean indicating if user has active or trailing subscription
 */
export async function hasActiveSubscription(userId?: string): Promise<boolean> {
  try {
    if (!userId) {
      return false; // Not authenticated
    }

    const subscription = await getActiveAndTrailingSubscription(userId);
    return !!subscription;
  } catch (error) {
    return false;
  }
}
