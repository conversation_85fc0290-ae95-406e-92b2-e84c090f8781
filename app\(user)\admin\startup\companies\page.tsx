'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { PencilIcon, XCircleIcon, ExternalLink } from 'lucide-react';
import { getCompanies, deleteCompany } from '@/actions/admin/startup/companies';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import type { StartupCompany, StartupCompanyFormData } from '@/types/startups';
import { ScrollArea } from '@/components/ui/scroll-area';
import CompanyFormDialog from './company-form';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

export default function CompaniesPage() {
  const [companies, setCompanies] = useState<StartupCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [sortBy, setSortBy] = useState<'date' | 'name'>('date');
  const [editCompany, setEditCompany] = useState<StartupCompany | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [selectedCompanyDetails, setSelectedCompanyDetails] =
    useState<StartupCompany | null>(null);
  const [isCompanyDetailsOpen, setIsCompanyDetailsOpen] = useState(false);

  // Initialize empty form data
  const emptyFormData: StartupCompanyFormData = {
    name: '',
    description: '',
    industry: '',
    founder_school: '',
    contact_email: '',
    internal_email: '',
    founder_linkedin: '',
    company_crunchbase: '',
    company_linkedin: '',
    company_website: '',
    funding: '',
    legally_incorporated: '',
    logo_url: ''
  };

  useEffect(() => {
    fetchCompanies();
  }, [currentPage, sortBy, debouncedSearchTerm]); // Add debouncedSearchTerm as a dependency

  // Debounce search input to avoid too many API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      // Reset to first page when search term changes
      if (searchTerm !== debouncedSearchTerm && currentPage !== 1) {
        setCurrentPage(1);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]); // Remove currentPage dependency

  const fetchCompanies = async () => {
    setIsLoading(true);
    try {
      const {
        data,
        totalPages: pages,
        totalCount
      } = await getCompanies({
        page: currentPage,
        pageSize: 20,
        sortBy: sortBy === 'name' ? 'name' : 'created_at',
        sortOrder: sortBy === 'name' ? 'asc' : 'desc',
        searchTerm: debouncedSearchTerm
      });
      setCompanies(data || []);
      setTotalPages(pages || 1);
      setTotalCount(totalCount || 0);
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCompany = async (id: string) => {
    if (confirm('Are you sure you want to delete this company?')) {
      try {
        await deleteCompany(id);
        fetchCompanies();
      } catch (error) {
        console.error('Error deleting company:', error);
      }
    }
  };

  const handleEditCompany = (company: StartupCompany) => {
    setEditCompany(company);
    setIsDialogOpen(true);
  };

  const handleCompanySaved = () => {
    setIsDialogOpen(false);
    setEditCompany(null);
    fetchCompanies();
  };

  const toggleSort = () => {
    setSortBy(sortBy === 'date' ? 'name' : 'date');
  };

  const handleCompanyNameClick = (company: StartupCompany) => {
    setSelectedCompanyDetails(company);
    setIsCompanyDetailsOpen(true);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-2">Startup Companies</h1>
      <p className="text-gray-600 mb-6">
        Here are all posted startup companies
      </p>

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <p className="text-sm text-gray-500">
            Showing {companies.length} of {totalCount} companies
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative w-64">
            <Input
              type="text"
              placeholder="Search by company name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <Button variant="outline" onClick={toggleSort}>
            Sort by {sortBy === 'date' ? 'Date' : 'Name'}
          </Button>
          <Button
            onClick={() => {
              setEditCompany(null);
              setIsDialogOpen(true);
            }}
          >
            Create
          </Button>
        </div>
      </div>

      <ScrollArea className="h-[600px] w-full rounded-md border">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 sticky top-0">
              <th className="p-3 text-left font-medium text-gray-500">
                Avatar
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Company
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Applied
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Joined At
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Delete
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Edit</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={6} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : companies.length === 0 ? (
              <tr>
                <td colSpan={6} className="p-4 text-center">
                  No companies found
                </td>
              </tr>
            ) : (
              companies.map((company) => (
                <tr key={company.id} className="border-b">
                  <td className="p-3">
                    {company.logo_url ? (
                      <div className="w-10 h-10 relative">
                        <Image
                          src={company.logo_url || '/placeholder.svg'}
                          alt={company.name || 'Company logo'}
                          fill
                          className="object-contain rounded"
                        />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-xs text-gray-500">No logo</span>
                      </div>
                    )}
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleCompanyNameClick(company)}
                      className="text-gray-900 hover:text-[#118073] font-medium underline-offset-4 hover:underline flex items-center gap-2 group"
                    >
                      {company.name || 'N/A'}
                      <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </button>
                  </td>
                  <td className="p-3">{company.applications_count || 0}</td>
                  <td className="p-3">
                    {company.created_at
                      ? new Date(company.created_at).toLocaleDateString(
                          'en-US',
                          {
                            month: 'short',
                            day: '2-digit',
                            year: '2-digit'
                          }
                        )
                      : 'N/A'}
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleDeleteCompany(company.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <XCircleIcon className="h-5 w-5" />
                    </button>
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleEditCompany(company)}
                      className="text-[#118073] hover:text-green-700"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </ScrollArea>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1 || isLoading}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages || isLoading}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      <CompanyFormDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        company={editCompany}
        onSave={handleCompanySaved}
      />

      <Dialog
        open={isCompanyDetailsOpen}
        onOpenChange={setIsCompanyDetailsOpen}
      >
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader className="px-4 pt-4">
            <DialogTitle className="flex items-center gap-4">
              {selectedCompanyDetails?.logo_url && (
                <div className="w-12 h-12 relative">
                  <Image
                    src={selectedCompanyDetails.logo_url}
                    alt={selectedCompanyDetails.name || 'Company logo'}
                    fill
                    className="object-contain rounded"
                  />
                </div>
              )}
              <span className="text-xl font-semibold">
                {selectedCompanyDetails?.name || 'No name provided'}
              </span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 mt-4 overflow-y-auto px-4 pb-4 flex-grow">
            {selectedCompanyDetails?.description && (
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">
                  About
                </h3>
                <p className="text-gray-900">
                  {selectedCompanyDetails.description}
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              {selectedCompanyDetails?.industry && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">
                    Industry
                  </h4>
                  <p className="text-gray-900">
                    {selectedCompanyDetails.industry}
                  </p>
                </div>
              )}

              {selectedCompanyDetails?.founder_school && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">
                    Founder School
                  </h4>
                  <p className="text-gray-900">
                    {selectedCompanyDetails.founder_school}
                  </p>
                </div>
              )}

              {selectedCompanyDetails?.funding && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">
                    Funding
                  </h4>
                  <Badge
                    variant="secondary"
                    className="bg-purple-100 text-purple-700"
                  >
                    {selectedCompanyDetails.funding}
                  </Badge>
                </div>
              )}

              <div>
                <h4 className="text-sm font-medium text-gray-500 mb-1">
                  Legal Status
                </h4>
                <Badge
                  variant="secondary"
                  className={
                    selectedCompanyDetails?.legally_incorporated
                      ? 'bg-green-100 text-green-700'
                      : 'bg-yellow-100 text-yellow-700'
                  }
                >
                  {selectedCompanyDetails?.legally_incorporated
                    ? 'Incorporated'
                    : 'Not Incorporated'}
                </Badge>
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-500">
                Contact Information
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {selectedCompanyDetails?.contact_email && (
                  <div>
                    <h4 className="text-xs text-gray-500">Contact Email</h4>
                    <p className="text-gray-900">
                      {selectedCompanyDetails.contact_email}
                    </p>
                  </div>
                )}
                {selectedCompanyDetails?.internal_email && (
                  <div>
                    <h4 className="text-xs text-gray-500">Internal Email</h4>
                    <p className="text-gray-900">
                      {selectedCompanyDetails.internal_email}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="text-sm font-medium text-gray-500">
                External Links
              </h3>
              <div className="flex flex-wrap gap-4">
                {selectedCompanyDetails?.company_website && (
                  <a
                    href={selectedCompanyDetails.company_website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#118073] hover:underline text-sm flex items-center gap-1"
                  >
                    Website <ExternalLink className="h-3 w-3" />
                  </a>
                )}
                {selectedCompanyDetails?.company_linkedin && (
                  <a
                    href={selectedCompanyDetails.company_linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#118073] hover:underline text-sm flex items-center gap-1"
                  >
                    LinkedIn <ExternalLink className="h-3 w-3" />
                  </a>
                )}
                {selectedCompanyDetails?.company_crunchbase && (
                  <a
                    href={selectedCompanyDetails.company_crunchbase}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#118073] hover:underline text-sm flex items-center gap-1"
                  >
                    Crunchbase <ExternalLink className="h-3 w-3" />
                  </a>
                )}
                {selectedCompanyDetails?.founder_linkedin && (
                  <a
                    href={selectedCompanyDetails.founder_linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#118073] hover:underline text-sm flex items-center gap-1"
                  >
                    Founder LinkedIn <ExternalLink className="h-3 w-3" />
                  </a>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
