'use client';

import { usePathname } from 'next/navigation';

interface ProgressIndicatorProps {
  steps: string[];
  className?: string;
}

export function ProgressIndicator({
  steps,
  className = ''
}: ProgressIndicatorProps) {
  const pathname = usePathname();

  // Find current step index
  const currentStepIndex = steps.findIndex((step) => pathname === step);

  return (
    <div className={`flex items-center justify-center gap-2 py-4 ${className}`}>
      {steps.slice(0, -1).map((step, index) => (
        <div key={step} className="flex items-center">
          <div
            className={`w-10 h-2 rounded-full ${
              index <= currentStepIndex ? 'bg-[#36BA98]' : 'bg-gray-200'
            }`}
          />
          {index < steps.length - 2 && (
            <div className="w-2" /> // Gap between indicators
          )}
        </div>
      ))}
    </div>
  );
}
