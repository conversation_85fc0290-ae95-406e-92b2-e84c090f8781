"use client"

import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"

interface PaginationProps {
  currentPage: number
  totalPages: number
  baseUrl: string
  filters?: Record<string, string | undefined>
}

export default function Pagination({ currentPage, totalPages, baseUrl, filters = {} }: PaginationProps) {
  const router = useRouter()

  const buildUrl = (page: number) => {
    const params = new URLSearchParams()
    params.set("page", page.toString())

    // Add filters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, value)
      }
    })

    return `${baseUrl}?${params.toString()}`
  }

  const goToPage = (page: number) => {
    if (page < 1 || page > totalPages) return
    router.push(buildUrl(page))
  }

  return (
    <div className="flex items-center justify-between mt-8">
      <Button variant="outline" onClick={() => goToPage(currentPage - 1)} disabled={currentPage <= 1}>
        Previous Page
      </Button>

      <span className="text-sm text-gray-600">
        Page {currentPage} of {totalPages}
      </span>

      <Button variant="outline" onClick={() => goToPage(currentPage + 1)} disabled={currentPage >= totalPages}>
        Next Page
      </Button>
    </div>
  )
}

