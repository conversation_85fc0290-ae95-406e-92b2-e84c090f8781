'use client';

import { useState } from 'react';
import { CalendarIcon, Pencil } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { updateUserSubscription } from '@/actions/admin/users';
import { toast } from '@/components/ui/use-toast';

type SubscriptionType =
  | 'Daily Jobs Access'
  | 'VIP 1-Month Pass'
  | 'VIP 6-Month Pass'
  | 'VIP Full-Year Pass'
  | null;

interface EditSubscriptionDialogProps {
  userId: string;
  currentSubscriptionType: SubscriptionType;
  currentExpiryDate: string | null;
  onSuccess?: () => void;
}

export function EditSubscriptionDialog({
  userId,
  currentSubscriptionType,
  currentExpiryDate,
  onSuccess
}: EditSubscriptionDialogProps) {
  const [open, setOpen] = useState(false);
  const [subscriptionType, setSubscriptionType] = useState<SubscriptionType>(
    currentSubscriptionType
  );
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(
    currentExpiryDate ? new Date(currentExpiryDate) : undefined
  );
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    if (!subscriptionType) {
      toast({
        title: 'Error',
        description: 'Please select a subscription type',
        variant: 'destructive'
      });
      return;
    }

    if (!expiryDate) {
      toast({
        title: 'Error',
        description: 'Please select an expiry date',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsLoading(true);
      await updateUserSubscription(userId, {
        subscription_type: subscriptionType,
        subscription_expiry_date: expiryDate.toISOString(),
        vip: subscriptionType.includes('VIP')
      });

      toast({
        title: 'Success',
        description: 'Subscription updated successfully'
      });

      setOpen(false);
      if (onSuccess) onSuccess();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update subscription',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Pencil className="h-4 w-4 text-gray-500 hover:text-gray-700" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit User Subscription</DialogTitle>
          <DialogDescription>
            Update the subscription details for this user.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <label
              htmlFor="subscriptionType"
              className="text-right text-sm font-medium"
            >
              Plan
            </label>
            <Select
              value={subscriptionType || ''}
              onValueChange={(value) =>
                setSubscriptionType(value as SubscriptionType)
              }
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select subscription type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Daily Jobs Access">
                  Daily Jobs Access
                </SelectItem>
                <SelectItem value="VIP 1-Month Pass">
                  VIP 1-Month Pass
                </SelectItem>
                <SelectItem value="VIP 6-Month Pass">
                  VIP 6-Month Pass (Essential)
                </SelectItem>
                <SelectItem value="VIP Full-Year Pass">
                  VIP Full-Year Pass
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <label
              htmlFor="expiryDate"
              className="text-right text-sm font-medium"
            >
              Expiry Date
            </label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="expiryDate"
                  variant={'outline'}
                  className={cn(
                    'col-span-3 text-left font-normal',
                    !expiryDate && 'text-muted-foreground'
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {expiryDate ? (
                    format(expiryDate, 'PPP')
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={expiryDate}
                  onSelect={(date) => setExpiryDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="bg-[#36BA98] hover:bg-[#118073]"
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
