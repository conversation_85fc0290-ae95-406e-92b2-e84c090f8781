import { type JobDetailRow } from '@/types/google-sheets';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ExternalLink, Briefcase, MapPin, Tag, Monitor } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useUser } from '@/hooks/useUser';
import { trackJobClick } from '@/utils/job-tracking';

// Helper function to check if a job title contains "intern"
export const isInternPosition = (jobTitle: string | undefined): boolean => {
  return jobTitle ? jobTitle.toLowerCase().includes('intern') : false;
};

export type JobDetailDialogProps = {
  job: JobDetailRow | null;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  fileId?: string;
};

export const JobDetailDialog = ({
  job,
  isOpen,
  onOpenChange,
  fileId
}: JobDetailDialogProps) => {
  const { user } = useUser();

  if (!job) return null;

  const isIntern = isInternPosition(job.Job_title);

  // Handle apply button click with tracking
  const handleApplyClick = async () => {
    if (
      user &&
      fileId &&
      job.Apply_links &&
      job.Apply_links.startsWith('http')
    ) {
      try {
        // Track the application click
        await trackJobClick(fileId, user.id, {
          keyword: job.Keyword,
          jobTitle: job.Job_title,
          company: job.Company
        });
      } catch (error) {
        console.error('Error tracking job application click:', error);
        // Continue with the application even if tracking fails
      }

      // Open the application link in a new tab
      window.open(job.Apply_links, '_blank');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle
            className={cn('text-xl leading-tight', isIntern && 'text-blue-700')}
          >
            {job.Job_title || 'Job Details'}
          </DialogTitle>
          <DialogDescription>
            <>
              {job.Company && (
                <span className="font-medium">{job.Company}</span>
              )}
              {job.Company && job.Location && <span> · </span>}
              {job.Location && <span>{job.Location}</span>}
            </>
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="flex-1 pr-4 mt-2">
          <div className="space-y-6">
            {/* Section: Basic Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {job.Keyword && (
                <div className="flex items-start gap-2">
                  <Tag className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Keyword</p>
                    <p className="text-sm text-muted-foreground">
                      {job.Keyword}
                    </p>
                  </div>
                </div>
              )}

              {job.Company && (
                <div className="flex items-start gap-2">
                  <Briefcase className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Company</p>
                    <p className="text-sm text-muted-foreground">
                      {job.Company}
                    </p>
                  </div>
                </div>
              )}

              {job.Location && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">
                      {job.Location}
                    </p>
                  </div>
                </div>
              )}

              {job['Work Mode'] && (
                <div className="flex items-start gap-2">
                  <Monitor className="h-5 w-5 text-muted-foreground mt-0.5" />
                  <div>
                    <p className="text-sm font-medium">Work Mode</p>
                    <p className="text-sm text-muted-foreground">
                      {job['Work Mode']}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Section: Qualifications */}
            {job.Qualifications && (
              <div className="space-y-2 mt-2">
                <h3 className="text-sm font-medium">Qualifications</h3>
                <div className="text-sm text-muted-foreground p-3 bg-muted/40 rounded-md whitespace-pre-wrap">
                  {job.Qualifications}
                </div>
              </div>
            )}

            {/* Hidden/Extra fields section (only shown if values exist) */}
            {(job.Employment ||
              job.Authorization ||
              job['Years of Exp'] ||
              job.Job_post_time) && (
              <div className="space-y-2 pt-2 border-t">
                <h3 className="text-sm font-medium">Additional Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {job.Employment && (
                    <div className="flex items-start gap-2">
                      <div>
                        <p className="text-xs font-medium">Employment Type</p>
                        <p className="text-sm">{job.Employment}</p>
                      </div>
                    </div>
                  )}

                  {job.Authorization && (
                    <div className="flex items-start gap-2">
                      <div>
                        <p className="text-xs font-medium">Authorization</p>
                        <p className="text-sm">{job.Authorization}</p>
                      </div>
                    </div>
                  )}

                  {job['Years of Exp'] && (
                    <div className="flex items-start gap-2">
                      <div>
                        <p className="text-xs font-medium">
                          Years of Experience
                        </p>
                        <p className="text-sm">{job['Years of Exp']}</p>
                      </div>
                    </div>
                  )}

                  {job.Job_post_time && (
                    <div className="flex items-start gap-2">
                      <div>
                        <p className="text-xs font-medium">Posted</p>
                        <p className="text-sm">{job.Job_post_time}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>

        <DialogFooter className="mt-6 gap-2 sm:gap-0">
          {job.Apply_links && job.Apply_links.startsWith('http') ? (
            <Button
              onClick={handleApplyClick}
              className="flex-1 w-full sm:w-auto"
            >
              Apply for Job <ExternalLink className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button disabled className="flex-1 w-full sm:w-auto">
              No Apply Link Available
            </Button>
          )}
          <Button
            variant="outline"
            className="flex-1 w-full sm:w-auto"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
