'use client';
import CircularNavigation from '@/components/navigation';
import { marketingConfig } from '@/config/marketing';
import { useUser } from '@/hooks/useUser';
import { ActiveCapstoneProvider } from './industrial-project/components/active-capstone-provider';

export default function JobMarketLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useUser();
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <CircularNavigation
        items={marketingConfig.mainNav}
        user={user ? true : false}
      />

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ActiveCapstoneProvider user={user} loading={loading}>
          {children}
        </ActiveCapstoneProvider>
      </main>
    </div>
  );
}
