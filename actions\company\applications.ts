'use server';

import { revalidatePath } from 'next/cache';
import { createClient as createServerClient } from '@/utils/supabase/server';
import type { StartupApplication, PaginatedResponse } from '@/types/startups';
import type { User } from '@/types/user';

interface GetApplicationsParams {
  page: number;
  searchTerm?: string;
  companyId?: string;
  role?: string;
  pageSize?: number;
  status?: string;
}

export async function getCompanyApplications({
  page = 1,
  searchTerm = '',
  companyId,
  role,
  status,
  pageSize = 10
}: GetApplicationsParams): Promise<PaginatedResponse<StartupApplication>> {
  const supabase = createServerClient();
  const offset = (page - 1) * pageSize;

  // Build the query
  let query = supabase
    .from('startup_applications')
    .select(
      '*, candidate:users!startup_applications_candidate_fkey(id, first_name, last_name, email, avatar_url)',
      {
        count: 'exact'
      }
    );

  if (companyId) {
    query = query.eq('company_name', companyId);
  }

  // Only apply status filter when a specific status is requested
  if (status) {
    query = query.eq('status', status);
  }

  if (searchTerm) {
    query = query.or(
      `job_title.ilike.%${searchTerm}%,candidate.first_name.ilike.%${searchTerm}%,candidate.last_name.ilike.%${searchTerm}%`
    );
  }

  // Execute the query
  const {
    data: applications,
    error: applicationsError,
    count
  } = await query
    .order('creation_date', { ascending: false })
    .range(offset, offset + pageSize - 1);

  if (applicationsError) {
    console.error('Error fetching applications:', applicationsError);
    throw new Error(applicationsError.message);
  }

  // Get related job data
  const jobIds = applications.map((app) => app.job).filter(Boolean) as string[];
  let jobsMap: Record<string, any> = {};

  if (jobIds.length > 0) {
    const { data: jobs, error: jobsError } = await supabase
      .from('startup_jobs')
      .select('id, title, work_type, location')
      .in('id', jobIds);

    if (!jobsError && jobs) {
      jobsMap = jobs.reduce(
        (acc, job) => {
          acc[job.id] = job;
          return acc;
        },
        {} as Record<string, any>
      );
    }
  }

  // Apply role filtering if needed
  let result = applications;
  if (role && role !== 'all') {
    result = applications.filter((app) => {
      const jobData = jobsMap[app.job as string];
      return jobData && jobData.work_type === role;
    });
  }

  // Process the results
  const processedData = result.map((app) => {
    const candidate = app.candidate as any;
    const jobData = app.job ? jobsMap[app.job as string] : null;

    return {
      ...app,
      candidate_name: candidate
        ? `${candidate.first_name || ''} ${candidate.last_name || ''}`.trim()
        : 'Unknown',
      job_title: jobData ? jobData.title : app.job_title || 'Unknown',
      location: jobData ? jobData.location : null
    };
  });

  return {
    data: processedData,
    count,
    currentPage: page,
    totalPages: Math.ceil((count || 0) / pageSize)
  };
}

export async function getApplicationById(applicationId: string): Promise<{
  application: StartupApplication | null;
  candidate: User | null;
}> {
  const supabase = createServerClient();

  // Get the application with candidate data
  const { data: applicationData, error: applicationError } = await supabase
    .from('startup_applications')
    .select('*, candidate:users!startup_applications_candidate_fkey(*)')
    .eq('id', applicationId)
    .single();

  if (applicationError) {
    console.error('Error fetching application:', applicationError);
    return { application: null, candidate: null };
  }

  // Get the job data separately if job ID exists
  let jobData = null;
  if (applicationData.job) {
    const { data: job, error: jobError } = await supabase
      .from('startup_jobs')
      .select('id, title, work_type, location, description, required_skills')
      .eq('id', applicationData.job)
      .single();

    if (!jobError) {
      jobData = job;
    }
  }

  const application = {
    ...applicationData,
    candidate_name: applicationData.candidate
      ? `${applicationData.candidate.first_name || ''} ${applicationData.candidate.last_name || ''}`.trim()
      : 'Unknown',
    job_title: jobData ? jobData.title : applicationData.job_title || 'Unknown',
    location: jobData ? jobData.location : null,
    job: jobData // Add the job data
  } as StartupApplication;

  return {
    application,
    candidate: applicationData.candidate as User
  };
}

export async function updateApplicationStatus(
  applicationId: string,
  status: string
): Promise<void> {
  const supabase = createServerClient();

  const { error } = await supabase
    .from('startup_applications')
    .update({
      status,
      modified_date: new Date().toISOString()
    })
    .eq('id', applicationId);

  if (error) {
    console.error('Error updating application status:', error);
    throw new Error(error.message);
  }

  revalidatePath('/company/applications');
}

export async function deleteApplication(applicationId: string): Promise<void> {
  const supabase = createServerClient();

  const { error } = await supabase
    .from('startup_applications')
    .delete()
    .eq('id', applicationId);

  if (error) {
    console.error('Error deleting application:', error);
    throw new Error(error.message);
  }

  revalidatePath('/company/applications');
}

export async function getCandidateDetails(
  candidateId: string
): Promise<User | null> {
  const supabase = createServerClient();

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', candidateId)
    .single();

  if (error) {
    console.error('Error fetching candidate details:', error);
    return null;
  }

  return data as User;
}
