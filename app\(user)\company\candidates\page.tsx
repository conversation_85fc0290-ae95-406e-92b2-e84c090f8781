'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { getUserInitials } from '@/utils/string';
import { User } from '@/types/user';
import { createClient } from '@/utils/supabase/client';
import { jobTypes, workTypes } from '@/app/(user)/candidate/constants';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

const ITEMS_PER_PAGE = 9;
const MAX_BIO_LENGTH = 100;

export default function CandidatesPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [expandedBios, setExpandedBios] = useState<Record<string, boolean>>({});
  const searchParams = useSearchParams();
  const currentPage = Number(searchParams.get('page')) || 1;
  const { toast } = useToast();
  const supabase = createClient();

  // Get filters from URL
  const workPreferenceFilter = searchParams.get('work_preference') || '';
  const workTypeFilter = searchParams.get('work_type') || '';

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);

        // Calculate offset for pagination
        const offset = (currentPage - 1) * ITEMS_PER_PAGE;

        // Build the query
        let query = supabase
          .from('users')
          .select('*', { count: 'exact' })
          .eq('is_open_for_reverse_hiring', true);

        // Add work preference filter if selected
        if (workPreferenceFilter) {
          query = query.contains('work_preferences', [workPreferenceFilter]);
        }

        // Add work type filter if selected
        if (workTypeFilter) {
          query = query.contains('work_types', [workTypeFilter]);
        }

        // Add pagination and ordering
        const { data, error, count } = await query
          .range(offset, offset + ITEMS_PER_PAGE - 1)
          .order('created_at', { ascending: false });

        if (error) throw error;

        setUsers(data || []);
        setTotalCount(count || 0);
      } catch (error) {
        console.error('Error fetching users:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch candidates. Please try again later.',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [currentPage, workPreferenceFilter, workTypeFilter, toast, supabase]);

  const toggleBio = (userId: string) => {
    setExpandedBios((prev) => ({
      ...prev,
      [userId]: !prev[userId]
    }));
  };

  const updateFilter = (
    type: 'work_preference' | 'work_type',
    value: string
  ) => {
    const params = new URLSearchParams(searchParams);
    if (value) {
      params.set(type, value);
    } else {
      params.delete(type);
    }
    params.set('page', '1'); // Reset to first page when filter changes
    window.location.href = `?${params.toString()}`;
  };

  const clearFilters = () => {
    window.location.href = '?page=1';
  };

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4 mt-2" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">
          Candidates Open for Reverse Hiring
        </h1>
        <p className="text-sm text-muted-foreground">
          Showing {users.length} of {totalCount} candidates
        </p>
      </div>

      {/* Filters */}
      <div className="space-y-4">
        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[200px]">
            <label className="text-sm font-medium mb-2 block">
              Work Preference
            </label>
            <select
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              value={workPreferenceFilter}
              onChange={(e) => updateFilter('work_preference', e.target.value)}
            >
              <option value="">All Work Preferences</option>
              {jobTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>
          <div className="flex-1 min-w-[200px]">
            <label className="text-sm font-medium mb-2 block">Work Type</label>
            <select
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
              value={workTypeFilter}
              onChange={(e) => updateFilter('work_type', e.target.value)}
            >
              <option value="">All Work Types</option>
              {workTypes.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Active Filters */}
        {(workPreferenceFilter || workTypeFilter) && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              Active filters:
            </span>
            {workPreferenceFilter && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {workPreferenceFilter}
                <button
                  onClick={() => updateFilter('work_preference', '')}
                  className="hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {workTypeFilter && (
              <Badge variant="secondary" className="flex items-center gap-1">
                {workTypeFilter}
                <button
                  onClick={() => updateFilter('work_type', '')}
                  className="hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-sm text-muted-foreground hover:text-foreground"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>

      {users.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <p className="text-muted-foreground">
              No candidates found matching your filters.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {users.map((user) => (
              <Card key={user.id} className="flex flex-col">
                <CardHeader>
                  <div className="flex items-center gap-4">
                    <Avatar>
                      <AvatarImage src={user.avatar_url || undefined} />
                      <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">
                        {user.first_name} {user.last_name}
                      </CardTitle>
                      <p className="text-sm text-muted-foreground">
                        {user.location || 'Location not specified'}
                      </p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="flex-grow">
                  <div className="space-y-4">
                    {user.bio && (
                      <div className="space-y-2">
                        <p className="text-sm">
                          {expandedBios[user.id]
                            ? user.bio
                            : user.bio.length > MAX_BIO_LENGTH
                              ? `${user.bio.slice(0, MAX_BIO_LENGTH)}...`
                              : user.bio}
                        </p>
                        {user.bio.length > MAX_BIO_LENGTH && (
                          <Button
                            variant="link"
                            className="p-0 h-auto text-sm"
                            onClick={() => toggleBio(user.id)}
                          >
                            {expandedBios[user.id] ? 'Show Less' : 'Read More'}
                          </Button>
                        )}
                      </div>
                    )}
                    <div className="flex flex-wrap gap-2">
                      {user.linkedin_url && (
                        <a
                          href={user.linkedin_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                        >
                          LinkedIn
                        </a>
                      )}
                      {user.portfolio && (
                        <a
                          href={user.portfolio}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                        >
                          Portfolio
                        </a>
                      )}
                      {user.resume_url && (
                        <a
                          href={user.resume_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2"
                        >
                          Resume
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {totalPages > 1 && (
            <div className="flex justify-center gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  const params = new URLSearchParams(searchParams);
                  params.set('page', String(currentPage - 1));
                  window.location.href = `?${params.toString()}`;
                }}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="flex items-center px-4">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                onClick={() => {
                  const params = new URLSearchParams(searchParams);
                  params.set('page', String(currentPage + 1));
                  window.location.href = `?${params.toString()}`;
                }}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
