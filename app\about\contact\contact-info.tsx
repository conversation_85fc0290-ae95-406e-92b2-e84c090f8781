import { Mail, MapPin, Phone, Linkedin } from 'lucide-react';

export default function ContactInfo() {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="mb-6 text-3xl font-bold text-gray-900">Get in touch</h2>
        <p className="text-lg text-gray-700">
          We're here to help with any questions about our services, membership,
          or how we can support your career journey.
        </p>
      </div>

      <div className="space-y-6">
        <div className="flex items-start">
          <div className="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-[#118073]/10">
            <Mail className="w-6 h-6 text-[#118073]" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">Email</h3>
            <p className="mt-1 text-lg text-gray-700"><EMAIL></p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-[#118073]/10">
            <Phone className="w-6 h-6 text-[#118073]" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">Phone</h3>
            <p className="mt-1 text-lg text-gray-700">+****************</p>
          </div>
        </div>

        <div className="flex items-start">
          <div className="flex items-center justify-center w-12 h-12 mr-4 rounded-full bg-[#118073]/10">
            <MapPin className="w-6 h-6 text-[#118073]" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900">Office</h3>
            <p className="mt-1 text-lg text-gray-700">
              123 Innovation Way
              <br />
              San Francisco, CA 94107
            </p>
          </div>
        </div>
      </div>

      <div className="pt-8 mt-8 border-t border-gray-200">
        <h3 className="mb-4 text-xl font-bold text-gray-900">
          Connect with us
        </h3>
        <div className="flex space-x-4">
          <a
            href="#"
            className="flex items-center justify-center w-12 h-12 transition-colors rounded-full bg-[#118073]/10 hover:bg-[#118073]/20"
            aria-label="LinkedIn"
          >
            <span className="sr-only">LinkedIn</span>
            <Linkedin className="w-6 h-6 text-[#118073]" />
          </a>
        </div>
      </div>
    </div>
  );
}
