'use client';

import { useState, useEffect } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useUser } from '@/hooks/useUser';

// Component imports
import { PersonalDetails, WorkPreferences, Security } from './components';

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('personal');
  const [localUserData, setLocalUserData] = useState<any>(null);
  const { user, isOAuthUser } = useUser();

  useEffect(() => {
    async function checkOAuthStatus() {
      if (user) {
        setLocalUserData(user);
      }
    }

    checkOAuthStatus();
  }, [user]);

  return (
    <div className="container max-w-4xl py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your personal information and work preferences
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="personal">Personal Details</TabsTrigger>
          <TabsTrigger value="preferences">Work Preferences</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="personal">
          <PersonalDetails
            user={user}
            localUserData={localUserData}
            setLocalUserData={setLocalUserData}
          />
        </TabsContent>

        <TabsContent value="preferences">
          <WorkPreferences
            user={user}
            localUserData={localUserData}
            setLocalUserData={setLocalUserData}
          />
        </TabsContent>

        <TabsContent value="security">
          <Security user={user} isOAuthUser={isOAuthUser || false} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
