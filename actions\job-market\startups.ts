'use server';

import { createClient } from '@/utils/supabase/server';
import { LOCATION_OPTIONS } from '@/config/startup-job';
import type {
  StartupJob,
  JobFilters,
  PaginatedResponse
} from '@/types/startups';

/**
 * Get the total count of startup jobs with optional filters
 */
export async function getStartupJobsCount(
  filters?: JobFilters
): Promise<number> {
  try {
    const supabase = createClient();

    let query = supabase
      .from('startup_jobs')
      .select('*', { count: 'exact', head: true })
      .eq('live', true);

    // Apply filters only if they have actual values
    if (filters) {
      if (filters.title && filters.title.trim() !== '') {
        query = query.ilike('title', `%${filters.title}%`);
      }
      if (filters.location && filters.location !== 'all') {
        query = query.eq('location', filters.location);
      }
      if (filters.sponsorship && filters.sponsorship !== 'all') {
        query = query.eq('sponsorship', filters.sponsorship);
      }
      if (filters.term && filters.term !== 'all') {
        query = query.eq('term', filters.term);
      }
    }

    const { count, error } = await query;

    if (error) {
      console.error('Error getting startup jobs count:', error);
      throw new Error('Failed to get startup jobs count');
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getStartupJobsCount:', error);
    throw new Error('Failed to get startup jobs count');
  }
}

/**
 * Get all startup jobs with pagination and filtering
 */
export async function getAllStartupJobs(
  page = 1,
  pageSize = 10,
  dateFilter?: string,
  filters?: JobFilters
): Promise<PaginatedResponse<StartupJob>> {
  try {
    const supabase = createClient();
    const totalCount = await getStartupJobsCount(filters);

    const start = (page - 1) * pageSize;
    const end = start + pageSize - 1;

    let query = supabase
      .from('startup_jobs')
      .select(
        `
        *,
        startup_company:startup_companies!company_id(
          id,
          name,
          logo_url,
          description,
          company_linkedin,
          company_website,
          legally_incorporated,
          funding,
          internal_email,
          industry
        )
      `
      )
      .eq('live', true)
      .order('created_at', { ascending: false })
      .range(start, end);

    // Apply filters only if they have actual values
    if (filters) {
      if (filters.title && filters.title.trim() !== '') {
        query = query.ilike('title', `%${filters.title}%`);
      }
      if (filters.location && filters.location !== 'all') {
        query = query.eq('location', filters.location);
      }
      if (filters.sponsorship && filters.sponsorship !== 'all') {
        query = query.eq('sponsorship', filters.sponsorship);
      }
      if (filters.term && filters.term !== 'all') {
        query = query.eq('term', filters.term);
      }
    }

    if (dateFilter) {
      const filterDate = new Date(dateFilter);
      query = query.gte('created_at', filterDate.toISOString());
    }

    const { data: jobs, error } = await query;

    if (error) {
      console.error('Error fetching startup jobs:', error);
      throw new Error('Failed to fetch startup jobs');
    }

    // Process jobs but maintain the company data that was successfully joined
    const processedJobs = jobs?.map((job) => ({
      ...job,
      startup_company: job.startup_company || {
        id: job.company_id,
        name: job.company_description || 'Unknown Company',
        logo_url: null,
        description: job.company_description || null,
        company_linkedin: null,
        company_website: null,
        legally_incorporated: null,
        funding: null,
        internal_email: null,
        industry: null
      }
    })) as StartupJob[];

    return {
      data: processedJobs || [],
      count: totalCount,
      currentPage: page,
      totalPages: Math.ceil(totalCount / pageSize)
    };
  } catch (error) {
    console.error('Error in getAllStartupJobs:', error);
    throw new Error('Failed to fetch startup jobs');
  }
}

/**
 * Get filter options for startup jobs
 */
export async function getStartupFilterOptions() {
  try {
    const supabase = createClient();

    const { data: jobs, error } = await supabase
      .from('startup_jobs')
      .select('sponsorship, term')
      .eq('live', true);

    if (error) {
      console.error('Error fetching filter options:', error);
      throw new Error('Failed to fetch filter options');
    }

    // Use predefined locations from config file
    const locations = Array.from(LOCATION_OPTIONS);

    // Get unique values for sponsorships and terms
    const sponsorships = Array.from(
      new Set(jobs?.map((job) => job.sponsorship).filter(Boolean))
    );
    const terms = Array.from(
      new Set(jobs?.map((job) => job.term).filter(Boolean))
    );

    return {
      locations,
      sponsorships,
      terms
    };
  } catch (error) {
    console.error('Error in getStartupFilterOptions:', error);
    throw new Error('Failed to fetch filter options');
  }
}

/**
 * Get a specific startup job by ID
 */
export async function getStartupJobById(id: string): Promise<StartupJob> {
  try {
    const supabase = createClient();

    const { data: job, error } = await supabase
      .from('startup_jobs')
      .select(
        `
        *,
        startup_company:startup_companies!company_id(
          id,
          name,
          logo_url,
          description,
          company_linkedin,
          company_website,
          legally_incorporated,
          funding,
          internal_email,
          industry
        )
      `
      )
      .eq('id', id)
      .eq('live', true)
      .maybeSingle();

    if (error) {
      console.error('Database query error:', error);
      throw new Error('Failed to fetch job details');
    }

    if (!job) {
      throw new Error('Job not found');
    }

    // Process and return the job with proper fallback
    const processedJob = {
      ...job,
      startup_company: job.startup_company || {
        id: job.company_id,
        name: job.company_description || 'Unknown Company',
        logo_url: null,
        description: job.company_description || null,
        company_linkedin: null,
        company_website: null,
        legally_incorporated: null,
        funding: null,
        internal_email: null,
        industry: null
      }
    } as StartupJob;

    return processedJob;
  } catch (error) {
    console.error('Error in getStartupJobById:', error);
    throw new Error('Failed to fetch job details');
  }
}

/**
 * Get similar jobs based on a job ID
 */
export async function getSimilarJobs(
  jobId: string,
  limit = 3
): Promise<StartupJob[]> {
  try {
    const supabase = createClient();
    const job = await getStartupJobById(jobId);

    const { data: similarJobs, error } = await supabase
      .from('startup_jobs')
      .select(
        `
        *,
        startup_company:startup_companies!company_id (
          id,
          name,
          logo_url
        )
      `
      )
      .eq('live', true)
      .eq('term', job.term)
      .neq('id', jobId)
      .limit(limit);

    if (error) {
      console.error('Error fetching similar jobs:', error);
      throw new Error('Failed to fetch similar jobs');
    }

    return similarJobs as StartupJob[];
  } catch (error) {
    console.error('Error in getSimilarJobs:', error);
    return [];
  }
}

/**
 * Get recent jobs by company
 */
export async function getRecentJobsByCompany(
  companyId: string,
  currentJobId: string,
  limit = 2
): Promise<StartupJob[]> {
  try {
    const supabase = createClient();

    const { data: jobs, error } = await supabase
      .from('startup_jobs')
      .select('*')
      .eq('company_id', companyId)
      .eq('live', true)
      .neq('id', currentJobId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching company jobs:', error);
      throw new Error('Failed to fetch company jobs');
    }

    return jobs as StartupJob[];
  } catch (error) {
    console.error('Error in getRecentJobsByCompany:', error);
    return [];
  }
}
