'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Calendar, CreditCard, CheckCircle } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { createStripePortal } from '@/utils/stripe/server';
import { useRouter } from 'next/navigation';
import { Subscription, SubscriptionStatus } from '@/types/subscription';

// Define a type for the possible return values from createStripePortal
type StripePortalResult = string | { errorRedirect?: string };

interface PricingPlan {
  name: string;
  price: string;
  period: 'monthly' | 'annual' | 'semi-annual' | 'one-time';
  description: string;
  features: readonly string[];
  ctaText: string;
  popular?: boolean;
}

const pricingPlans: readonly PricingPlan[] = [
  {
    name: 'Essential Access Pass',
    price: '$59.99',
    period: 'semi-annual',
    description: 'For active job seekers',
    features: [
      'Job alerts from 100+ platforms',
      'Expert career tips from 6+ years experience',
      'Direct apply—90% faster',
      'H1B-focused job filters'
    ],
    ctaText: 'Subscribe'
  },
  {
    name: 'Pro Monthly Pass',
    price: '$39.99',
    period: 'monthly',
    description: 'For ambitious professionals',
    features: [
      'Application guidance',
      'Priority referrals',
      'Internship & OPT support',
      'All Semi-Annual Pass features'
    ],
    ctaText: 'Subscribe'
  },
  {
    name: 'Elite Semi-Annual Pass',
    price: '$129.99',
    period: 'semi-annual',
    description: 'For growth-focused professionals',
    features: [
      'FAANG and startup referrals',
      'Weekly curated job lists for diverse roles',
      'AI-powered career tools',
      'Daily jobs from 100+ platforms'
    ],
    ctaText: 'Subscribe'
  },
  {
    name: 'Ultimate Annual Pass',
    price: '$199.99',
    period: 'annual',
    description: 'For committed career builders',
    features: [
      'All Monthly Pass features',
      'Boost LinkedIn connections',
      'Real-world capstone projects',
      'Exclusive startup jobs'
    ],
    ctaText: 'Subscribe',
    popular: true
  }
] as const;

interface SubscriptionDetailsProps {
  subscription: Subscription;
  userId: string;
}

export function SubscriptionDetails({
  subscription,
  userId
}: SubscriptionDetailsProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getPlanDetails = (planName: string): PricingPlan | undefined => {
    return pricingPlans.find((plan) => plan.name === planName);
  };

  const planDetails = getPlanDetails(subscription.plan);

  const handleManageSubscription = async () => {
    setIsLoading(true);
    try {
      const result: StripePortalResult = await createStripePortal(
        window.location.pathname,
        userId,
        subscription.owner_email
      );

      // The result is a URL string that we can redirect to
      if (typeof result === 'string') {
        window.location.href = result;
      } else if (result && typeof result === 'object') {
        // Use explicit type assertion when accessing errorRedirect
        const errorRedirectUrl = (result as { errorRedirect?: string })
          .errorRedirect;
        if (errorRedirectUrl) {
          window.location.href = errorRedirectUrl;
        } else {
          throw new Error('Invalid response from subscription portal');
        }
      } else {
        throw new Error('Invalid response from subscription portal');
      }
    } catch (error) {
      console.error('Subscription management error:', error);
      toast({
        title: 'Error',
        description: 'Could not process your request. Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusBadge = (status: SubscriptionStatus) => {
    switch (status) {
      case 'active':
        return (
          <Badge
            variant="outline"
            className="bg-green-50 text-green-700 border-green-200"
          >
            Active
          </Badge>
        );
      case 'trialing':
        return (
          <Badge
            variant="outline"
            className="bg-blue-50 text-blue-700 border-blue-200"
          >
            Trial
          </Badge>
        );
      case 'canceled':
        return (
          <Badge
            variant="outline"
            className="bg-red-50 text-red-700 border-red-200"
          >
            Canceled
          </Badge>
        );
      case 'past_due':
        return (
          <Badge
            variant="outline"
            className="bg-orange-50 text-orange-700 border-orange-200"
          >
            Past Due
          </Badge>
        );
      default:
        return (
          <Badge
            variant="outline"
            className="bg-gray-50 text-gray-700 border-gray-200"
          >
            {status}
          </Badge>
        );
    }
  };

  const getBillingPeriod = () => {
    if (!planDetails) return 'N/A';

    switch (planDetails.period) {
      case 'monthly':
        return 'Monthly';
      case 'annual':
        return 'Annual';
      case 'semi-annual':
        return 'Semi-Annual';
      case 'one-time':
        return 'One-time payment';
      default:
        return 'N/A';
    }
  };

  const isSubscriptionEnding =
    subscription.end_date && new Date(subscription.end_date) > new Date();

  return (
    <Card className="border-primary/20 shadow-md">
      <CardHeader className="pb-4">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-2xl font-bold text-primary">
              {subscription.plan}
            </h3>
            <div className="flex items-center mt-1 space-x-2">
              {getStatusBadge(subscription.status)}
              <span className="text-lg font-semibold">
                {planDetails?.price}
                {planDetails?.period !== 'one-time' && (
                  <span className="text-sm font-normal text-gray-500">
                    /{planDetails?.period.replace('-', ' ')}
                  </span>
                )}
              </span>
            </div>
          </div>
          <CheckCircle className="h-8 w-8 text-primary" />
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-gray-500" />
            <div>
              <p className="text-sm text-gray-500">Current period</p>
              <p className="font-medium">
                {formatDate(subscription.start_date)} -{' '}
                {formatDate(subscription.end_date)}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5 text-gray-500" />
            <div>
              <p className="text-sm text-gray-500">Billing</p>
              <p className="font-medium">{getBillingPeriod()}</p>
            </div>
          </div>
        </div>

        {isSubscriptionEnding && (
          <div className="mt-4 p-3 bg-yellow-50 rounded-md border border-yellow-200">
            <p className="text-yellow-800">
              Your subscription will end on {formatDate(subscription.end_date)}.
              You can reactivate it from the subscription management page.
            </p>
          </div>
        )}

        <div className="mt-4">
          <h4 className="font-medium mb-2">Subscription Details</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="text-gray-500">Created:</div>
            <div>{formatDate(subscription.creation_date)}</div>
            {subscription.cancelable_date && (
              <>
                <div className="text-gray-500">Cancelable Date:</div>
                <div>{formatDate(subscription.cancelable_date)}</div>
              </>
            )}
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-2">
        <Button
          onClick={handleManageSubscription}
          className="w-full bg-primary hover:bg-primary/90"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            'Manage Subscription'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
