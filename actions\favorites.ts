'use server';

import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';

interface UserBookmarks {
  bookmarked_startup_jobs: string[] | null;
  bookmarked_pf_jobs: string[] | null;
}

export async function getFavorites() {
  try {
    const supabase = createClient();

    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Session error:', sessionError);
      return { startupJobs: [], publicFirmJobs: [], isAuthenticated: false };
    }

    if (!session?.user) {
      return { startupJobs: [], publicFirmJobs: [], isAuthenticated: false };
    }

    const { data, error } = await supabase
      .from('users')
      .select(
        `
        bookmarked_startup_jobs,
        bookmarked_pf_jobs
      `
      )
      .eq('id', session.user.id)
      .single();

    if (error) {
      console.error('Error fetching favorites:', error);
      return { startupJobs: [], publicFirmJobs: [], isAuthenticated: true };
    }

    const userBookmarks = data as UserBookmarks;

    // Fetch the actual job details for both types
    const startupJobsPromise = userBookmarks.bookmarked_startup_jobs?.length
      ? supabase
          .from('startup_jobs')
          .select('*')
          .in('id', userBookmarks.bookmarked_startup_jobs)
      : Promise.resolve({ data: [] });

    const publicFirmJobsPromise = userBookmarks.bookmarked_pf_jobs?.length
      ? supabase
          .from('public_firm_jobs')
          .select('*')
          .in('id', userBookmarks.bookmarked_pf_jobs)
      : Promise.resolve({ data: [] });

    const [startupJobs, publicFirmJobs] = await Promise.all([
      startupJobsPromise,
      publicFirmJobsPromise
    ]);

    return {
      startupJobs: startupJobs.data || [],
      publicFirmJobs: publicFirmJobs.data || [],
      isAuthenticated: true
    };
  } catch (error) {
    console.error('Error in getFavorites:', error);
    return { startupJobs: [], publicFirmJobs: [], isAuthenticated: false };
  }
}

export async function removeFromFavorites(
  jobId: string,
  type: 'startup' | 'public-firm'
) {
  try {
    const supabase = createClient();

    const {
      data: { session },
      error: sessionError
    } = await supabase.auth.getSession();

    if (sessionError || !session?.user) {
      throw new Error('Authentication required');
    }

    // Get current bookmarks
    const { data: userData, error: fetchError } = await supabase
      .from('users')
      .select<string, UserBookmarks>(
        type === 'startup' ? 'bookmarked_startup_jobs' : 'bookmarked_pf_jobs'
      )
      .eq('id', session.user.id)
      .single();

    if (fetchError) throw new Error('Failed to fetch user data');
    if (!userData) throw new Error('User data not found');

    // Get the correct bookmark array based on type
    const currentBookmarks =
      type === 'startup'
        ? userData.bookmarked_startup_jobs || []
        : userData.bookmarked_pf_jobs || [];

    // Remove the job ID from the array
    const updatedBookmarks = currentBookmarks.filter((id) => id !== jobId);

    // Update the database
    const { error: updateError } = await supabase
      .from('users')
      .update({
        [type === 'startup' ? 'bookmarked_startup_jobs' : 'bookmarked_pf_jobs']:
          updatedBookmarks
      })
      .eq('id', session.user.id);

    if (updateError) throw new Error('Failed to update favorites');

    revalidatePath('/candidate/favorites');
    return { success: true };
  } catch (error) {
    console.error('Error in removeFromFavorites:', error);
    throw error;
  }
}
