'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Lock, Loader2, AlertCircle, Eye, EyeOff } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import { createClient } from '@/utils/supabase/client';

const passwordSchema = z.object({
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[0-9]/, 'Must contain a number')
    .regex(/[A-Z]/, 'Must contain an uppercase letter')
});

type PasswordFormValues = z.infer<typeof passwordSchema>;

interface SecurityProps {
  user: any;
  isOAuthUser: boolean;
}

export function Security({ user, isOAuthUser }: SecurityProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const [showPassword, setShowPassword] = useState(false);
  const supabase = createClient();

  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: { password: '' },
    mode: 'all'
  });

  const onSubmit = async (values: PasswordFormValues) => {
    setIsSubmitting(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: values.password
      });

      if (error) throw error;

      toast({
        title: 'Password Updated',
        description: 'You can now use your new password to sign in.'
      });

      passwordForm.reset();
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description:
          error instanceof Error ? error.message : 'Password update failed.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Security Settings</CardTitle>
            <CardDescription>
              Manage your account security and credentials
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isOAuthUser ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>OAuth Account</AlertTitle>
            <AlertDescription>
              Your account is managed through an external provider. Password
              reset is not available.
            </AlertDescription>
          </Alert>
        ) : (
          <Form {...passwordForm}>
            <form
              onSubmit={passwordForm.handleSubmit(onSubmit)}
              className="space-y-6"
            >
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Reset Password</h3>
                <FormField
                  control={passwordForm.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            placeholder="Enter your new password"
                            autoComplete="new-password"
                            value={field.value}
                            onChange={field.onChange}
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </button>
                        </div>
                      </FormControl>
                      <div className="text-sm mt-2 space-y-1 text-muted-foreground">
                        <p
                          className={
                            field.value.length >= 6 ? 'text-green-500' : ''
                          }
                        >
                          • At least 6 characters
                        </p>
                        <p
                          className={
                            /[0-9]/.test(field.value) ? 'text-green-500' : ''
                          }
                        >
                          • At least one number
                        </p>
                        <p
                          className={
                            /[A-Z]/.test(field.value) ? 'text-green-500' : ''
                          }
                        >
                          • At least one capital letter
                        </p>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="flex justify-end">
                <Button
                  type="submit"
                  className="bg-[#118073] hover:bg-[#118073]/90"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    <>
                      <Lock className="mr-2 h-4 w-4" />
                      Update Password
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
}
