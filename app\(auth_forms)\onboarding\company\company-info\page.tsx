'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { useOnboarding } from '../components/onboarding-provider';
import Image from 'next/image';
import { useUser } from '@/hooks/useUser';
import { createCompanyProfile } from '@/actions/company/account';
import { uploadImage } from '@/utils/supabase/storage/client';
import { Loader2 } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

// Define the form schema
const formSchema = z.object({
  name: z.string().min(1, { message: 'Company name is required' }),
  description: z
    .string()
    .min(1, { message: 'Company description is required' }),
  company_crunchbase: z.string().optional(),
  company_linkedin: z.string().optional(),
  contact_email: z.string().email().optional(),
  current_funding: z.string().optional(),
  founder_linkedin: z.string().optional(),
  founder_school: z.string().optional(),
  industry: z.string().optional(),
  legally_incorporated: z.boolean().default(false),
  location: z.string().optional(),
  logo_url: z.string().optional().nullable(),
  open_for_reverse_hiring: z.boolean().default(false)
});

type FormValues = z.infer<typeof formSchema>;

export default function CompanyInfoPage() {
  const router = useRouter();
  const { data, updateCompanyInfo, markStepAsCompleted } = useOnboarding();
  const { user, loading } = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(
    data.companyInfo.logo_url
  );
  const [logoError, setLogoError] = useState(false);

  // Initialize form with existing data
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: data.companyInfo.name || '',
      description: data.companyInfo.description || '',
      company_crunchbase: data.companyInfo.company_crunchbase || '',
      company_linkedin: data.companyInfo.company_linkedin || '',
      contact_email: data.companyInfo.contact_email || '',
      current_funding: data.companyInfo.current_funding || '',
      founder_linkedin: data.companyInfo.founder_linkedin || '',
      founder_school: data.companyInfo.founder_school || '',
      industry: data.companyInfo.industry || '',
      legally_incorporated: data.companyInfo.legally_incorporated || false,
      location: data.companyInfo.location || '',
      logo_url: data.companyInfo.logo_url || '',
      open_for_reverse_hiring: data.companyInfo.open_for_reverse_hiring || false
    }
  });

  // Industries list
  const industries = [
    'Technology',
    'Finance',
    'Healthcare',
    'Education',
    'E-commerce',
    'Marketing',
    'Manufacturing',
    'Entertainment',
    'Food & Beverage',
    'Transportation',
    'Energy',
    'Real Estate',
    'Consulting',
    'Retail',
    'Other'
  ];

  // Current funding options
  const fundingOptions = [
    'Pre-seed',
    'Seed',
    'Series A',
    'Series B',
    'Series C+',
    'Bootstrapped',
    'Revenue Funded',
    'Public',
    'Other'
  ];

  // Handle logo file selection
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files && e.target.files[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
        setLogoError(false);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    if (isLoading) return; // Prevent multiple submissions

    setIsLoading(true);
    try {
      // Show submitting toast
      const loadingToast = toast.loading('Saving company information...');

      // Handle logo upload if a file was selected
      let logoUrl = values.logo_url;
      if (logoFile) {
        // Create folder name based on company name
        const folderName = values.name.toLowerCase().replace(/\s+/g, '-');

        // Upload the logo
        const { imageUrl, error } = await uploadImage({
          file: logoFile,
          bucket: 'company-logos',
          folder: folderName
        });

        if (error) {
          toast.error('Failed to upload company logo');
          return;
        }

        logoUrl = imageUrl;
      }

      // Update the context with form values
      updateCompanyInfo({
        ...values,
        logo_url: logoUrl
      });

      // Prepare company data for creation
      const startupCompanyData = {
        id: uuidv4(),
        name: values.name,
        description: values.description,
        company_crunchbase: values.company_crunchbase || null,
        company_linkedin: values.company_linkedin || null,
        contact_email: values.contact_email || null,
        current_funding: values.current_funding || null,
        founder_linkedin: values.founder_linkedin || null,
        founder_school: values.founder_school || null,
        industry: values.industry || null,
        legally_incorporated: values.legally_incorporated ? 'yes' : 'no',
        location: values.location || null,
        logo_url: logoUrl,
        open_for_reverse_hiring: values.open_for_reverse_hiring || false,
        creator_email: user?.email || null,
        slug: values.name.toLowerCase().replace(/\s+/g, '-')
      };

      // Create the startup company record using the server action
      if (user?.id) {
        const result = await createCompanyProfile(user.id, startupCompanyData);

        // Mark this step as completed
        markStepAsCompleted('company-info');

        // Dismiss loading toast and show success toast
        toast.dismiss(loadingToast);
        toast.success('Company information saved successfully!');

        // Navigate immediately after success
        router.push('/onboarding/company/complete');
      } else {
        toast.dismiss(loadingToast);
        toast.error(
          'User authentication error. Please try again or log out and log back in.'
        );
      }
    } catch (error) {
      toast.error('Failed to save company information. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state if user data is still loading
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-[#36BA98]" />
      </div>
    );
  }

  // Check if user exists
  if (!user) {
    return (
      <div className="container max-w-3xl mx-auto py-10 text-center">
        <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent mb-4">
          Authentication Required
        </h1>
        <p className="text-gray-600 mb-6">
          Please sign in to access the company onboarding process.
        </p>
        <Button
          onClick={() => router.push('/login')}
          className="bg-[#36BA98] hover:bg-[#2da888]"
        >
          Sign In
        </Button>
      </div>
    );
  }

  return (
    <div className="container max-w-3xl mx-auto py-10">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent mb-2">
          Company Information
        </h1>
        <p className="text-gray-600">
          Tell us about your company to help candidates learn more about you.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Company Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Name*</FormLabel>
                <FormControl>
                  <Input placeholder="Enter your company name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Company Description */}
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Company Description*</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Tell us about your company, mission, and what you do"
                    className="min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Company LinkedIn */}
            <FormField
              control={form.control}
              name="company_linkedin"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company LinkedIn</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://linkedin.com/company/..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Company Crunchbase */}
            <FormField
              control={form.control}
              name="company_crunchbase"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Company Crunchbase</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://crunchbase.com/organization/..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Contact Email */}
            <FormField
              control={form.control}
              name="contact_email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Contact Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Location */}
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input placeholder="City, Country" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Industry */}
            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Industry</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {industries.map((industry) => (
                        <SelectItem key={industry} value={industry}>
                          {industry}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Current Funding */}
            <FormField
              control={form.control}
              name="current_funding"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Current Funding</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select funding stage" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {fundingOptions.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Founder LinkedIn */}
            <FormField
              control={form.control}
              name="founder_linkedin"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Founder LinkedIn</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="https://linkedin.com/in/..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Founder School */}
            <FormField
              control={form.control}
              name="founder_school"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Founder School</FormLabel>
                  <FormControl>
                    <Input placeholder="E.g. Stanford University" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Logo Upload */}
          <div className="space-y-4">
            <FormLabel htmlFor="logo">Company Logo</FormLabel>
            <div className="flex items-center space-x-4">
              <div className="h-24 w-24 rounded-md border border-gray-200 flex items-center justify-center overflow-hidden bg-gray-50">
                {logoPreview && !logoError ? (
                  <Image
                    src={logoPreview}
                    alt="Company logo preview"
                    width={96}
                    height={96}
                    className="object-contain"
                    onError={() => setLogoError(true)}
                  />
                ) : (
                  <span className="text-gray-400 text-xs text-center px-2">
                    No logo uploaded
                  </span>
                )}
              </div>
              <div>
                <Input
                  id="logo"
                  type="file"
                  accept="image/*"
                  onChange={handleLogoChange}
                  className="w-full"
                  aria-label="Upload company logo"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Recommended size: 400x400px. Max file size: 2MB.
                </p>
              </div>
            </div>
          </div>

          {/* Legally Incorporated */}
          <FormField
            control={form.control}
            name="legally_incorporated"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    id="legally_incorporated"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    aria-describedby="legally_incorporated_desc"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel htmlFor="legally_incorporated">
                    Legally Incorporated
                  </FormLabel>
                  <FormDescription id="legally_incorporated_desc">
                    Is your company legally incorporated?
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          {/* Open for Reverse Hiring */}
          <FormField
            control={form.control}
            name="open_for_reverse_hiring"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                <FormControl>
                  <Checkbox
                    id="open_for_reverse_hiring"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    aria-describedby="open_for_reverse_hiring_desc"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel htmlFor="open_for_reverse_hiring">
                    Open for Reverse Hiring
                  </FormLabel>
                  <FormDescription id="open_for_reverse_hiring_desc">
                    Allow candidates to discover and apply to your company
                    directly, even without posted jobs
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <div className="pt-6">
            <Button
              type="submit"
              className="w-full bg-[#36BA98] hover:bg-[#2da888] transition-colors"
              disabled={isLoading}
              aria-disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save and Continue'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
