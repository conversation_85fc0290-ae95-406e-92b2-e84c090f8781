'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  Upload,
  Brain,
  Award,
  Lock,
  ExternalLink,
  CheckCircle,
  Crown,
  Gift,
  Loader2
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { useUser } from '@/hooks/useUser';

const aiServices = [
  {
    id: 1,
    title: 'Career Assessment',
    description: 'Evaluate your skills, interests, and career goals',
    chatbotUrl: 'https://chatgpt.com/g/g-lcPqBUliN-sophie-career-assessment',
    icon: Brain,
    isCompleted: true
  },
  {
    id: 2,
    title: 'LinkedIn Optimizer',
    description: 'Get feedback on your LinkedIn profile',
    chatbotUrl: 'https://chatgpt.com/g/g-Oreo9Cfth-internup-career-coach',
    icon: Star,
    isCompleted: false
  },
  {
    id: 3,
    title: 'Networking Assistant',
    description: 'Learn effective networking strategies',
    chatbotUrl:
      'https://chatgpt.com/g/g-GKlx7X9Pm-sophie-new-grad-networking-master',
    icon: Bot,
    isCompleted: false
  },
  {
    id: 4,
    title: 'Resume Builder',
    description: 'Get personalized resume feedback',
    chatbotUrl: 'https://chatgpt.com/g/g-0HfscAJe7-sophie-resume-master',
    icon: Award,
    isCompleted: false
  },
  {
    id: 5,
    title: 'Interview Coach',
    description: 'Practice technical and behavioral interviews',
    chatbotUrl:
      'https://chatgpt.com/g/g-EYQL5NYRm-sophie-entry-level-sde-interview-prep',
    icon: CheckCircle,
    isCompleted: false
  },
  {
    id: 6,
    title: 'Visa Guide',
    description: 'Navigate work authorization requirements',
    chatbotUrl: 'https://chatgpt.com/g/g-S9nWnLH7J-sophie-101',
    icon: Lock,
    isCompleted: false
  }
];

export default function AICoachingPage() {
  const { user, loading } = useUser();

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Header Section - Simplified to match capstone project style */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          AI Career Coaching
        </h1>
        <p className="text-muted-foreground">
          Get personalized career guidance with our AI-powered coaching
          services. Choose individual services or upgrade to VIP for an
          all-in-one experience.
        </p>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Services Column */}
        <div className="lg:col-span-2 space-y-6">
          {/* VIP All-in-One Service */}
          <Card className="bg-gradient-to-br from-[#118073]/10 via-[#118073]/5 to-background border-[#118073]/20 shadow-md">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-[#118073]" />
                  <CardTitle>VIP All-in-One Coaching</CardTitle>
                </div>
                <Badge variant="default" className="bg-[#118073]">
                  VIP
                </Badge>
              </div>
              <CardDescription>
                Access our comprehensive AI coaching platform with all services
                integrated
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <Button
                  className="w-full bg-[#118073] hover:bg-[#118073]/90"
                  size="lg"
                  disabled
                >
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Loading...
                </Button>
              ) : user?.vip ? (
                <Button
                  className="w-full bg-[#118073] hover:bg-[#118073]/90"
                  size="lg"
                  onClick={() =>
                    window.open(
                      'https://chatgpt.com/g/g-nzda1ZqWY-sophie-all-in-one',
                      '_blank'
                    )
                  }
                >
                  Start Chat
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Button>
              ) : (
                <Button
                  className="w-full bg-[#118073] hover:bg-[#118073]/90"
                  size="lg"
                  onClick={() =>
                    window.open(
                      'https://chatgpt.com/g/g-nzda1ZqWY-sophie-all-in-one',
                      '_blank'
                    )
                  }
                >
                  Upgrade Now
                  <ExternalLink className="ml-2 h-4 w-4" />
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Individual Services Grid */}
          <div className="grid gap-4 sm:grid-cols-2">
            {aiServices.map((service) => (
              <Card
                key={service.id}
                className="shadow-sm hover:shadow-md transition-shadow"
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <service.icon className="h-5 w-5 text-[#118073]" />
                      <CardTitle className="text-base">
                        {service.title}
                      </CardTitle>
                    </div>
                    {service.isCompleted && (
                      <Badge
                        variant="secondary"
                        className="bg-[#118073]/10 text-[#118073] border-none"
                      >
                        Completed
                      </Badge>
                    )}
                  </div>
                  <CardDescription>{service.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button
                    variant="outline"
                    className="w-full border-[#118073] text-[#118073] hover:bg-[#118073] hover:text-white"
                    onClick={() => window.open(service.chatbotUrl, '_blank')}
                  >
                    Start Chat
                    <ExternalLink className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Ease Your Mind Chatbot */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-[#118073]" />
                <CardTitle>Ease Your Mind</CardTitle>
              </div>
              <CardDescription>
                Feel stressed? Chat with our AI companion for emotional support
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                variant="secondary"
                className="w-full bg-[#118073]/10 text-[#118073] hover:bg-[#118073] hover:text-white"
                onClick={() =>
                  window.open(
                    'https://chatgpt.com/g/g-QkkcNLMqE-samantha',
                    '_blank'
                  )
                }
              >
                Start Conversation
                <ExternalLink className="ml-2 h-4 w-4" />
              </Button>
            </CardContent>
          </Card>

          {/* Discount Offer */}
          <Card className="shadow-sm hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-center gap-2">
                <Gift className="h-5 w-5 text-[#118073]" />
                <CardTitle>Get 20% Off</CardTitle>
              </div>
              <CardDescription>
                Share your chat experience and receive a discount code
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button
                variant="outline"
                className="w-full border-[#118073] text-[#118073] hover:bg-[#118073] hover:text-white"
                onClick={() =>
                  window.open(
                    'https://docs.google.com/forms/d/e/1FAIpQLSc9r1HTS7riAbxwlsd6usp0jTq0e0ZM2yPfI-qEPeoglAzjkw/viewform',
                    '_blank'
                  )
                }
              >
                <Upload className="mr-2 h-4 w-4" />
                Share Feedback
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
