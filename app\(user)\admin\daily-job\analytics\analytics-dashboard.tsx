'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import type { DailyJob } from '@/types/crawler';
import dynamic from 'next/dynamic';

// Import all components and utilities from our components folder
import {
  PeriodSelector,
  StatsSummary,
  UserActivityTable,
  calculateStats,
  filterJobsByPeriod,
  prepareDailyDownloadsData,
  createUserTypeData,
  prepareFileTypeData
} from './components';

// Dynamically import recharts components with noSSR
const RechartsComponent = dynamic(() => import('./recharts-components'), {
  ssr: false
});

// Main analytics dashboard component
interface AnalyticsDashboardProps {
  initialJobs: DailyJob[];
}

export default function AnalyticsDashboard({
  initialJobs
}: AnalyticsDashboardProps) {
  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('7days');
  const [filteredJobs, setFilteredJobs] = useState<DailyJob[]>([]);

  // Stats
  const [totalDownloads, setTotalDownloads] = useState<number>(0);
  const [vipDownloads, setVipDownloads] = useState<number>(0);
  const [nonVipDownloads, setNonVipDownloads] = useState<number>(0);
  const [uniqueUsers, setUniqueUsers] = useState<number>(0);

  // Process jobs data based on selected period
  useEffect(() => {
    if (initialJobs.length === 0) return;

    // Filter jobs based on period
    const filtered = filterJobsByPeriod(initialJobs, selectedPeriod);
    setFilteredJobs(filtered);

    // Calculate stats
    const stats = calculateStats(filtered);
    setVipDownloads(stats.vipDownloads);
    setNonVipDownloads(stats.nonVipDownloads);
    setTotalDownloads(stats.totalDownloads);
    setUniqueUsers(stats.uniqueUsers);
  }, [initialJobs, selectedPeriod]);

  // Prepare chart data
  const dailyDownloadsData = prepareDailyDownloadsData(filteredJobs);
  const userTypeData = createUserTypeData(vipDownloads, nonVipDownloads);
  const fileTypeData = prepareFileTypeData(filteredJobs);

  if (loading) {
    return <div className="text-center py-10">Loading analytics data...</div>;
  }

  return (
    <div className="space-y-8">
      {/* Period selector */}
      <PeriodSelector
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
      />

      {/* Stats summary */}
      <StatsSummary
        totalDownloads={totalDownloads}
        vipDownloads={vipDownloads}
        nonVipDownloads={nonVipDownloads}
        uniqueUsers={uniqueUsers}
      />

      {/* Charts */}
      <RechartsComponent
        dailyDownloadsData={dailyDownloadsData}
        userTypeData={userTypeData}
        fileTypeData={fileTypeData}
      />

      {/* Most active users */}
      <Card>
        <CardHeader>
          <CardTitle>Most Active Users</CardTitle>
          <CardDescription>
            Users with the most downloads during the selected period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserActivityTable jobs={filteredJobs} />
        </CardContent>
      </Card>
    </div>
  );
}
