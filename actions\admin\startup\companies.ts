// Companies related actions
import PaginatedResponse from '@/types/pagination';
import {
  CompaniesQueryParams,
  StartupCompany,
  StartupCompanyFormData
} from '@/types/startups';
import { createClient } from '@/utils/supabase/client';
import { revalidatePath } from 'next/cache';

// Helper function to generate random string ID
function generateId(): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${randomStr}`;
}

export async function getCompanies({
  page = 1,
  searchTerm = '',
  pageSize = 20,
  sortBy = 'created_at',
  sortOrder = 'desc'
}: CompaniesQueryParams): Promise<PaginatedResponse<StartupCompany>> {
  const supabase = createClient();
  const startIndex = (page - 1) * pageSize;

  // First get total count with the same filters
  let countQuery = supabase
    .from('startup_companies')
    .select('*', { count: 'exact' });

  if (searchTerm) {
    countQuery = countQuery.ilike('name', `%${searchTerm}%`);
  }

  const countResult = await countQuery;

  // Main query with sorting
  let query = supabase.from('startup_companies').select('*');

  if (searchTerm) {
    query = query.ilike('name', `%${searchTerm}%`);
  }

  const { data, error } = await query
    .order(sortBy, { ascending: sortOrder === 'asc' })
    .range(startIndex, startIndex + pageSize - 1);

  if (error) {
    console.error('Error fetching companies:', error);
    throw new Error('Failed to fetch companies');
  }

  const totalCount = countResult.count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return { data, totalPages, totalCount };
}

export async function getCompanyByName(name: string): Promise<StartupCompany> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('startup_companies')
    .select('*')
    .eq('name', name)
    .single();

  if (error) {
    console.error('Error fetching company:', error);
    throw new Error('Failed to fetch company');
  }
  return data as StartupCompany;
}

export async function getAllCompanies(
  sortBy = 'created_at',
  sortOrder = 'desc'
): Promise<StartupCompany[]> {
  const supabase = createClient();
  const { data, error } = await supabase
    .from('startup_companies')
    .select('*')
    .order(sortBy, { ascending: sortOrder === 'asc' });
  if (error) {
    console.error('Error fetching companies:', error);
    throw new Error('Failed to fetch companies');
  }
  return data as StartupCompany[];
}

export async function createCompany(
  companyData: StartupCompanyFormData
): Promise<StartupCompany> {
  // Add this directive to mark this as a server action

  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_companies')
    .insert({
      id: generateId(), // Add unique string ID
      ...companyData,
      created_at: new Date().toISOString()
    })
    .select();

  if (error) {
    console.error('Error creating company:', error);
    throw new Error('Failed to create company');
  }

  // revalidatePath("/admin/startup/companies")
  return data[0] as StartupCompany;
}

export async function updateCompany(
  id: string,
  companyData: Partial<StartupCompanyFormData>
): Promise<StartupCompany> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_companies')
    .update({ ...companyData, modified_at: new Date().toISOString() })
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating company:', error);
    throw new Error('Failed to update company');
  }

  // revalidatePath("/admin/startup/companies")
  return data[0] as StartupCompany;
}

// In actions/admin/startups.ts
export async function deleteCompany(id: string): Promise<{ success: boolean }> {
  const supabase = createClient();

  // First, delete all jobs associated with this company
  const { error: jobsError } = await supabase
    .from('startup_jobs')
    .delete()
    .eq('company_id', id);

  if (jobsError) {
    console.error('Error deleting associated jobs:', jobsError);
    throw new Error('Failed to delete associated jobs');
  }

  // Then delete the company
  const { error } = await supabase
    .from('startup_companies')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting company:', error);
    throw new Error('Failed to delete company');
  }

  // revalidatePath("/admin/startup/companies")
  return { success: true };
}
