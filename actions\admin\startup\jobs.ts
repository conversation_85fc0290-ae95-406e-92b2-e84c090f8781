// actions/admin/startup/jobs.ts
import { createClient } from '@/utils/supabase/client';
// import { revalidatePath } from "next/cache"
import type {
  StartupJob,
  StartupJobFormData,
  JobsQueryParams,
  StartupCompany
} from '@/types/startups';
import type PaginatedResponse from '@/types/pagination';

export async function getJobs({
  page = 1,
  searchTerm = '',
  companyId = ''
}: JobsQueryParams): Promise<PaginatedResponse<StartupJob>> {
  const supabase = createClient();
  const pageSize = 20;
  const startIndex = (page - 1) * pageSize;

  let query = supabase.from('startup_jobs').select(`
      *,
      company:company_id(
        id,
        name,
        logo_url
      )
    `);

  if (searchTerm) {
    query = query.ilike('title', `%${searchTerm}%`);
  }

  if (companyId) {
    query = query.eq('company_id', companyId);
  }

  // First get total count
  const { count: totalCount, error: countError } = await supabase
    .from('startup_jobs')
    .select('id', { count: 'exact' })
    .match({
      ...(searchTerm && { title: `%${searchTerm}%` }),
      ...(companyId && { company_id: companyId })
    });

  if (countError) {
    console.error('Error fetching job count:', countError);
    throw new Error('Failed to fetch job count');
  }

  const { data, error } = await query
    .order('created_at', { ascending: false })
    .range(startIndex, startIndex + pageSize - 1);

  if (error) {
    console.error('Error fetching jobs:', error);
    throw new Error('Failed to fetch jobs');
  }

  const totalPages = Math.ceil((totalCount ?? 0) / pageSize);

  return { data, totalPages, totalCount: totalCount ?? 0 };
}

export async function getCompanies(): Promise<{ data: StartupCompany[] }> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_companies')
    .select('id, name, logo_url')
    .order('name', { ascending: true })
    .limit(10000);
  if (error) {
    console.error('Error fetching companies:', error);
    throw new Error('Failed to fetch companies');
  }

  return { data: data as StartupCompany[] };
}

export async function createJob(
  jobData: StartupJobFormData
): Promise<StartupJob> {
  const supabase = createClient();

  try {
    // Add UUIDs for required_skills if it's an empty array
    // Some databases require non-null values for array fields
    if (jobData.required_skills && jobData.required_skills.length === 0) {
      jobData.required_skills = [];
    }

    const { data, error } = await supabase
      .from('startup_jobs')
      .insert({ ...jobData, created_at: new Date().toISOString() })
      .select();

    if (error) {
      console.error('Supabase error creating job:', error);
      throw new Error(
        `Failed to create job: ${error.message}, ${error.details || ''}`
      );
    }

    if (!data || data.length === 0) {
      throw new Error('No data returned from job creation');
    }

    // revalidatePath("/startup/jobs")
    return data[0] as StartupJob;
  } catch (error) {
    console.error('Unexpected error in createJob:', error);
    throw error;
  }
}

export async function updateJob(
  id: string,
  jobData: Partial<StartupJobFormData>
): Promise<StartupJob> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_jobs')
    .update({ ...jobData, modified_at: new Date().toISOString() })
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating job:', error);
    throw new Error('Failed to update job');
  }

  // revalidatePath("/startup/jobs")
  return data[0] as StartupJob;
}

export async function deleteJob(id: string): Promise<{ success: boolean }> {
  const supabase = createClient();

  const { error } = await supabase.from('startup_jobs').delete().eq('id', id);

  if (error) {
    console.error('Error deleting job:', error);
    throw new Error('Failed to delete job');
  }

  // revalidatePath("/startup/jobs")
  return { success: true };
}
