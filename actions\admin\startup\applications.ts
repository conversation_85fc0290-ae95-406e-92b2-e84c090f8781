// Applications related actions
import { createClient } from '@/utils/supabase/client';

import { ApplicationsQueryParams, StartupApplication } from '@/types/startups';
import PaginatedResponse from '@/types/pagination';
import { User } from '@/types/user';
export async function getApplications({
  page = 1,
  searchTerm = '',
  user
}: ApplicationsQueryParams): Promise<PaginatedResponse<StartupApplication>> {
  const supabase = createClient();
  const pageSize = 20;
  const startIndex = (page - 1) * pageSize;

  if (!user) {
    throw new Error('Unauthorized');
  }

  // Check if user is admin
  const isAdmin = user.user_type?.toLowerCase() === 'admin';

  // Build the query
  let query = supabase.from('startup_applications').select('*');
  let countQuery = supabase
    .from('startup_applications')
    .select('*', { count: 'exact', head: true });

  // If not admin, only show user's own applications
  if (!isAdmin) {
    query = query.eq('candidate', user.email);
    countQuery = countQuery.eq('candidate', user.email);
  }

  // Add search functionality if provided
  if (searchTerm) {
    const searchFilter = `job_title.ilike.%${searchTerm}%,company_name.ilike.%${searchTerm}%`;
    query = query.or(searchFilter);
    countQuery = countQuery.or(searchFilter);
  }

  // Get count
  const { count } = await countQuery;

  // Execute the main query
  const { data, error } = await query
    .order('creation_date', { ascending: false })
    .range(startIndex, startIndex + pageSize - 1);

  if (error) {
    throw new Error(`Failed to fetch applications: ${error.message}`);
  }

  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data || [],
    totalPages,
    totalCount: count || 0
  };
}

export async function updateApplicationStatus(
  id: string,
  status: string,
  user: User
): Promise<StartupApplication> {
  const supabase = createClient();

  // Check if user is admin
  const isAdmin = user.user_type?.toLowerCase() === 'admin';

  // Build the query
  let query = supabase
    .from('startup_applications')
    .update({ status, modified_date: new Date().toISOString() })
    .eq('id', id);

  // If not admin, only allow updating own applications
  if (!isAdmin) {
    query = query.eq('candidate', user.email);
  }

  const { data, error } = await query.select();

  if (error) {
    throw new Error('Failed to update application status');
  }

  if (!data || data.length === 0) {
    throw new Error('Application not found or unauthorized');
  }

  return data[0] as StartupApplication;
}

export async function deleteApplication(
  id: string,
  user: User
): Promise<{ success: boolean }> {
  const supabase = createClient();

  if (!user) {
    throw new Error('Unauthorized');
  }

  // Check if user is admin
  const isAdmin = user.user_type?.toLowerCase() === 'admin';

  // Build the query
  let query = supabase.from('startup_applications').delete().eq('id', id);

  // If not admin, only allow deleting own applications
  if (!isAdmin) {
    query = query.eq('candidate', user.email);
  }

  const { error } = await query;

  if (error) {
    throw new Error('Failed to delete application');
  }

  return { success: true };
}
