import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/utils/stripe/config';

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { accountId, refreshUrl, returnUrl } = body;

    if (!accountId || !refreshUrl || !returnUrl) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create account link
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return NextResponse.json({ url: accountLink.url });
  } catch (error) {
    console.error('Error creating Stripe account link:', error);
    return NextResponse.json(
      { error: 'Failed to create Stripe account link' },
      { status: 500 }
    );
  }
}
