'use server';

import { createClient as createServerClient } from '@/utils/supabase/server';
import {
  CreateReferralInput,
  Referral,
  ReferralResponse,
  InsiderReferralResponse
} from '@/types/referral';

/**
 * Create a new referral request
 * @param referralData - The referral data to create
 * @returns The created referral's ID
 */
export async function createReferral(
  referralData: CreateReferralInput
): Promise<{ referralId: number }> {
  const supabase = createServerClient();

  try {
    // Add validation to ensure required fields are present
    if (!referralData.requested_company) {
      throw new Error('Company name is required');
    }

    if (!referralData.job_title) {
      throw new Error('Job title is required');
    }

    if (!referralData.candidate_id) {
      throw new Error('Candidate ID is required');
    }

    if (!referralData.candidate_resume) {
      throw new Error('Resume is required');
    }

    const referralDataWithTimestamp = {
      ...referralData,
      created_at: new Date().toISOString(),
      status: 'pending'
    };

    const { data, error } = await supabase
      .from('referrals')
      .insert(referralDataWithTimestamp)
      .select()
      .single();

    if (error) {
      console.error('Insert error:', error);
      throw new Error(error.message);
    }

    return { referralId: data.id };
  } catch (error) {
    console.error('Supabase error creating referral:', error);
    throw new Error('Failed to create referral request');
  }
}

/**
 * Get all referrals with pagination and search
 */
export async function getAllReferrals(
  page: number,
  itemsPerPage: number,
  searchTerm?: string
): Promise<{ data: ReferralResponse[]; count: number }> {
  const supabase = createServerClient();
  const startIndex = (page - 1) * itemsPerPage;

  try {
    let query = supabase.from('referrals').select(
      `
        *,
        profiles:candidate_id (
          id,
          email,
          first_name,
          last_name
        ),
        insiders:insider_id (
          id,
          first_name,
          last_name,
          user_email
        )
      `,
      { count: 'exact' }
    );

    if (searchTerm) {
      query = query.or(`
        requested_company.ilike.%${searchTerm}%,
        job_title.ilike.%${searchTerm}%
      `);
    }

    const { data, error, count } = await query
      .range(startIndex, startIndex + itemsPerPage - 1)
      .order('created_at', { ascending: false });
    if (error) {
      console.error('Query error:', error);
      throw error;
    }

    // Transform the data to match the ReferralResponse interface
    const transformedData: ReferralResponse[] = data.map((item: any) => ({
      id: item.id,
      candidate_id: item.candidate_id,
      candidate_name: item.profiles
        ? `${item.profiles.first_name} ${item.profiles.last_name}`
        : 'Unknown',
      candidate_email: item.profiles?.email,
      requested_company: item.requested_company,
      job_title: item.job_title,
      job_listing_url: item.job_listing_url,
      candidate_resume: item.candidate_resume,
      candidate_linkedin: item.candidate_linkedin,
      creation_date: item.created_at,
      status: item.status,
      insider_id: item.insider_id,
      insider_name: item.insiders
        ? `${item.insiders.first_name} ${item.insiders.last_name}`
        : undefined,
      insider_email: item.insiders?.user_email,
      self_introduction: item.self_introduction
    }));

    return {
      data: transformedData,
      count: count || 0
    };
  } catch (error) {
    console.error('Error fetching referrals:', error);
    throw new Error('Failed to fetch referrals');
  }
}

/**
 * Get all referrals for a specific user
 * @param userId - The user ID (email) to get referrals for
 * @returns Array of referrals for the user
 */
export async function getReferralsForUser(userId: string): Promise<Referral[]> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('referrals')
      .select('*')
      .eq('candidate_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Supabase error fetching referrals for user:', error);
      throw new Error('Failed to fetch referrals for user');
    }

    return data || [];
  } catch (error) {
    console.error('Error fetching referrals for user:', error);
    throw new Error('Failed to fetch referrals for user');
  }
}

/**
 * Get the count of referrals for a user since a specific date
 * @param userId - The user ID (email) to count referrals for
 * @param startDate - The start date from which to count referrals
 * @returns The number of referrals created by the user since the start date
 */
export async function getReferralCountSinceDate(
  userId: string,
  startDate: string
): Promise<number> {
  const supabase = createServerClient();

  try {
    const { count, error } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('candidate_id', userId)
      .gte('created_at', startDate);

    if (error) {
      console.error('Supabase error counting referrals:', error);
      throw new Error('Failed to count referrals');
    }

    return count || 0;
  } catch (error) {
    console.error('Error counting referrals:', error);
    throw new Error('Failed to count referrals');
  }
}

/**
 * Get a single referral by ID
 * @param referralId - The ID of the referral to retrieve
 * @returns The referral or null if it doesn't exist
 */
export async function getReferralById(
  referralId: number
): Promise<Referral | null> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('referrals')
      .select('*')
      .eq('id', referralId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Record not found
        return null;
      }
      console.error('Supabase error fetching referral:', error);
      throw new Error('Failed to fetch referral');
    }

    return data;
  } catch (error) {
    console.error('Error fetching referral:', error);
    throw new Error('Failed to fetch referral');
  }
}

/**
 * Get the weekly referral count for a user (last 7 days)
 * @param userId - The user ID (email) to count weekly referrals for
 * @returns The number of referrals created by the user in the last 7 days
 */
export async function getWeeklyReferralCount(userId: string): Promise<number> {
  // Calculate date 7 days ago
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  const startDate = sevenDaysAgo.toISOString();

  return getReferralCountSinceDate(userId, startDate);
}

// Add this new function to check if a referral exists
export async function checkReferralExists(candidateId: string, jobId: string) {
  const supabaseAdmin = createServerClient();

  try {
    const { data, error } = await supabaseAdmin
      .from('referrals')
      .select('id')
      .eq('candidate_id', candidateId)
      .eq('job_id', jobId)
      .single();

    if (error) throw error;

    return !!data; // Returns true if a referral exists, false otherwise
  } catch (error) {
    console.error('Error checking referral status:', error);
    return false;
  }
}

/**
 * Get available insiders for a company
 */
export async function getAvailableInsiders(companyName: string) {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('insiders')
      .select('id, first_name, last_name, user_email, public_firm, position')
      .eq('public_firm', companyName)
      .eq('account_status', 'Accepted');

    if (error) throw error;

    return data.map((insider) => ({
      id: insider.id,
      name: insider.first_name + ' ' + insider.last_name,
      email: insider.user_email,
      company: insider.public_firm,
      role: insider.position
    }));
  } catch (error) {
    console.error('Error fetching insiders:', error);
    throw new Error('Failed to fetch insiders');
  }
}

/**
 * Assign an insider to a referral
 */
export async function assignInsiderToReferral(
  referralId: string,
  insiderId: string
) {
  const supabase = createServerClient();

  try {
    // First update the referral
    const { error: updateError } = await supabase
      .from('referrals')
      .update({
        insider_id: insiderId,
        status: 'assigned',
        updated_at: new Date().toISOString()
      })
      .eq('id', referralId);

    if (updateError) {
      console.error('Update error:', updateError);
      throw updateError;
    }

    // Then fetch the updated referral with insider information
    const { data: updatedReferral, error: fetchError } = await supabase
      .from('referrals')
      .select(
        `
        *,
        insiders:insider_id (
          id,
          first_name,
          last_name,
          user_email
        )
      `
      )
      .eq('id', referralId)
      .single();

    if (fetchError) {
      console.error('Fetch error:', fetchError);
      throw fetchError;
    }

    // Transform the data to match the expected format
    return {
      ...updatedReferral,
      insider_name: updatedReferral.insiders
        ? `${updatedReferral.insiders.first_name} ${updatedReferral.insiders.last_name}`
        : undefined,
      insider_email: updatedReferral.insiders?.user_email
    };
  } catch (error) {
    console.error('Error assigning insider:', error);
    throw new Error('Failed to assign insider');
  }
}

/**
 * Get referrals for a specific user with pagination and filtering
 */
export async function getUserReferrals(
  userId: string,
  page: number = 1,
  limit: number = 20,
  searchTerm?: string,
  status?: string
) {
  const supabase = createServerClient();
  const startIndex = (page - 1) * limit;

  try {
    let query = supabase
      .from('referrals')
      .select(
        `
        *,
        insiders:insider_id (
          id,
          first_name,
          last_name,
          user_email,
          public_firm
        )
      `,
        { count: 'exact' }
      )
      .eq('candidate_id', userId);

    // Apply status filter if provided
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    // Apply search filter if provided
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`
        requested_company.ilike.%${searchTerm}%,
        job_title.ilike.%${searchTerm}%
      `);
    }

    const { data, error, count } = await query
      .range(startIndex, startIndex + limit - 1)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user referrals:', error);
      throw error;
    }

    return {
      data: data || [],
      count: count || 0,
      currentPage: page,
      totalPages: Math.ceil((count || 0) / limit)
    };
  } catch (error) {
    console.error('Error in getUserReferrals:', error);
    throw new Error('Failed to fetch referrals');
  }
}

/**
 * Get referrals assigned to an insider
 * @param insiderId - The ID of the insider
 * @param status - Optional status filter
 * @returns Array of referrals assigned to the insider
 */
export async function getInsiderReferrals(
  insiderId: string,
  status?: string
): Promise<InsiderReferralResponse[]> {
  const supabase = createServerClient();
  try {
    let query = supabase
      .from('referrals')
      .select(
        `
        *,
        profiles:candidate_id (
          id,
          email,
          first_name,
          last_name
        )
      `
      )
      .eq('insider_id', insiderId);

    // Apply status filter if provided
    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('created_at', {
      ascending: false
    });

    if (error) {
      console.error('Error fetching insider referrals:', error);
      throw error;
    }

    // Transform the data to match the InsiderReferralResponse interface
    const transformedData: InsiderReferralResponse[] = data.map(
      (item: any) => ({
        id: item.id,
        candidateName: item.profiles
          ? `${item.profiles.first_name} ${item.profiles.last_name}`
          : 'Unknown',
        candidateEmail: item.profiles?.email,
        candidateId: item.candidate_id,
        jobTitle: item.job_title,
        companyName: item.requested_company,
        location: '', // This might not be in your database, add if available
        date: new Date(item.created_at).toLocaleDateString(),
        status: item.status,
        jobListingUrl: item.job_listing_url,
        candidateResume: item.candidate_resume,
        candidateLinkedin: item.candidate_linkedin,
        notes: item.notes || '',
        candidatePhone: item.candidate_phone_number,
        selfIntroduction: item.self_introduction
      })
    );

    return transformedData;
  } catch (error) {
    console.error('Error fetching insider referrals:', error);
    throw new Error('Failed to fetch insider referrals');
  }
}

/**
 * Mark a referral as reviewed by insider
 * @param referralId - The ID of the referral to mark as reviewed
 * @param insiderId - The ID of the insider reviewing the referral
 * @param notes - Optional notes from the insider
 * @returns The updated referral
 */
export async function markReferralAsReviewed(
  referralId: string,
  insiderId: string,
  notes?: string
): Promise<{ success: boolean }> {
  const supabase = createServerClient();

  try {
    // First, get the current status of the referral
    const { data: referral, error: fetchError } = await supabase
      .from('referrals')
      .select('status')
      .eq('id', referralId)
      .single();

    if (fetchError) {
      console.error('Error fetching referral:', fetchError);
      throw fetchError;
    }

    // Only allow marking as reviewed if the current status is 'assigned'
    if (referral.status !== 'assigned') {
      throw new Error('Only assigned referrals can be marked as reviewed');
    }

    // Update the referral status to 'reviewed'
    const updateData: any = {
      status: 'reviewed',
      updated_at: new Date().toISOString()
    };

    if (notes) {
      updateData.notes = notes;
    }

    const { error } = await supabase
      .from('referrals')
      .update(updateData)
      .eq('id', referralId);

    if (error) {
      console.error('Error updating referral status:', error);
      throw error;
    }

    // Add $1 to the insider's earnings for reviewing
    try {
      // Import the addToInsiderBalance function from insider-payouts
      const { addToInsiderBalance } = await import('@/actions/insider-payouts');

      // Add $1 (100 cents) to the insider's balance
      await addToInsiderBalance(
        insiderId,
        100, // $1 in cents
        'Earnings for reviewing referral'
      );
    } catch (earningsError) {
      console.error('Error adding earnings for review:', earningsError);
      // Don't throw here, as the status was already updated
    }

    return { success: true };
  } catch (error) {
    console.error('Error marking referral as reviewed:', error);
    throw new Error('Failed to mark referral as reviewed');
  }
}

/**
 * Update referral status by insider
 * @param referralId - The ID of the referral to update
 * @param status - The new status
 * @param notes - Optional notes from the insider
 * @returns The updated referral
 */
export async function updateReferralStatus(
  referralId: string,
  status: string,
  notes?: string
): Promise<{ success: boolean }> {
  const supabase = createServerClient();

  try {
    // First, get the current status and insider ID of the referral
    const { data: referral, error: fetchError } = await supabase
      .from('referrals')
      .select('status, insider_id')
      .eq('id', referralId)
      .single();

    if (fetchError) {
      console.error('Error fetching referral:', fetchError);
      throw fetchError;
    }

    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (notes) {
      updateData.notes = notes;
    }

    const { error } = await supabase
      .from('referrals')
      .update(updateData)
      .eq('id', referralId);

    if (error) {
      console.error('Error updating referral status:', error);
      throw error;
    }

    // If the status is being changed to 'accepted' and the previous status was 'reviewed',
    // add $2 to the insider's earnings
    if (status === 'accepted' && referral.status === 'reviewed') {
      try {
        // Import the addToInsiderBalance function from insider-payouts
        const { addToInsiderBalance } = await import(
          '@/actions/insider-payouts'
        );

        // Add $2 (200 cents) to the insider's balance
        await addToInsiderBalance(
          referral.insider_id,
          200, // $2 in cents
          'Bonus earnings for accepted referral after review'
        );
      } catch (earningsError) {
        console.error(
          'Error adding bonus earnings for accepted referral:',
          earningsError
        );
        // Don't throw here, as the status was already updated
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating referral status:', error);
    throw new Error('Failed to update referral status');
  }
}

/**
 * Get referral statistics for an insider
 * @param insiderId - The ID of the insider
 * @returns Statistics about the insider's referrals
 */
export async function getInsiderReferralStats(insiderId: string): Promise<{
  pending: number;
  reviewed: number;
  accepted: number;
  rejected: number;
  total: number;
}> {
  const supabase = createServerClient();

  try {
    // Get total count
    const { count: total, error: totalError } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('insider_id', insiderId);

    if (totalError) throw totalError;

    // Get pending count (assigned status)
    const { count: pending, error: pendingError } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('insider_id', insiderId)
      .eq('status', 'assigned');

    if (pendingError) throw pendingError;

    // Get reviewed count
    const { count: reviewed, error: reviewedError } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('insider_id', insiderId)
      .eq('status', 'reviewed');

    if (reviewedError) throw reviewedError;

    // Get accepted count
    const { count: accepted, error: acceptedError } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('insider_id', insiderId)
      .eq('status', 'accepted');

    if (acceptedError) throw acceptedError;

    // Get rejected count
    const { count: rejected, error: rejectedError } = await supabase
      .from('referrals')
      .select('*', { count: 'exact', head: true })
      .eq('insider_id', insiderId)
      .eq('status', 'rejected');

    if (rejectedError) throw rejectedError;

    return {
      pending: pending || 0,
      reviewed: reviewed || 0,
      accepted: accepted || 0,
      rejected: rejected || 0,
      total: total || 0
    };
  } catch (error) {
    console.error('Error fetching insider referral stats:', error);
    throw new Error('Failed to fetch insider referral statistics');
  }
}
