'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { ProgressIndicator } from '../components/progress-indicator';
import { useOnboarding } from '../components/onboarding-provider';
import { createClient } from '@/utils/supabase/client';
import { useUser } from '@/hooks/useUser';
import { updateCandidateProfile } from '@/actions/admin/candidate';
import { uploadFile } from '@/utils/supabase/storage/client';

type ExtendedAboutYouInfo = {
  resume_url?: string | null;
  portfolio?: string | null;
  linkedin_url?: string | null;
  bio?: string | null;
  mbti?: string | null;
  language_proficiency?: string[];
};

export default function AboutYouPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data, updateAboutYou, markStepAsCompleted } = useOnboarding();
  const supabase = createClient();
  const { user } = useUser();

  // Define steps for progress indicator
  const steps = [
    '/onboarding/candidate/personal-info',
    '/onboarding/candidate/job-preferences',
    '/onboarding/candidate/about-you',
    '/onboarding/candidate/complete'
  ];

  // State for form fields
  const [resumeFile, setResumeFile] = useState<File | null>(null);
  const [resumeUrl, setResumeUrl] = useState<string | null>(null);
  const [portfolioLink, setPortfolioLink] = useState('');
  const [linkedinLink, setLinkedinLink] = useState('');
  const [bio, setBio] = useState('');
  const [mbti, setMbti] = useState('');
  const [isUploading, setIsUploading] = useState(false);

  // Load existing data if available
  useEffect(() => {
    if (data.aboutYou) {
      const extendedInfo = data.aboutYou as unknown as ExtendedAboutYouInfo;
      setResumeUrl(extendedInfo.resume_url || null);
      setPortfolioLink(extendedInfo.portfolio || '');
      setLinkedinLink(extendedInfo.linkedin_url || '');
      setBio(extendedInfo.bio || '');
      setMbti(extendedInfo.mbti || '');
    }
  }, [data.aboutYou]);

  // Resume upload handler
  const handleResumeUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type - only accept PDF files
    if (file.type !== 'application/pdf') {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a PDF file',
        variant: 'destructive'
      });
      return;
    }

    // Validate file size (7MB max)
    if (file.size > 7 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Resume file must be less than 7MB',
        variant: 'destructive'
      });
      return;
    }

    setResumeFile(file);

    // For the preview, you can create a temporary URL
    // This will be replaced with actual upload during form submission
    const objectUrl = URL.createObjectURL(file);
    setResumeUrl(objectUrl);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      let finalResumeUrl = resumeUrl;

      // Upload resume if a new file was selected
      if (resumeFile) {
        setIsUploading(true);

        // Use the uploadFile utility to upload the resume
        const result = await uploadFile({
          file: resumeFile,
          bucket: 'candidate-resumes',
          folder: `profile_user_${user?.id || user?.email}`
        });

        if (result.error) {
          throw new Error(result.error);
        }

        finalResumeUrl = result.fileUrl;
        setIsUploading(false);
      }

      // Prepare form data for context
      const aboutYouData = {
        resume_url: finalResumeUrl,
        portfolio: portfolioLink || null,
        linkedin_url: linkedinLink || null,
        bio: bio || null,
        mbti: mbti || null
      } as unknown as typeof data.aboutYou;

      // Update context with form data
      updateAboutYou(aboutYouData);

      // Store in localStorage for persistence
      localStorage.setItem(
        'onboarding_about_you',
        JSON.stringify(aboutYouData)
      );

      // Update user profile in the database if user is available
      if (user?.id) {
        await updateCandidateProfile(user.id, {
          resume_url: finalResumeUrl,
          portfolio: portfolioLink || null,
          linkedin_url: linkedinLink || null,
          bio: bio || null,
          mbti: mbti || null
        });
      }

      // Mark this step as completed
      markStepAsCompleted('/onboarding/candidate/about-you');

      // Show success toast
      toast({
        title: 'Information Saved',
        description: 'Your additional information has been saved successfully.',
        duration: 3000
      });

      // Navigate to the next step
      router.push('/onboarding/candidate/complete');
    } catch (error) {
      console.error('Error saving information:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Something went wrong. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header with progress indicator */}
      <ProgressIndicator steps={steps} />

      <div className="flex flex-1 p-6 md:p-12">
        {/* Left section with logo */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center pr-12">
          <div className="flex flex-col items-center gap-8">
            <div className="flex items-center space-x-2">
              <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
                <Image
                  src="/favicon_new.png"
                  alt="logo"
                  width={25}
                  height={25}
                />
              </div>
              <span className="text-lg md:text-xl font-extrabold tracking-tightest">
                InternUp
              </span>
            </div>
            <div className="flex flex-col items-center gap-2 text-center">
              <h2 className="text-xl font-semibold">Connecting</h2>
              <h2 className="text-xl font-semibold">
                International <span className="text-[#36BA98]">Talent</span>
              </h2>
              <h2 className="text-xl font-semibold">
                with <span className="text-[#36BA98]">Opportunities</span>
              </h2>
            </div>
          </div>
        </div>

        {/* Right section with form */}
        <div className="w-full lg:w-1/2">
          <div className="max-w-md mx-auto">
            <h1 className="text-3xl font-bold mb-4">Tell Us More About You</h1>
            <p className="text-gray-700 mb-8">
              Add optional details to highlight your experience and skills for
              potential employers.
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Resume Upload */}
              <div className="space-y-2">
                <label htmlFor="resume" className="block text-sm font-medium">
                  Resume
                </label>
                <div className="flex items-center justify-between border rounded-lg p-2">
                  <span className="text-sm text-gray-500">
                    {resumeFile
                      ? resumeFile.name
                      : 'Upload Your Resume (PDF only, max 7MB)'}
                  </span>
                  <label htmlFor="resume-upload" className="cursor-pointer">
                    <div className="bg-gray-100 rounded-md p-1">
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                        <polyline points="17 8 12 3 7 8" />
                        <line x1="12" y1="3" x2="12" y2="15" />
                      </svg>
                    </div>
                    <input
                      id="resume-upload"
                      type="file"
                      accept=".pdf"
                      className="hidden"
                      onChange={handleResumeUpload}
                    />
                  </label>
                </div>
              </div>

              {/* Portfolio Link */}
              <div className="space-y-2">
                <label
                  htmlFor="portfolio"
                  className="block text-sm font-medium"
                >
                  Portfolio Link
                </label>
                <Input
                  id="portfolio"
                  type="url"
                  placeholder="Add Your Portfolio Link"
                  value={portfolioLink}
                  onChange={(e) => setPortfolioLink(e.target.value)}
                  className="rounded-lg"
                />
              </div>

              {/* LinkedIn Link */}
              <div className="space-y-2">
                <label htmlFor="linkedin" className="block text-sm font-medium">
                  LinkedIn Link
                </label>
                <Input
                  id="linkedin"
                  type="url"
                  placeholder="Add Your LinkedIn Link"
                  value={linkedinLink}
                  onChange={(e) => setLinkedinLink(e.target.value)}
                  className="rounded-lg"
                />
              </div>

              {/* Bio */}
              <div className="space-y-2">
                <label htmlFor="bio" className="block text-sm font-medium">
                  Bio
                </label>
                <Textarea
                  id="bio"
                  placeholder="Share a little about yourself and what you're looking for!"
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  rows={4}
                  className="rounded-lg"
                />
              </div>

              {/* MBTI */}
              <div className="space-y-2">
                <label htmlFor="mbti" className="block text-sm font-medium">
                  MBTI
                </label>
                <Input
                  id="mbti"
                  type="text"
                  placeholder="Share your MBTI type to help us get to know you more!"
                  value={mbti}
                  onChange={(e) => setMbti(e.target.value)}
                  className="rounded-lg"
                />
              </div>

              <div className="pt-6 flex justify-center">
                <Button
                  type="submit"
                  className="bg-[#36BA98] hover:bg-[#2da888] text-white px-12 py-2 rounded-xl"
                  disabled={isSubmitting || isUploading}
                >
                  {isSubmitting || isUploading ? 'Processing...' : 'Submit'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
