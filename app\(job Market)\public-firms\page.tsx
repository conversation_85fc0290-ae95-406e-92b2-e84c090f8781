import { Suspense } from 'react';
import PublicFirmJobsList from './public-firms-jobs-list';
import JobListSkeleton from '@/components/job-market/job-list-skeleton';
import { StandaloneReferralButton } from '@/components/job-market/standalone-referral-button';
import { PlusCircle, Sparkles } from 'lucide-react';

export default async function PublicFirmsPage({
  searchParams
}: {
  searchParams: {
    page?: string;
    search?: string;
    location?: string;
    term?: string;
    active?: string;
  };
}) {
  const currentPage = Number(searchParams.page) || 1;

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto text-center mb-12 bg-white rounded-xl shadow-md p-8 border border-gray-100">
          <h1 className="text-4xl font-bold text-gray-900 mb-6">
            Exclusive Top Company Opportunities
          </h1>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-8 text-lg">
            At InternUp, we can connect you with top companies. Simply find the
            job you want to apply for on the company's recruitment website and
            share the job link with us. We'll handle the rest to boost your
            chances.
          </p>

          {/* Enhanced prominence for the custom referral section */}
          <div className="relative mt-12 mb-6">
            <div className="absolute -top-5 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-emerald-600 to-green-500 text-white px-4 py-1 rounded-full font-bold text-sm flex items-center gap-1 shadow-md z-10">
              <Sparkles className="h-4 w-4" />
              <span>FEATURED SERVICE</span>
              <Sparkles className="h-4 w-4" />
            </div>
            <div className="flex flex-col justify-center items-center gap-6 p-8 bg-gradient-to-br from-green-50 to-emerald-100 border-2 border-green-400 rounded-xl shadow-xl mx-auto animate-pulse-subtle">
              <h3 className="text-2xl font-bold text-emerald-800">
                Custom Job Referral
              </h3>
              <p className="text-emerald-700 text-center max-w-2xl">
                Found a position elsewhere that you're interested in? Let us
                boost your application with our personalized referral service!
              </p>
              <div className="flex items-center gap-3 bg-white p-4 rounded-lg shadow-md border border-green-300">
                <PlusCircle className="h-6 w-6 text-emerald-600 flex-shrink-0" />
                <span className="font-semibold text-emerald-700">
                  Can't find your desired position?
                </span>
                <div className="transform transition-transform hover:scale-105">
                  <StandaloneReferralButton />
                </div>
              </div>
              <div className="text-sm text-emerald-600 italic mt-2">
                Our referrals significantly boost your chances of getting
                interviews
              </div>
            </div>
          </div>
        </div>

        <div className="mt-12">
          <h2 className="text-2xl font-semibold text-gray-900 mb-8 text-center">
            Available Positions
          </h2>
          <Suspense fallback={<JobListSkeleton count={5} />}>
            <PublicFirmJobsList
              currentPage={currentPage}
              searchParams={searchParams}
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
