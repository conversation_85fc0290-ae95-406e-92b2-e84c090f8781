'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { useRouter } from 'next/navigation';
import { AlertCircle, Loader2, CheckCircle, Clock, Play } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { applyForCapstoneProject } from '@/actions/capstone';
import { v4 as uuidv4 } from 'uuid';
import { useActiveCapstone } from './active-capstone-provider';

interface ApplyProjectButtonProps {
  id?: string;
  projectId: string;
  projectTitle?: string;
  variant?: 'default' | 'outline' | 'secondary';
  className?: string;
  fullWidth?: boolean;
}

export function ApplyProjectButton({
  projectId,
  projectTitle,
  variant = 'default',
  className = '',
  fullWidth = false
}: ApplyProjectButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAppliedToThisProject, setIsAppliedToThisProject] =
    useState<boolean>(false);
  const [applicationStatus, setApplicationStatus] = useState<string | null>(
    null
  );

  const router = useRouter();
  const { toast } = useToast();
  const {
    activeCapstone,
    isLoading: capstoneLoading,
    refetchActiveCapstone,
    user,
    userLoading,
    hasActiveCapstone
  } = useActiveCapstone();

  // Check application status when component mounts or when activeCapstone changes
  useEffect(() => {
    if (!capstoneLoading && activeCapstone) {
      // Check if the active project is this one
      if (activeCapstone.project_id === projectId) {
        setIsAppliedToThisProject(true);
        setApplicationStatus(activeCapstone.status || 'Active');
      } else {
        setIsAppliedToThisProject(false);
      }
    } else if (!capstoneLoading) {
      setIsAppliedToThisProject(false);
      setApplicationStatus(null);
    }
  }, [activeCapstone, capstoneLoading, projectId]);

  const handleApply = async () => {
    if (!user) {
      toast({
        title: 'Sign in required',
        description: 'Please sign in to apply for capstone projects',
        variant: 'destructive'
      });
      setIsDialogOpen(false);
      router.push('/signin');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Apply for the project
      const result = await applyForCapstoneProject({
        id: uuidv4(),
        project_id: projectId,
        capstone_project: projectTitle,
        user_email: user.email,
        email: user.email,
        name:
          user.first_name && user.last_name
            ? `${user.first_name} ${user.last_name}`
            : user.email,
        status: 'Pending',
        creator: user.email
      });

      if (result.success) {
        setHasApplied(true);

        // Refresh the active capstone data
        await refetchActiveCapstone();

        toast({
          title: 'Application submitted!',
          description: 'Your application has been successfully submitted.'
        });

        // Wait for 1.5 seconds to show success state before closing
        setTimeout(() => {
          setIsDialogOpen(false);
          router.push(`/industrial-project/${projectId}#apply`);
          router.refresh();
        }, 1500);
      } else {
        setError(
          result.error || 'Failed to submit application. Please try again.'
        );
        toast({
          title: 'Application failed',
          description:
            result.error || 'Failed to submit application. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (err) {
      console.error('Error applying for project:', err);
      setError('An unexpected error occurred. Please try again.');
      toast({
        title: 'Application failed',
        description: 'An unexpected error occurred. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openDialog = async () => {
    if (userLoading || capstoneLoading) return;

    if (!user) {
      toast({
        title: 'Sign in required',
        description: 'Please sign in to apply for capstone projects',
        variant: 'destructive'
      });
      router.push('/signin');
      return;
    }

    // If user has already applied to a different project, show a message
    if (hasActiveCapstone && !isAppliedToThisProject) {
      toast({
        title: 'Application not allowed',
        description:
          'You already have an active capstone project. Please complete or withdraw from it before applying to a new one.',
        variant: 'destructive'
      });
      return;
    }

    // If user has already applied to this project, don't open dialog
    if (isAppliedToThisProject) {
      return;
    }

    setError(null);
    setHasApplied(false);
    setIsDialogOpen(true);
  };

  // Determine button text and style based on application status
  const getButtonContent = () => {
    if (userLoading || capstoneLoading) {
      return (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Loading...
        </>
      );
    }

    if (isAppliedToThisProject) {
      if (applicationStatus === 'Pending') {
        return (
          <>
            <Clock className="mr-2 h-4 w-4" />
            Application Pending
          </>
        );
      } else if (applicationStatus === 'Started') {
        return (
          <>
            <Play className="mr-2 h-4 w-4" />
            Project In Progress
          </>
        );
      }
      return (
        <>
          <CheckCircle className="mr-2 h-4 w-4" />
          Applied
        </>
      );
    }

    return 'Apply Now';
  };

  // Determine button variant
  const getButtonVariant = () => {
    if (isAppliedToThisProject) {
      if (applicationStatus === 'Pending') {
        return 'outline';
      } else if (applicationStatus === 'Started') {
        return 'secondary';
      }
      return 'secondary';
    }
    return variant;
  };

  const isButtonDisabled =
    userLoading ||
    capstoneLoading ||
    (hasActiveCapstone && !isAppliedToThisProject);

  return (
    <>
      <Button
        className={`${fullWidth ? 'w-full' : ''} ${className} ${
          isAppliedToThisProject && applicationStatus === 'Pending'
            ? 'border-amber-500 text-amber-600 hover:bg-amber-100'
            : isAppliedToThisProject && applicationStatus === 'Started'
              ? 'bg-blue-100 border-blue-500 text-blue-600 hover:bg-blue-200'
              : isAppliedToThisProject
                ? 'bg-green-100 border-green-500 text-green-600 hover:bg-green-200'
                : variant === 'default'
                  ? 'bg-primary hover:bg-primary/90 text-white'
                  : ''
        }`}
        variant={getButtonVariant()}
        onClick={openDialog}
        disabled={isButtonDisabled}
      >
        {getButtonContent()}
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle
              className={`flex items-center gap-2 ${error ? 'text-amber-600' : 'text-primary'}`}
            >
              {error ? (
                <>
                  <AlertCircle className="h-5 w-5" />
                  Warning
                </>
              ) : hasApplied ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Application Submitted
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5" />
                  Important Notice
                </>
              )}
            </DialogTitle>
            <DialogDescription className="text-left pt-2">
              {error ? (
                <p className="text-amber-600 font-medium">{error}</p>
              ) : hasApplied ? (
                <p className="text-green-600">
                  Your application has been successfully submitted! You will be
                  notified of any updates.
                </p>
              ) : (
                <>
                  <p className="mb-4">
                    Students can only have{' '}
                    <span className="font-semibold">
                      one active capstone project
                    </span>{' '}
                    at a time.
                  </p>
                  <p className="mb-4">
                    If you already have an ongoing capstone project, you must
                    complete or withdraw from it before applying to a new one.
                  </p>
                  <p>Do you wish to continue with this application?</p>
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          {!hasApplied && (
            <DialogFooter className="flex sm:justify-between">
              {!error ? (
                <>
                  <DialogClose asChild>
                    <Button
                      variant="outline"
                      type="button"
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    type="button"
                    className="bg-primary hover:bg-primary/90 text-white"
                    onClick={handleApply}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Submitting...
                      </>
                    ) : (
                      'Yes, Continue'
                    )}
                  </Button>
                </>
              ) : (
                <DialogClose asChild className="ml-auto">
                  <Button type="button">Close</Button>
                </DialogClose>
              )}
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
