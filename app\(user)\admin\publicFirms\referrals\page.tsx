'use client';

import { useState, useEffect } from 'react';
import { Search, FileText, UserPlus, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import {
  getAllReferrals,
  getAvailableInsiders,
  assignInsiderToReferral
} from '@/actions/job-market/publicFirm-referrals';
import { bubbleClient } from '@/utils/bubble/client';

interface Insider {
  id: string;
  name: string;
  company: string;
  role: string;
}

interface Referral {
  id: string;
  candidate_id: string;
  candidate_name: string;
  candidate_email: string;
  requested_company: string;
  job_title: string;
  job_listing_url: string;
  candidate_resume: string;
  candidate_linkedin: string;
  creation_date: string;
  status: 'Pending' | 'Assigned' | 'Accepted' | 'Rejected';
  insider_id?: string;
  insider_name?: string;
  insider_email?: string;
}

export default function PublicFirmReferralsPage() {
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedReferral, setSelectedReferral] = useState<Referral | null>(
    null
  );
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false);
  const [availableInsiders, setAvailableInsiders] = useState<Insider[]>([]);
  const [selectedInsiderId, setSelectedInsiderId] = useState<string>('');
  const { toast } = useToast();
  const itemsPerPage = 20;

  useEffect(() => {
    fetchReferrals();
  }, [currentPage, searchTerm]);

  const fetchReferrals = async () => {
    setIsLoading(true);
    try {
      const { data, count } = await getAllReferrals(
        currentPage,
        itemsPerPage,
        searchTerm
      );
      setReferrals(
        data.map((referral) => ({
          ...referral,
          status: referral.status as
            | 'Pending'
            | 'Assigned'
            | 'Accepted'
            | 'Rejected'
        }))
      );
      setTotalCount(count);
      setTotalPages(Math.ceil(count / itemsPerPage));
    } catch (error) {
      console.error('Error fetching referrals:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch referrals. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAvailableInsiders = async (companyName: string) => {
    try {
      const insiders = await getAvailableInsiders(companyName);
      setAvailableInsiders(insiders);
    } catch (error) {
      console.error('Error fetching insiders:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch available insiders.',
        variant: 'destructive'
      });
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Accepted':
        return 'bg-green-500';
      case 'Rejected':
        return 'bg-red-500';
      case 'Assigned':
        return 'bg-blue-500';
      case 'Pending':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const handleAssignInsider = async (referralId: string, insiderId: string) => {
    try {
      const updatedReferral = await assignInsiderToReferral(
        referralId,
        insiderId
      );

      // Update the referral in the local state
      setReferrals((prevReferrals) =>
        prevReferrals.map((ref) =>
          ref.id === referralId
            ? {
                ...ref,
                insider_id: updatedReferral.insider_id,
                insider_name: updatedReferral.insider_name,
                insider_email: updatedReferral.insider_email,
                status: 'Assigned'
              }
            : ref
        )
      );

      toast({
        title: 'Insider Assigned',
        description:
          'The insider has been successfully assigned to this referral request.'
      });

      // --- BEGIN ADDED EMAIL LOGIC ---
      if (updatedReferral.insider_email) {
        const emailSubject = `New Referral Request Assigned: ${updatedReferral.job_title} at ${updatedReferral.requested_company}`;
        const emailBody = `Dear ${updatedReferral.insider_name},

You have been assigned a new referral request for the position of ${updatedReferral.job_title} at ${updatedReferral.requested_company}.

Candidate Name: ${updatedReferral.candidate_name}

Please log in to your InternUp Insider Portal to review the details and take action.

Best regards,
InternUp Admin Team`;

        try {
          await bubbleClient.sendEmail(
            updatedReferral.insider_email,
            emailSubject,
            emailBody
          );
        } catch (emailError) {
          console.error(
            'Failed to send assignment email notification:',
            emailError
          );
          // Optionally show a warning toast that email failed
          toast({
            title: 'Email Warning',
            description:
              'Failed to send notification email to the assigned insider.',
            variant: 'destructive' // Or 'warning'
          });
        }
      } else {
        console.warn(
          'Could not send assignment email: Insider email is missing for referral ID:',
          referralId
        );
      }
      // --- END ADDED EMAIL LOGIC ---

      setIsAssignDialogOpen(false);
      setSelectedInsiderId('');
    } catch (error) {
      console.error('Error assigning insider:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign insider. Please try again.',
        variant: 'destructive'
      });
    }
  };

  useEffect(() => {
    if (isAssignDialogOpen && selectedReferral) {
      fetchAvailableInsiders(selectedReferral.requested_company);
    }
  }, [isAssignDialogOpen, selectedReferral]);

  return (
    <div className="container mx-auto py-8 px-4 h-screen">
      <h1 className="text-3xl font-bold mb-2">Public Firm Referrals</h1>
      <p className="text-gray-600 mb-6">
        Manage referral requests and insider assignments
      </p>

      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-gray-500">{totalCount} results</p>

        <div className="flex gap-4">
          <div className="flex gap-2">
            <Input
              placeholder="Search referral requests"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[400px]"
            />
            <Button onClick={() => fetchReferrals()}>Search</Button>
          </div>
        </div>
      </div>

      <div className="overflow-y-auto h-[calc(80vh)]">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 text-left font-medium text-gray-500">
                Candidate
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Company
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Job Title
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Request Date
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Status
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Assigned Insider
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Documents
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={8} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : referrals.length === 0 ? (
              <tr>
                <td colSpan={8} className="p-4 text-center">
                  No referral requests found
                </td>
              </tr>
            ) : (
              referrals.map((referral) => (
                <tr key={referral.id} className="border-b">
                  <td className="p-3">
                    <div className="space-y-1">
                      <div className="font-medium">
                        {referral.candidate_name}
                      </div>
                      <div className="text-sm text-gray-500">
                        {referral.candidate_email}
                      </div>
                    </div>
                  </td>
                  <td className="p-3">{referral.requested_company}</td>
                  <td className="p-3">{referral.job_title}</td>
                  <td className="p-3">
                    {new Date(referral.creation_date).toLocaleDateString(
                      'en-US',
                      {
                        month: 'short',
                        day: '2-digit',
                        year: '2-digit'
                      }
                    )}
                  </td>
                  <td className="p-3">
                    <Badge
                      className={`${getStatusBadgeColor(referral.status)} text-white`}
                      variant="outline"
                    >
                      {referral.status.charAt(0).toUpperCase() +
                        referral.status.slice(1)}
                    </Badge>
                  </td>
                  <td className="p-3">
                    {referral.insider_id ? (
                      <div className="space-y-1">
                        <div className="font-medium">
                          {referral.insider_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {referral.insider_email}
                        </div>
                      </div>
                    ) : (
                      <span className="text-gray-400">Not assigned</span>
                    )}
                  </td>
                  <td className="p-3">
                    <div className="flex flex-col gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={() =>
                          window.open(referral.candidate_resume, '_blank')
                        }
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Resume
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={() =>
                          window.open(referral.candidate_linkedin, '_blank')
                        }
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        LinkedIn
                      </Button>
                    </div>
                  </td>
                  <td className="p-3">
                    <div className="flex gap-2">
                      <Button
                        onClick={() => {
                          setSelectedReferral(referral);
                          setIsAssignDialogOpen(true);
                        }}
                        size="sm"
                        className="bg-[#118073] text-white hover:bg-[#0e6a5e]"
                      >
                        <UserPlus className="h-4 w-4 mr-1" />
                        Assign
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Assign Insider Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Assign Insider to Referral Request</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-gray-600 mb-4">
              Select an insider from {selectedReferral?.requested_company} to
              handle this referral request
            </p>
            <Select
              value={selectedInsiderId}
              onValueChange={setSelectedInsiderId}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an insider" />
              </SelectTrigger>
              <SelectContent>
                {availableInsiders.map((insider) => (
                  <SelectItem key={insider.id} value={insider.id}>
                    {insider.name} - {insider.role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsAssignDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                if (selectedReferral && selectedInsiderId) {
                  handleAssignInsider(selectedReferral.id, selectedInsiderId);
                }
              }}
              disabled={!selectedInsiderId}
              className="bg-[#118073] text-white hover:bg-[#0e6a5e]"
            >
              Assign Insider
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
