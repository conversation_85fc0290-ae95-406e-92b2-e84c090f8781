// Companies
"use server"

import { createClient } from "@/utils/supabase/server"
import { v4 as uuidv4 } from "uuid"
import { PublicFirmCompany } from "@/types/public-firms"

export async function getPublicFirmCompanies(page = 1, limit = 100): Promise<{ data: PublicFirmCompany[], count: number | null }> {
  const supabase = createClient()
  const offset = (page - 1) * limit
  
  const { data, error, count } = await supabase
    .from("public_firm_companies")
    .select("*", { count: "exact" })
    .order("creation_date", { ascending: false })
    .range(offset, offset + limit - 1)
  
  if (error) {
    console.error("Error fetching public firm companies:", error)
    throw new Error(`Failed to fetch companies: ${error.message}`)
  }
  
  return { data, count }
}

export async function getPublicFirmCompany(id: string): Promise<PublicFirmCompany> {
  const supabase = createClient()
  
  const { data, error } = await supabase.from("public_firm_companies").select("*").eq("id", id).single()
  
  if (error) {
    console.error("Error fetching public firm company:", error)
    throw new Error(`Failed to fetch company: ${error.message}`)
  }
  
  return data
}

export async function createPublicFirmCompany(companyData: Omit<PublicFirmCompany, "id" | "applied" | "creation_date" | "modified_date">): Promise<PublicFirmCompany> {
  const supabase = createClient()
  
  const newCompany: PublicFirmCompany = {
    id: uuidv4(),
    ...companyData,
    applied: [],
    creation_date: new Date().toISOString(),
    modified_date: new Date().toISOString(),
  }
  
  const { data, error } = await supabase.from("public_firm_companies").insert([newCompany]).select()
  
  if (error) {
    console.error("Error creating public firm company:", error)
    throw new Error(`Failed to create company: ${error.message}`)
  }
  
  return data[0]
}

export async function updatePublicFirmCompany(id: string, companyData: Partial<PublicFirmCompany>): Promise<PublicFirmCompany> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from("public_firm_companies")
    .update({
      ...companyData,
      modified_date: new Date().toISOString(),
    })
    .eq("id", id)
    .select()
  
  if (error) {
    console.error("Error updating public firm company:", error)
    throw new Error(`Failed to update company: ${error.message}`)
  }
  
  return data[0]
}

export async function toggleCompanyLiveStatus(id: string, status: string): Promise<PublicFirmCompany> {
  const supabase = createClient()
  
  const { data, error } = await supabase
    .from("public_firm_companies")
    .update({
      live: status,
      modified_date: new Date().toISOString(),
    })
    .eq("id", id)
    .select()
  
  if (error) {
    console.error("Error toggling company status:", error)
    throw new Error(`Failed to update company status: ${error.message}`)
  }
  
  return data[0]
}

export async function deletePublicFirmCompany(id: string): Promise<{ success: boolean }> {
  const supabase = createClient()
  
  const { error } = await supabase.from("public_firm_companies").delete().eq("id", id)
  
  if (error) {
    console.error("Error deleting public firm company:", error)
    throw new Error(`Failed to delete company: ${error.message}`)
  }
  
  return { success: true }
}