'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle2 } from 'lucide-react';
import Image from 'next/image';

// Validation schema for password form
const passwordSchema = z
  .object({
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        'Password must contain at least one uppercase letter, one lowercase letter, and one number'
      ),
    confirmPassword: z.string()
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword']
  });

type PasswordFormValues = z.infer<typeof passwordSchema>;

export default function SetPassword() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const supabase = createClient();

  // Form definition
  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: '',
      confirmPassword: ''
    }
  });

  // Check authentication status on load
  useEffect(() => {
    async function checkAuth() {
      const { data } = await supabase.auth.getSession();
      
      if (!data.session) {
        toast({
          title: 'Authentication Required',
          description: 'Please use the magic link from your email again to access this page.',
          variant: 'destructive'
        });
        router.push('/signin');
        return;
      }
      
      setIsAuthenticated(true);
      setIsLoading(false);
    }
    
    checkAuth();
  }, [router, toast, supabase.auth]);

  // Handle form submission
  async function onSubmit(values: PasswordFormValues) {
    if (isSubmitting) return;
    setIsSubmitting(true);
    
    try {
      // Update password for the authenticated user and set has_set_password flag
      const { error } = await supabase.auth.updateUser({
        password: values.password,
        data: { has_set_password: true }
      });
      
      if (error) throw error;
      
      setIsSuccess(true);
      
      // Get user type to determine redirect
      const { data: { user } } = await supabase.auth.getUser();
      const {data:userData} = await supabase.from('users').select('user_type').eq('email', user?.email).single();
      const userType = userData?.user_type?.toLowerCase() || 'candidate';
      
      const pathMap: Record<string, string> = {
        candidate: '/candidate',
        company: '/company',
        insider: '/insider',
        admin: '/admin'
      };
      
      // Delay redirect to show success message
      setTimeout(() => {
        router.push(pathMap[userType] || '/dashboard');
      }, 2000);
      
    } catch (error) {
      console.error('Error setting password:', error);
      toast({
        title: 'Failed to Set Password',
        description: error instanceof Error ? error.message : 'An unexpected error occurred.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Verifying your access...</span>
      </div>
    );
  }

  if (isSuccess) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center text-center px-4">
        <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
        <h1 className="text-2xl font-bold">Password Set Successfully!</h1>
        <p className="text-muted-foreground mt-2 max-w-md">
          Your password has been successfully set. Redirecting you to your dashboard...
        </p>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="flex min-h-screen bg-background">
      {/* Left Column */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Complete Your Account Setup</h1>
            <p className="text-xl">
              Set up your password to finish migrating your account to our new system.
              This will keep your profile and data secure.
            </p>
          </div>
        </div>
        <div className="relative z-10 text-sm opacity-80">
          © {new Date().getFullYear()} InternUp. All rights reserved.
        </div>
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20" />
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40" />
      </div>

      {/* Right Column */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto flex items-center justify-center">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold">Set Your Password</h2>
            <p className="text-muted-foreground mt-2">
              Choose a secure password for your InternUp account
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        autoComplete="new-password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        autoComplete="new-password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Setting Password...
                  </>
                ) : (
                  'Set Password & Continue'
                )}
              </Button>

              <div className="text-center text-sm text-muted-foreground mt-4">
                Having trouble? Contact{' '}
                <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </a>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
}
