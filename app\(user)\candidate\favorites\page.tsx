'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Heart,
  Search,
  Briefcase,
  Building,
  MapPin,
  Clock,
  DollarSign,
  Calendar,
  ExternalLink,
  X,
  Loader2
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { getFavorites, removeFromFavorites } from '@/actions/favorites';

interface Job {
  id: string;
  title: string;
  company_name?: string;
  location?: string;
  salary_range?: string;
  job_type?: string;
  created_at: string;
  description?: string;
  company_logo?: string;
  application_url?: string;
}

export default function FavoritesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [startupJobs, setStartupJobs] = useState<Job[]>([]);
  const [publicFirmJobs, setPublicFirmJobs] = useState<Job[]>([]);

  useEffect(() => {
    const loadFavorites = async () => {
      try {
        setLoading(true);
        const { startupJobs, publicFirmJobs } = await getFavorites();
        setStartupJobs(startupJobs);
        setPublicFirmJobs(publicFirmJobs);
      } catch (error) {
        console.error('Error loading favorites:', error);
        toast({
          title: 'Error',
          description: 'Failed to load favorites',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    loadFavorites();
  }, []);

  const filteredStartupJobs = startupJobs.filter(
    (job) =>
      job.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.company_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.location?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const filteredPublicFirmJobs = publicFirmJobs.filter(
    (job) =>
      job.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.company_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      job.location?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const allFilteredJobs = [...filteredStartupJobs, ...filteredPublicFirmJobs];

  const handleRemoveFromFavorites = async (
    jobId: string,
    type: 'startup' | 'public-firm'
  ) => {
    try {
      await removeFromFavorites(jobId, type);
      if (type === 'startup') {
        setStartupJobs((prev) => prev.filter((job) => job.id !== jobId));
      } else {
        setPublicFirmJobs((prev) => prev.filter((job) => job.id !== jobId));
      }
      toast({
        title: 'Success',
        description: 'Job removed from favorites'
      });
    } catch (error) {
      console.error('Error removing from favorites:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove from favorites',
        variant: 'destructive'
      });
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading favorites...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Favorites</h1>
        <p className="text-muted-foreground">
          Jobs you've saved to review later
        </p>
      </div>

      <div className="flex items-center">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search favorites..."
            className="pl-10 w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">
            All Favorites ({allFilteredJobs.length})
          </TabsTrigger>
          <TabsTrigger value="startup">
            Startup Jobs ({filteredStartupJobs.length})
          </TabsTrigger>
          <TabsTrigger value="public-firm">
            Public Firm Jobs ({filteredPublicFirmJobs.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6 mt-6">
          {allFilteredJobs.length > 0 ? (
            allFilteredJobs.map((job) => (
              <JobCard
                key={job.id}
                job={job}
                type={
                  startupJobs.some((sj) => sj.id === job.id)
                    ? 'startup'
                    : 'public-firm'
                }
                onRemove={() =>
                  handleRemoveFromFavorites(
                    job.id,
                    startupJobs.some((sj) => sj.id === job.id)
                      ? 'startup'
                      : 'public-firm'
                  )
                }
              />
            ))
          ) : (
            <EmptyState
              title="No favorites found"
              description={
                searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Save jobs to find them here later'
              }
            />
          )}
        </TabsContent>

        <TabsContent value="startup" className="space-y-6 mt-6">
          {filteredStartupJobs.length > 0 ? (
            filteredStartupJobs.map((job) => (
              <JobCard
                key={job.id}
                job={job}
                type="startup"
                onRemove={() => handleRemoveFromFavorites(job.id, 'startup')}
              />
            ))
          ) : (
            <EmptyState
              title="No startup favorites found"
              description={
                searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Save startup jobs to find them here later'
              }
              icon={<Briefcase className="h-10 w-10 text-muted-foreground" />}
            />
          )}
        </TabsContent>

        <TabsContent value="public-firm" className="space-y-6 mt-6">
          {filteredPublicFirmJobs.length > 0 ? (
            filteredPublicFirmJobs.map((job) => (
              <JobCard
                key={job.id}
                job={job}
                type="public-firm"
                onRemove={() =>
                  handleRemoveFromFavorites(job.id, 'public-firm')
                }
              />
            ))
          ) : (
            <EmptyState
              title="No public firm favorites found"
              description={
                searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Save public firm jobs to find them here later'
              }
              icon={<Building className="h-10 w-10 text-muted-foreground" />}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

function JobCard({
  job,
  type,
  onRemove
}: {
  job: Job;
  type: 'startup' | 'public-firm';
  onRemove: () => void;
}) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-md overflow-hidden bg-muted flex items-center justify-center">
              <img
                src={job.company_logo || '/placeholder.svg'}
                alt={`${job.company_name} logo`}
                className="h-full w-full object-cover"
              />
            </div>
            <div>
              <CardTitle className="text-lg">{job.title}</CardTitle>
              <div className="text-sm text-muted-foreground">
                {job.company_name}
              </div>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onRemove}
            className="text-muted-foreground hover:text-destructive"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Remove from favorites</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <p className="text-sm text-muted-foreground mb-4">{job.description}</p>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="flex items-center gap-1 text-muted-foreground">
            <MapPin className="h-4 w-4" />
            <span>{job.location || 'Location not specified'}</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground">
            <DollarSign className="h-4 w-4" />
            <span>{job.salary_range || 'Salary not specified'}</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground">
            <Clock className="h-4 w-4" />
            <span>{job.job_type || 'Job type not specified'}</span>
          </div>
          <div className="flex items-center gap-1 text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Posted {formatDate(job.created_at)}</span>
          </div>
        </div>
      </CardContent>
      <Separator />
      <CardFooter className="p-4 flex justify-between">
        <Badge
          className={
            type === 'startup'
              ? 'bg-[#118073] hover:bg-[#118073]/90'
              : 'bg-secondary'
          }
        >
          {type === 'startup' ? (
            <Briefcase className="h-3 w-3 mr-1" />
          ) : (
            <Building className="h-3 w-3 mr-1" />
          )}
          {type === 'startup' ? 'Startup' : 'Public Firm'}
        </Badge>
        <div className="flex gap-2">
          {job.application_url && (
            <a
              href={job.application_url}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex"
            >
              <Button variant="outline" size="sm">
                <ExternalLink className="h-4 w-4 mr-1" />
                View Details
              </Button>
            </a>
          )}
          <a
            href={`/candidate/${
              type === 'startup' ? 'startup' : 'public-firms'
            }/jobs/${job.id}`}
            className="inline-flex"
          >
            <Button size="sm" className="bg-[#118073] hover:bg-[#118073]/90">
              Apply Now
            </Button>
          </a>
        </div>
      </CardFooter>
    </Card>
  );
}

function EmptyState({
  title,
  description,
  icon = <Heart className="h-10 w-10 text-[#118073]" />
}: {
  title: string;
  description: string;
  icon?: React.ReactNode;
}) {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      <div className="rounded-full bg-[#118073]/10 p-3">{icon}</div>
      <h3 className="mt-4 text-lg font-semibold">{title}</h3>
      <p className="mt-2 text-sm text-muted-foreground max-w-xs">
        {description}
      </p>
      <a href="/candidate/jobs" className="mt-4">
        <Button className="bg-[#118073] hover:bg-[#118073]/90">
          Browse Jobs
        </Button>
      </a>
    </div>
  );
}
