import { bubbleClient } from '@/utils/bubble/client';

/**
 * Test function to verify email sending works with Resend
 * Call this function to test email functionality
 */
export async function testEmailSending() {
  try {
    const testEmail = '<EMAIL>'; // Replace with your test email
    const result = await bubbleClient.sendEmail(
      testEmail,
      'Test Email from InternUp',
      '<h1>Hello!</h1><p>This is a test email sent via Resend from your InternUp application.</p>'
    );
    
    console.log('✅ Email sent successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    throw error;
  }
}
