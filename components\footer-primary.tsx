'use client'
import { useState } from "react"
import React from 'react'
import Image from "next/image";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { createClient } from '@supabase/supabase-js'
import { useToast } from "@/components/ui/use-toast"
import { CoolMode } from "@/components/magicui/cool-mode";

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

const AnimatedUnderline = ({ children, href, className }: { children: React.ReactNode; href: string; className?: string }) => (
  <a 
    href={href} 
    className={`${className} relative overflow-hidden group`}
  >
    {children}
    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-current transform scale-x-0 origin-left transition-transform duration-500 ease-out group-hover:scale-x-100"></span>
  </a>
);

export default function FooterPrimary() {
  const [email, setEmail] = useState('')
  const { toast } = useToast()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const { error } = await supabase
        .from('user_email_list')
        .insert([{ email }])
      
      if (error) throw error

      toast({
        title: "Subscribed! 🎉",
        description: "Thank you for subscribing! You will get an email when the app comes out.",
      })
      setEmail('')
    } catch (error) {
      console.error('Error inserting email:', error)
      toast({
        title: "Error",
        description: "An error occurred. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <footer className="py-14 w-full bg-[#118073]  text-white">
      <div className="container px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 w-2/5">
          <div>
            <ul className="space-y-1">
            <li>
                <AnimatedUnderline href="/about/faq" className="text-white">
                  FAQ
                </AnimatedUnderline>
              </li>
              <li>
                <AnimatedUnderline href="/about/mission" className="text-white">
                  About Us
                </AnimatedUnderline>
              </li>
              <li>
                <AnimatedUnderline href="/about/contact" className="text-white">
                  Contact Us
                </AnimatedUnderline>
              </li>
            </ul>
          </div>
          <div>
            <ul className="space-y-2">
              <li>
                <AnimatedUnderline href="/blogs" className="text-white">
                  Blog
                </AnimatedUnderline>
              </li>
              <li>
                <AnimatedUnderline href="/" className="text-white">
                  Community
                </AnimatedUnderline>
              </li>
            </ul>
          </div>
          <div>
            <ul className="space-y-2">
              <li>
                <AnimatedUnderline href="https://x.com/dao_olivia" className="text-white">
                  X
                </AnimatedUnderline>
              </li>
              <li>
                <AnimatedUnderline href="https://www.linkedin.com/company/internup-org/" className="text-white">
                  LinkedIn
                </AnimatedUnderline>
              </li>
            </ul>
          </div>
          <div>
            
          </div>
        </div>
        <div className="border-t mt-10 pt-6 flex flex-col items-center md:flex-row justify-between">
          <div className="flex items-center space-x-2">
            <Image src="/footer_logo.png" alt="InternUp Logo" height={50} width={180}/>
          </div>
        </div>
      </div>
    </footer>
  );
}

function ArrowRightIcon(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M5 12h14" />
      <path d="m12 5 7 7-7 7" />
    </svg>
  );
}

function LogInIcon(props: any) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
      <polyline points="10 17 15 12 10 7" />
      <line x1="15" x2="3" y1="12" y2="12" />
    </svg>
  );
}
