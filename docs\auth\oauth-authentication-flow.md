# OAuth Authentication Flow

## Overview

This document provides a detailed explanation of the OAuth authentication flow in the InternUp application. The application uses Supabase for OAuth integration while maintaining compatibility with existing Bubble user data.

## Table of Contents

1. [Architecture](#architecture)
2. [OAuth Providers](#oauth-providers)
3. [Sign Up with OAuth](#sign-up-with-oauth)
4. [Sign In with OAuth](#sign-in-with-oauth)
5. [OAuth Callback Handling](#oauth-callback-handling)
6. [User Data Synchronization](#user-data-synchronization)
7. [Error Handling](#error-handling)
8. [Security Considerations](#security-considerations)

## Architecture

The OAuth authentication flow involves several components:

- **Frontend Components**: UI elements for initiating OAuth authentication
- **Supabase Auth**: Handles the OAuth provider integration
- **Callback Handler**: Processes the OAuth response and synchronizes user data
- **Bubble Integration**: Creates corresponding user accounts in Bubble

## OAuth Providers

The application supports the following OAuth providers:

- Google
- LinkedIn
- GitHub

Each provider is configured in Supabase with appropriate client IDs and secrets.

## Sign Up with OAuth

### Process Flow

1. User selects a user type (Candidate or Company)
2. User clicks on an OAuth provider button
3. The application calls `signUpWithOAuth()` function
4. User type is stored in localStorage
5. Supabase initiates the OAuth flow with the selected provider
6. User is redirected to the provider's authentication page
7. After authentication, the provider redirects back to the application's callback URL
8. The callback handler processes the response and creates user accounts

### Code Implementation

```typescript
// From utils/bubble/auth.ts
export async function signUpWithOAuth(provider: string, userType: string) {
  try {
    const supabase = createClient();
    // Ensure the redirectURL is absolute and includes the origin
    const redirectURL = new URL("api/auth/callback", window.location.origin).toString();

    // Store user type in localStorage before redirect
    localStorage.setItem('oauth_user_type', userType);

    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider as Provider,
      options: {
        redirectTo: redirectURL,
        queryParams: {
          user_type: userType, // Pass userType as query param
          flow: 'signup' // This is a sign-up flow
        },
        scopes: provider === 'linkedin' ? 'r_emailaddress r_liteprofile' : undefined,
      },
    });

    if (error) {
      return {
        type: 'error',
        title: 'OAuth Sign Up Failed',
        description: error.message
      };
    }

    return {
      type: 'success',
      title: 'Redirecting to Provider',
      description: 'Please complete authentication with the provider.'
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'OAuth Sign Up Failed',
      description: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}
```

## Sign In with OAuth

### Process Flow

The sign-in process is similar to sign-up but with a different flow parameter:

1. User clicks on an OAuth provider button
2. The application calls `signInWithOAuth()` function
3. User type is stored in localStorage
4. Supabase initiates the OAuth flow with the selected provider
5. User is redirected to the provider's authentication page
6. After authentication, the provider redirects back to the application's callback URL
7. The callback handler processes the response and retrieves the user account

### Code Implementation

```typescript
// From utils/bubble/auth.ts
export async function signInWithOAuth(provider: string, userType: string) {
  try {
    const supabase = createClient();
    // Ensure the redirectURL is absolute and includes the origin
    const redirectURL = new URL("api/auth/callback", window.location.origin).toString();

    // Store user type in localStorage before redirect
    localStorage.setItem('oauth_user_type', userType);

    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider as Provider,
      options: {
        redirectTo: redirectURL,
        queryParams: {
          user_type: userType, // Pass userType as query param
          flow: 'signin' // This is a sign-in flow
        },
        scopes: provider === 'linkedin' ? 'r_emailaddress r_liteprofile' : undefined,
      },
    });

    // Error handling and response
    // ...
  } catch (error) {
    // Error handling
  }
}
```

## OAuth Callback Handling

### Process Flow

1. OAuth provider redirects to `/api/auth/callback` with an authorization code
2. The API route redirects to the client-side handler at `/auth/callback`
3. The client-side handler (`auth-callback-handler.tsx`):
   - Extracts the authorization code and user type from URL parameters
   - Exchanges the code for a session using Supabase
   - Retrieves the user's email from the session
   - Checks if the user exists in Supabase
   - For new users:
     - Generates a random password
     - Creates a user account in Bubble
     - Creates a user record in Supabase with the Bubble user ID
   - For existing users:
     - Retrieves the Bubble user ID
     - Generates a new Bubble token
   - Updates the user record in Supabase
   - Sets authentication tokens
   - Redirects to the appropriate dashboard

### Code Implementation

```typescript
// From app/auth/callback/auth-callback-handler.tsx
useEffect(() => {
  const handleCallback = async () => {
    try {
      // Get code and user type from URL parameters
      const code = searchParams.get('code');
      const userType = searchParams.get('user_type') || localStorage.getItem('oauth_user_type');

      // Set default user type to "candidate" if not specified
      const finalUserType = userType || 'Candidate';

      // Get the authentication flow (signin/signup)
      const flow = searchParams.get('flow') || 'signup';

      // Exchange the code for a session
      const { data, error: sessionError } = await supabase.auth.getUser();

      if (sessionError || !data?.user) {
        throw new Error(sessionError?.message || "Could not sign you in. Please try again.");
      }

      const email = data.user.email;
      if (!email) {
        throw new Error("We couldn't get your email address. Please try another sign-in method.");
      }

      // Check if user exists in Supabase
      const { data: existingUser } = await supabase
        .from('users')
        .select('*')
        .eq('email', email)
        .single();

      let bubbleUserId: string;
      let bubbleToken: string = '';

      if (existingUser) {
        // User exists - use their existing bubble_user_id and user_type
        bubbleUserId = existingUser.id;

        // Generate a new Bubble token client-side
        bubbleToken = `oauth-${Date.now()}-${bubbleUserId}-${Math.random().toString(36).substring(2)}`;
      } else {
        // User doesn't exist - create them in Bubble first (sign-up flow)
        // Generate a random secure password for Bubble
        const randomPassword = Array(20)
          .fill('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*')
          .map(x => x[Math.floor(Math.random() * x.length)])
          .join('');

        // Create the user in Bubble
        const bubbleResponse = await bubbleClient.signUp(email, randomPassword, finalUserType);

        if (bubbleResponse && bubbleResponse.status !== 'success') {
          throw new Error('Failed to create user in Bubble');
        }

        bubbleUserId = bubbleResponse.response.user_id;
        bubbleToken = bubbleResponse.response.token;
      }

      // Store the tokens
      localStorage.setItem('bubble_auth_token', bubbleToken);
      localStorage.setItem('bubble_user_id', bubbleUserId);

      // Update the user in Supabase
      const { error: upsertError } = await supabase
        .from('users')
        .upsert({
          id: bubbleUserId,
          email: email,
          user_type: existingUser?.user_type || finalUserType,
          avatar_url: data.user.user_metadata?.avatar_url || '',
          verified: true, // OAuth users are pre-verified
          updated_at: new Date().toISOString(),
        }, { onConflict: 'id' });

      // Determine redirect path based on user type
      // Redirect user
      // ...
    } catch (error) {
      // Error handling
    }
  };

  handleCallback();
}, []);
```

## User Data Synchronization

### Bubble to Supabase Synchronization

- When a user signs up with OAuth, a corresponding account is created in Bubble
- The Bubble user ID is stored in the Supabase user record
- This allows the application to retrieve user data from both systems

### User Type Handling

- User type (Candidate or Company) is passed through the OAuth flow
- For new users, the selected user type is used
- For existing users, their current user type is preserved
- Admin users always retain their admin status

## Error Handling

The OAuth flow includes comprehensive error handling:

1. **Provider Errors**: Errors from the OAuth provider are captured and displayed to the user
2. **Session Exchange Errors**: Errors during code-to-session exchange are handled
3. **User Creation Errors**: Errors during user creation in Bubble or Supabase are handled
4. **Network Errors**: Timeouts and connection issues are handled gracefully

Error messages are displayed to the user with appropriate guidance on how to proceed.

## Security Considerations

### Token Security

- OAuth tokens are handled by Supabase and not exposed to the client
- Bubble tokens are stored in localStorage with appropriate security measures
- Session cookies are secure and HTTP-only

### User Verification

- OAuth users are automatically verified (no email verification required)
- This is because OAuth providers have already verified the user's email

### Password Security

- For OAuth users, a random strong password is generated for the Bubble account
- This password is not stored or accessible to the user or application
- The user authenticates through the OAuth provider, not with this password

---

This documentation provides a detailed explanation of the OAuth authentication flow. For implementation details, refer to the source code in the following files:

- `utils/bubble/auth.ts`: OAuth authentication functions
- `app/auth/callback/auth-callback-handler.tsx`: OAuth callback handler
- `app/api/auth/callback/route.ts`: API route for OAuth callback
- `app/(auth_forms)/signup/page.tsx`: OAuth sign-up buttons
- `app/(auth_forms)/signin/page.tsx`: OAuth sign-in buttons
