'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getActiveCapstoneUserByEmail } from '@/actions/capstone';
import type { CapstoneUser } from '@/types/capstone-user';
import { User } from '@supabase/supabase-js';

// Create context
interface ActiveCapstoneContextType {
  activeCapstone: CapstoneUser | null;
  isLoading: boolean;
  refetchActiveCapstone: () => Promise<void>;
  user: User | null;
  userLoading: boolean;
  hasActiveCapstone: boolean;
}

const ActiveCapstoneContext = createContext<ActiveCapstoneContextType>({
  activeCapstone: null,
  isLoading: true,
  refetchActiveCapstone: async () => {},
  user: null,
  userLoading: false,
  hasActiveCapstone: false
});

// Provider component
export const ActiveCapstoneProvider = ({
  children,
  user,
  loading
}: {
  children: React.ReactNode;
  user: User | null;
  loading: boolean;
}) => {
  const [activeCapstone, setActiveCapstone] = useState<CapstoneUser | null>(
    null
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasActiveCapstone, setHasActiveCapstone] = useState<boolean>(false);

  const fetchActiveCapstone = async () => {
    if (!user?.email) {
      setActiveCapstone(null);
      setHasActiveCapstone(false);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const capstone = await getActiveCapstoneUserByEmail(user.email);
      setActiveCapstone(capstone);
      setHasActiveCapstone(!!capstone);
    } catch (error) {
      console.error('Error fetching active capstone:', error);
      setHasActiveCapstone(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch active capstone when user is loaded
  useEffect(() => {
    if (!loading) {
      fetchActiveCapstone();
    }
  }, [user, loading]);

  return (
    <ActiveCapstoneContext.Provider
      value={{
        activeCapstone,
        isLoading,
        refetchActiveCapstone: fetchActiveCapstone,
        user,
        userLoading: loading,
        hasActiveCapstone
      }}
    >
      {children}
    </ActiveCapstoneContext.Provider>
  );
};

// Custom hook for using the context
export const useActiveCapstone = () => useContext(ActiveCapstoneContext);
