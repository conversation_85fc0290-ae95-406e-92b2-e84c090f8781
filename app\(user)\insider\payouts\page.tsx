'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useUser';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  Loader2,
  ExternalLink,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  CreditCard,
  ArrowRight,
  BarChart4,
  Wallet
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  getInsiderPayouts,
  upsertInsiderPaymentAccount,
  createStripeConnectAccount,
  getInsiderPaymentAccount
} from '@/actions/insider-payouts';
import { getPaymentAccountForUser } from './utils';
import { InsiderPayout, InsiderPaymentAccount } from '@/types/insider-payouts';
import { formatCurrency } from '@/utils/helpers';

// Form schema for payment account
const paymentAccountSchema = z.object({
  payment_method: z.enum(['stripe'])
});

export default function InsiderPayoutsPage() {
  const { user, loading: userLoading } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [paymentAccount, setPaymentAccount] = useState<InsiderPaymentAccount | null>(null);
  const [payouts, setPayouts] = useState<InsiderPayout[]>([]);
  const [totalEarnings, setTotalEarnings] = useState(0);
  const [availableEarnings, setAvailableEarnings] = useState(0);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false);
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const router = useRouter();

  // Initialize form
  const paymentAccountForm = useForm<z.infer<typeof paymentAccountSchema>>({
    resolver: zodResolver(paymentAccountSchema),
    defaultValues: {
      payment_method: 'stripe'
    }
  });

  // Load insider data
  useEffect(() => {
    async function loadInsiderData() {
      if (!user || !user.id || userLoading) return;

      try {
        setIsLoading(true);

        // Get user email and ID
        const userEmail = user.email;
        const userId = user.id;

        // Use our custom function that properly handles the relationship
        // between users, insiders, and payment accounts
        let accountData = null;

        if (userEmail) {
          console.log('Looking up payment account by email:', userEmail);
          accountData = await getPaymentAccountForUser(userEmail, true);
        }

        // If that fails, try with user ID as fallback
        if (!accountData && userId) {
          console.log('No account found with email, trying with user ID');
          accountData = await getPaymentAccountForUser(userId, false);
        }

        // Set the payment account if we found one
        if (accountData) {
          console.log('Setting payment account:', accountData);
          setPaymentAccount(accountData);
          if (accountData.payment_method) {
            paymentAccountForm.setValue('payment_method', accountData.payment_method as any);
          }
        }

        // Load payouts - if no insider data is found yet, this might fail but that's expected
        // for new insiders connecting their account for the first time
        try {
          const payoutsData = await getInsiderPayouts(userId);
          setPayouts(payoutsData.data);
        } catch (error) {
          console.log('No payouts data found yet - this is normal for new insiders');
          setPayouts([]);
        }

        // Get earnings data from user metadata
        const insiderData = await fetch(`/api/insider/earnings?email=${encodeURIComponent(user.email)}`).then(res => res.json());
        setTotalEarnings(insiderData.earnings_paid + insiderData.earnings_available || 0);
        setAvailableEarnings(insiderData.earnings_available || 0);
      } catch (error) {
        console.error('Error loading insider data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load payout information. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    }

    loadInsiderData();
  }, [user, userLoading, toast, paymentAccountForm, searchParams]);

  // Handle Stripe Connect account creation
  const handleConnectStripe = async () => {
    if (!user || !user.id) return;

    try {
      setIsConnecting(true);

      // Get user details
      const firstName = user.user_metadata?.firstName || 'Insider';
      const lastName = user.user_metadata?.lastName || 'User';
      const email = user.email || '';

      // Create Stripe Connect account
      const result = await createStripeConnectAccount(
        user.id,
        email,
        firstName,
        lastName
      );

      // Redirect to Stripe Connect onboarding
      // Include the account ID in the return URL so we can sync it directly
      const returnUrl = `${process.env.NEXT_PUBLIC_APP_URL}/insider/payouts?success=true&accountId=${result.accountId}`;
      const refreshUrl = `${process.env.NEXT_PUBLIC_APP_URL}/insider/payouts?refresh=true`;

      // Call our API endpoint to create the account link
      const response = await fetch('/api/insider/stripe-account-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          accountId: result.accountId,
          refreshUrl,
          returnUrl
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create Stripe account link');
      }

      const data = await response.json();
      window.location.href = data.url;
    } catch (error) {
      console.error('Error connecting Stripe account:', error);
      toast({
        title: 'Error',
        description: 'Failed to connect Stripe account. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle Stripe Connect account management
  const handleManageStripeAccount = async () => {
    if (!paymentAccount?.stripe_account_id) return;

    try {
      setIsConnecting(true);

      // Call our API endpoint to create the login link
      const response = await fetch('/api/insider/stripe-login-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          accountId: paymentAccount.stripe_account_id
        })
      });

      if (!response.ok) {
        throw new Error('Failed to create Stripe login link');
      }

      const data = await response.json();

      // Redirect to Stripe Connect dashboard
      window.location.href = data.url;
    } catch (error) {
      console.error('Error creating login link:', error);
      toast({
        title: 'Error',
        description: 'Failed to access Stripe account. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsConnecting(false);
    }
  };

  // Removed payout settings form submission handler

  // Handle payment account form submission
  const onSubmitPaymentAccount = async (data: z.infer<typeof paymentAccountSchema>) => {
    if (!user || !user.id) return;

    try {
      setIsUpdatingSettings(true);

      // Update payment account
      await upsertInsiderPaymentAccount(user.id, data);

      toast({
        title: 'Success',
        description: 'Payment method updated successfully.',
        variant: 'default'
      });

      // Refresh data
      const accountData = await getInsiderPaymentAccount(user.id);
      setPaymentAccount(accountData);

      // If Stripe is selected and no account exists, initiate Connect onboarding
      if (data.payment_method === 'stripe' && !accountData?.stripe_account_id) {
        handleConnectStripe();
      }
    } catch (error) {
      console.error('Error updating payment method:', error);
      toast({
        title: 'Error',
        description: 'Failed to update payment method. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsUpdatingSettings(false);
    }
  };

  // Manually sync Stripe Connect account with our database
  const syncStripeAccountWithDatabase = async (stripeAccountId: string) => {
    if (!user || !user.id || !user.email) return null;

    try {
      console.log(`Manually syncing Stripe account ${stripeAccountId} for user ${user.id}`);

      // Get full account details from Stripe
      const response = await fetch('/api/insider/stripe-account-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          userId: user.id,
          userEmail: user.email,
          stripeAccountId: stripeAccountId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to sync Stripe account with database');
      }

      const result = await response.json();
      console.log('Sync result:', result);

      return result.account;
    } catch (error) {
      console.error('Error syncing Stripe account with database:', error);
      return null;
    }
  };

  // Check for Stripe Connect success/refresh
  useEffect(() => {
    const success = searchParams.get('success');
    const refresh = searchParams.get('refresh');
    const accountId = searchParams.get('accountId');

    if (success === 'true' && user?.id) {
      console.log('Detected successful Stripe Connect onboarding');
      toast({
        title: 'Success',
        description: 'Your Stripe account has been connected successfully.',
        variant: 'default'
      });

      // Force refresh all data with a slight delay to ensure webhook processing is complete
      const refreshData = async () => {
        try {
          console.log('Refreshing insider data after successful onboarding');

          // First, if we have the accountId in the URL, directly sync it
          if (accountId) {
            console.log(`Found accountId ${accountId} in URL, syncing directly`);
            const syncedAccount = await syncStripeAccountWithDatabase(accountId);
            if (syncedAccount) {
              setPaymentAccount(syncedAccount);
              if (syncedAccount.payment_method) {
                paymentAccountForm.setValue('payment_method', syncedAccount.payment_method as any);
              }

              // Don't need to do further lookups
              return;
            }
          }

          // Use our custom function that properly handles the relationship
          // between users, insiders, and payment accounts
          let accountData = null;

          if (user.email) {
            console.log('Looking up payment account by email:', user.email);
            accountData = await getPaymentAccountForUser(user.email, true);
          }

          // If that fails, try with user ID as fallback
          if (!accountData && user.id) {
            console.log('No account found with email, trying with user ID');
            accountData = await getPaymentAccountForUser(user.id, false);
          }

          // Set the payment account if we found one
          if (accountData) {
            console.log('Setting payment account:', accountData);
            setPaymentAccount(accountData);
            if (accountData.payment_method) {
              paymentAccountForm.setValue('payment_method', accountData.payment_method as any);
            }
          } else {
            console.log('No payment account found - user needs to complete onboarding');
          }
        } catch (error) {
          console.error('Error refreshing data after onboarding:', error);
        }
      };

      // Delay the refresh slightly to ensure webhook processing is complete
      setTimeout(refreshData, 1000);

      // Remove query params after a short delay
      setTimeout(() => {
        router.replace('/insider/payouts');
      }, 500);

    } else if (refresh === 'true' && user?.id) {
      // User needs to complete onboarding
      handleConnectStripe();
    }
  }, [searchParams, user, toast, router, paymentAccountForm]);

  if (isLoading || userLoading) {
    return (
      <div className="container mx-auto py-12 flex justify-center items-center">
        <Loader2 className="h-8 w-8 animate-spin text-[#118073]" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Payout Dashboard</h1>
        <p className="text-muted-foreground">
          Manage your referral earnings and receive payments quickly and securely
        </p>
      </div>

      {/* Main earnings card with prominent display */}
      <Card className="bg-gradient-to-r from-[#118073]/10 to-[#118073]/5 border-[#118073]/20">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Wallet className="h-6 w-6 text-[#118073]" />
                <h2 className="text-2xl font-semibold">Your Earnings</h2>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Available for payout:</span>
                  <span className="text-2xl font-bold text-[#118073]">
                    {formatCurrency(availableEarnings / 100)}
                  </span>
                </div>
                <Progress
                  value={(availableEarnings / (totalEarnings || 1)) * 100}
                  className="h-2 bg-gray-200"
                />
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total earnings:</span>
                  <span className="text-lg font-bold">
                    {formatCurrency(totalEarnings / 100)}
                  </span>
                </div>
              </div>

              <div className="flex gap-2 pt-2">
                <Link href="/insider/referral-requests" className="flex-1">
                  <Button
                    variant="outline"
                    className="w-full"
                  >
                    <BarChart4 className="h-4 w-4 mr-2" />
                    View Referrals
                  </Button>
                </Link>
                {availableEarnings > 0 && paymentAccount?.account_status === 'active' && (
                  <Button
                    variant="default"
                    className="flex-1 bg-[#118073] hover:bg-[#118073]/90"
                  >
                    <DollarSign className="h-4 w-4 mr-2" />
                    Request Payout
                  </Button>
                )}
              </div>
            </div>

            <div className="space-y-4 border-l-0 md:border-l border-gray-200 md:pl-6">
              <div className="flex items-center gap-2">
                <CreditCard className="h-6 w-6 text-[#118073]" />
                <h2 className="text-2xl font-semibold">Payment Status</h2>
              </div>

              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  {paymentAccount?.account_status === 'active' ? (
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-amber-500 flex-shrink-0" />
                  )}
                  <div className="space-y-1">
                    <div className="font-medium">
                      {paymentAccount?.account_status === 'active'
                        ? 'Your account is ready to receive payments'
                        : paymentAccount?.account_status === 'pending'
                        ? 'Your account is pending verification'
                        : 'Payment account not set up'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {paymentAccount?.account_status === 'active'
                        ? 'You can request payouts at any time'
                        : paymentAccount?.account_status === 'pending'
                        ? 'Complete verification to receive payments'
                        : 'Set up your payment method to receive earnings'}
                    </div>
                  </div>
                </div>

                {paymentAccount?.stripe_account_id ? (
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleManageStripeAccount}
                    disabled={isConnecting}
                  >
                    {isConnecting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <ExternalLink className="h-4 w-4 mr-2" />
                    )}
                    Manage Payment Account
                  </Button>
                ) : (
                  <Button
                    variant="default"
                    className="w-full bg-[#118073] hover:bg-[#118073]/90"
                    onClick={handleConnectStripe}
                    disabled={isConnecting}
                  >
                    {isConnecting ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <CreditCard className="h-4 w-4 mr-2" />
                    )}
                    Set Up Payment Method
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Removed duplicate earnings and payment method cards */}

      <div className="w-full">
        <div className="flex items-center gap-2 mb-4">
          <Clock className="h-5 w-5 text-[#118073]" />
          <h2 className="text-xl font-semibold">Payout History</h2>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-[#118073]" />
                  <CardTitle>Payout History</CardTitle>
                </div>
                {payouts.length > 0 && (
                  <span className="text-sm text-muted-foreground">
                    {payouts.length} transaction{payouts.length !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
              <Separator className="my-2" />
            </CardHeader>
            <CardContent>
              {payouts.length === 0 ? (
                <div className="text-center py-8 space-y-3">
                  <DollarSign className="h-10 w-10 text-gray-300 mx-auto" />
                  <div>
                    <p className="font-medium text-gray-600">No payouts yet</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Your earnings will be paid out when you request them
                    </p>
                  </div>
                  {availableEarnings > 0 && paymentAccount?.account_status === 'active' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                    >
                      <DollarSign className="h-4 w-4 mr-2" />
                      Request a Payout
                    </Button>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b text-xs text-muted-foreground">
                        <th className="text-left py-2 px-4 font-medium">Date</th>
                        <th className="text-left py-2 px-4 font-medium">Amount</th>
                        <th className="text-left py-2 px-4 font-medium">Method</th>
                        <th className="text-left py-2 px-4 font-medium">Status</th>
                        <th className="text-left py-2 px-4 font-medium">Description</th>
                      </tr>
                    </thead>
                    <tbody>
                      {payouts.map((payout) => (
                        <tr key={payout.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 text-sm">
                            {new Date(payout.created_at).toLocaleDateString(undefined, {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}
                          </td>
                          <td className="py-3 px-4 font-medium text-[#118073]">
                            {formatCurrency(payout.amount / 100)}
                          </td>
                          <td className="py-3 px-4 text-sm">
                            {payout.payout_method === 'stripe'
                              ? 'Stripe'
                              : payout.payout_method === 'bank_transfer'
                              ? 'Bank Transfer'
                              : payout.payout_method}
                          </td>
                          <td className="py-3 px-4">
                            <span
                              className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                payout.status === 'completed'
                                  ? 'bg-green-100 text-green-800'
                                  : payout.status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : payout.status === 'processing'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-red-100 text-red-800'
                              }`}
                            >
                              {payout.status.charAt(0).toUpperCase() + payout.status.slice(1)}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-sm">
                            {payout.description || 'Referral earnings payout'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
