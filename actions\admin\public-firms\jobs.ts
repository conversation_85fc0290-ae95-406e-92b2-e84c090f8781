"use server"

import { createClient } from "@/utils/supabase/server"
import { v4 as uuidv4 } from "uuid"
import { PublicFirmJob, PaginatedResponse, CreatePublicFirmJobInput } from "@/types/public-firms"

// Jobs
export async function getPublicFirmJobs(page = 1, limit = 100): Promise<PaginatedResponse<PublicFirmJob>> {
  const supabase = createClient()
  const offset = (page - 1) * limit

  // Join with companies table to get the company logo
  const { data, error, count } = await supabase
    .from("public_firm_jobs")
    .select(`
      *,
      company_info:public_firm_companies!company(logo, name)
    `, { count: "exact" })
    .order("creation_date", { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error("Error fetching public firm jobs:", error)
    throw new Error(`Failed to fetch jobs: ${error.message}`)
  }

  // Process data to use company logo if job doesn't have its own
  const processedData = data.map(job => {
    // Use company logo if available and job doesn't have its own
    const companyLogo = job.company_info?.logo || null
    
    return {
      ...job,
      logo: job.logo || companyLogo,
      applied_count: job.application_count?.[0]?.count || 0
    }
  })

  return { data: processedData, count }
}

export async function getPublicFirmJob(id: string): Promise<PublicFirmJob> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from("public_firm_jobs")
    .select(`
      *,
      company_info:public_firm_companies!company(logo, name)
    `)
    .eq("id", id)
    .single()

  if (error) {
    console.error("Error fetching public firm job:", error)
    throw new Error(`Failed to fetch job: ${error.message}`)
  }

  // Use company logo if available and job doesn't have its own
  const companyLogo = data.company_info?.logo || null
  
  return {
    ...data,
    logo: data.logo || companyLogo
  }
}

export async function createPublicFirmJob(jobData: CreatePublicFirmJobInput): Promise<PublicFirmJob> {
  const supabase = createClient()

  // Check if company exists, if not create it
  if (jobData.company) {
    const { data: existingCompany } = await supabase
      .from("public_firm_companies")
      .select("name, logo")
      .eq("name", jobData.company)
      .single()

    if (!existingCompany) {
      // Create the company if it doesn't exist
      await supabase.from("public_firm_companies").insert([{
        id: uuidv4(),
        name: jobData.company,
        logo: jobData.logo, // Use the uploaded logo for the company too
        creation_date: new Date().toISOString(),
        modified_date: new Date().toISOString(),
        live: "yes"
      }])
    } else if (!jobData.logo && existingCompany.logo) {
      // Use the company logo if job doesn't have one
      jobData.logo = existingCompany.logo
    }
  }

  const newJob: PublicFirmJob = {
    id: uuidv4(),
    ...jobData,
    applied_user: [],
    favored: [],
    creation_date: new Date().toISOString(),
    modified_date: new Date().toISOString(),
  }

  const { data, error } = await supabase.from("public_firm_jobs").insert([newJob]).select()

  if (error) {
    console.error("Error creating public firm job:", error)
    throw new Error(`Failed to create job: ${error.message}`)
  }

  return data[0]
}

export async function updatePublicFirmJob(id: string, jobData: Partial<PublicFirmJob>): Promise<PublicFirmJob> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from("public_firm_jobs")
    .update({
      ...jobData,
      modified_date: new Date().toISOString(),
    })
    .eq("id", id)
    .select()

  if (error) {
    console.error("Error updating public firm job:", error)
    throw new Error(`Failed to update job: ${error.message}`)
  }

  return data[0]
}

export async function toggleJobLiveStatus(id: string, status: string): Promise<PublicFirmJob> {
  const supabase = createClient()

  const { data, error } = await supabase
    .from("public_firm_jobs")
    .update({
      live: status,
      modified_date: new Date().toISOString(),
    })
    .eq("id", id)
    .select()

  if (error) {
    console.error("Error toggling job status:", error)
    throw new Error(`Failed to update job status: ${error.message}`)
  }

  return data[0]
}

export async function deletePublicFirmJob(id: string): Promise<{ success: boolean }> {
  const supabase = createClient()

  const { error } = await supabase.from("public_firm_jobs").delete().eq("id", id)

  if (error) {
    console.error("Error deleting public firm job:", error)
    throw new Error(`Failed to delete job: ${error.message}`)
  }

  return { success: true }
}

// // Add a function to count applications for a specific job
// export async function getJobApplicationCount(jobLink: string): Promise<number> {
//   const supabase = createClient()
  
//   const { count, error } = await supabase
//     .from("public_firm_applications")
//     .select("*", { count: "exact" })
//     .eq("job_link", jobLink)
  
//   if (error) {
//     console.error("Error counting job applications:", error)
//     return 0
//   }
  
//   return count || 0
// }