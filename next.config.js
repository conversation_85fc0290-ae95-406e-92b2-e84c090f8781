/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Add any custom webpack config here if needed
    return config;
  },
  // Other Next.js config options
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: [
      'avatars.githubusercontent.com',
      '127.0.0.1',
      'llmgwifgtszjgjlzlwjq.supabase.co',
      'f3f8c3f7c334ada9407b72e0028b9909.cdn.bubble.io',
      'cbjkjobahufvdjnhffkp.supabase.co',
      's3-alpha-sig.figma.com'
    ]
  }
};

module.exports = nextConfig;
