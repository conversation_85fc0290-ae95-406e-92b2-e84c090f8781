'use server';

import { createClient } from '@/utils/supabase/client';
import { v4 as uuidv4 } from 'uuid';
// import { revalidatePath } from "next/cache"
import type { Blog, BlogFormData, BlogsQueryParams } from '@/types/blog';
import PaginatedResponse from '@/types/pagination';

// Blog related actions
export async function getBlogs({
  page = 1,
  searchTerm = ''
}: BlogsQueryParams): Promise<PaginatedResponse<Blog>> {
  const supabase = createClient();
  const pageSize = 10;
  const startIndex = (page - 1) * pageSize;

  let query = supabase.from('blogs').select('*', { count: 'exact' });

  if (searchTerm) {
    query = query.or(
      `title.ilike.%${searchTerm}%,block1.ilike.%${searchTerm}%`
    );
  }

  const { data, error, count } = await query
    .order('creation_date', { ascending: false })
    .range(startIndex, startIndex + pageSize - 1);

  if (error) {
    console.error('Error fetching blogs:', error);
    throw new Error('Failed to fetch blogs');
  }

  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data as Blog[],
    totalPages,
    totalCount: count ?? 0
  };
}

export async function getBlog(id: string): Promise<Blog> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('blogs')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching blog:', error);
    throw new Error('Failed to fetch blog');
  }

  return data as Blog;
}

export async function createBlog(blogData: BlogFormData): Promise<Blog> {
  const supabase = createClient();

  // Generate a slug from the title
  const slug = blogData.title
    .toLowerCase()
    .replace(/[^\w\s]/gi, '')
    .replace(/\s+/g, '-');

  // Generate a UUID for the id
  const id = uuidv4();

  const { data, error } = await supabase
    .from('blogs')
    .insert({
      id,
      ...blogData,
      slug,
      creation_date: new Date().toISOString(),
      modified_date: new Date().toISOString()
    })
    .select();

  if (error) {
    console.error('Error creating blog:', error);
    throw new Error('Failed to create blog');
  }

  // revalidatePath("/startup/blogs")
  return data[0] as Blog;
}

export async function updateBlog(
  id: string,
  blogData: Partial<BlogFormData>
): Promise<Blog> {
  const supabase = createClient();

  const updatedData: any = {
    ...blogData,
    modified_date: new Date().toISOString()
  };

  if (blogData.title) {
    updatedData.slug = blogData.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-');
  }

  const { data, error } = await supabase
    .from('blogs')
    .update(updatedData)
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating blog:', error);
    throw new Error('Failed to update blog');
  }

  // revalidatePath("/startup/blogs")
  return data[0] as Blog;
}

export async function deleteBlog(id: string): Promise<{ success: boolean }> {
  const supabase = createClient();

  const { error } = await supabase.from('blogs').delete().eq('id', id);

  if (error) {
    console.error('Error deleting blog:', error);
    throw new Error('Failed to delete blog');
  }

  // revalidatePath("/startup/blogs")
  return { success: true };
}
