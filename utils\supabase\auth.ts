import { getURL } from '@/lib/utils';
import { createClient } from '@/utils/supabase/client';
import { Provider, AuthError } from '@supabase/supabase-js';

// Define consistent response type
export type AuthResponse = {
  type: 'success' | 'error' | 'info';
  title: string;
  description: string;
  redirectPath?: string;
  data?: any;
};

/**
 * Sign up with email and password
 * @param formData Form data containing email, password and optional user data
 * @returns AuthResponse with result of the operation
 */
export async function signUp(formData: FormData) {
  try {
    const email = String(formData.get('email') || '').trim();
    const password = String(formData.get('password') || '').trim();
    const userType = String(formData.get('userType') || '').trim();

    if (!email || !password || !userType) {
      return {
        type: 'error',
        title: 'Missing Fields',
        description: 'Email, password, and user type are required.'
      };
    }

    const supabase = createClient();

    // First create the auth account
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: getURL('/auth/callback'),
        data: {
          user_type: userType // Store user type in auth metadata
        }
      }
    });

    if (authError || !authData?.user) {
      return {
        type: 'error',
        title: 'Sign Up Failed',
        description: authError?.message || 'Could not create account.'
      };
    }

    // Then create the user record in the users table
    const { error: userError } = await supabase.from('users').insert({
      id: authData.user.id,
      email,
      user_type: userType,
      migration_status: 'migrated',
      verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    if (userError) {
      // If user record creation fails, we should clean up the auth account
      await supabase.auth.admin.deleteUser(authData.user.id);

      return {
        type: 'error',
        title: 'Sign Up Failed',
        description: 'Could not create user profile. Please try again.'
      };
    }

    return {
      type: 'info',
      title: 'Check Your Email',
      description: 'We have sent you a confirmation email.'
    };
  } catch (error) {
    console.error('Error in signUp:', error);
    return {
      type: 'error',
      title: 'Sign Up Failed',
      description: 'An unexpected error occurred. Please try again.'
    };
  }
}

/**
 * Sign in with email and password
 * @param formData Form data containing email and password
 * @returns AuthResponse with result of the operation
 */
export async function signInWithPassword(
  formData: FormData
): Promise<AuthResponse> {
  try {
    if (!formData) {
      return {
        type: 'error',
        title: 'Sign In Failed',
        description: 'Missing form data'
      };
    }

    const email = String(formData.get('email') || '').trim();
    const password = String(formData.get('password') || '').trim();

    if (!email || !password) {
      return {
        type: 'error',
        title: 'Sign In Failed',
        description: 'Email and password are required'
      };
    }

    const supabase = createClient();

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      throw error;
    }

    if (!data?.user?.id) {
      return {
        type: 'error',
        title: 'Sign In Failed',
        description: 'User not found'
      };
    }

    // Get user type from metadata
    const userType =
      data.user.user_metadata?.user_type?.toLowerCase() || 'candidate';

    // Determine redirect path based on user type
    let redirectPath = '/dashboard';
    switch (userType) {
      case 'candidate':
        redirectPath = '/candidate';
        break;
      case 'company':
        redirectPath = '/company';
        break;
      case 'insider':
        redirectPath = '/insider';
        break;
      case 'admin':
        redirectPath = '/admin';
        break;
    }

    return {
      type: 'success',
      title: 'Signed In',
      description: 'You are now signed in.',
      redirectPath,
      data: { user: data.user }
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = 'An unexpected error occurred';

    if (authError?.message) {
      description = authError.message;
    }

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 400) {
      description = 'Invalid email or password';
    } else if (authError?.status === 422) {
      description = 'Email format is invalid';
    }

    return {
      type: 'error',
      title: 'Sign In Failed',
      description
    };
  }
}

/**
 * Sign out the current user
 * @returns AuthResponse with result of the operation
 */
export async function signOut(): Promise<AuthResponse> {
  try {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Signed Out',
      description: 'You have been successfully signed out.',
      redirectPath: '/'
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      type: 'error',
      title: 'Sign Out Failed',
      description: authError?.message || 'You could not be signed out.'
    };
  }
}

/**
 * Verify email with OTP
 * @param email User's email address
 * @param otp OTP code
 * @returns AuthResponse with result of the operation
 */
export async function verifyEmail(
  email: string,
  otp: string
): Promise<AuthResponse> {
  try {
    // Input validation
    if (!email || !otp) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'Email and verification code are required'
      };
    }

    const supabase = createClient();

    // Verify OTP
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token: otp,
      type: 'email'
    });

    if (error) {
      throw error;
    }

    // Get user information after successful verification
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Error fetching user data after verification:', userError);
    }

    // Determine redirect path based on user type (if available)
    let redirectPath = '/dashboard';

    if (userData?.user?.user_metadata?.user_type) {
      const userType = String(
        userData.user.user_metadata.user_type
      ).toLowerCase();

      switch (userType) {
        case 'candidate':
          redirectPath = '/candidate';
          break;
        case 'insider':
          redirectPath = '/insider';
          break;
        case 'company':
          redirectPath = '/company';
          break;
        case 'admin':
          redirectPath = '/admin';
          break;
      }
    }

    return {
      type: 'success',
      title: 'Email Verified',
      description: 'Your email has been verified successfully.',
      redirectPath,
      data: { user: userData?.user }
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = 'An unexpected error occurred';

    if (authError?.message) {
      description = authError.message;
    }

    // Provide user-friendly error messages based on error type
    if (authError?.status === 400 || authError?.status === 401) {
      if (authError.message.includes('expired')) {
        description =
          'Verification code has expired. Please request a new one.';
      } else if (authError.message.includes('invalid')) {
        description = 'Invalid verification code. Please check and try again.';
      }
    }

    return {
      type: 'error',
      title: 'Verification Failed',
      description,
      data: { error: authError }
    };
  }
}

/**
 * Resend verification code for an existing account
 * @param email User's email address
 * @returns AuthResponse with result of the operation
 */
export async function resendVerificationCode(
  email: string
): Promise<AuthResponse> {
  try {
    // Input validation
    if (!email) {
      return {
        type: 'error',
        title: 'Verification Code Request Failed',
        description: 'Email is required'
      };
    }

    const supabase = createClient();

    // Use the correct method to resend verification email
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email,
      options: {
        emailRedirectTo: getURL('/auth/callback?next=/dashboard')
      }
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Verification Code Resent',
      description: 'A new verification code has been sent to your email.'
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = authError?.message || 'An unexpected error occurred';

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 422) {
      description = 'The email address format is invalid';
    } else if (authError?.status === 429) {
      description = 'Too many requests. Please try again later.';
    }

    return {
      type: 'error',
      title: 'Verification Code Failed',
      description
    };
  }
}

/**
 * Send a password reset email
 * @param email User's email address
 * @param redirectTo URL to redirect to after password reset
 * @returns AuthResponse with result of the operation
 */
export async function resetPasswordForEmail(
  email: string,
  redirectTo: string
): Promise<AuthResponse> {
  try {
    const supabase = createClient();
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${getURL('/reset-password')}?email=${encodeURIComponent(email)}&type=recovery`
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Check Your Email',
      description: 'We have sent you a password reset link.'
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      type: 'error',
      title: 'Reset Failed',
      description: authError?.message || 'Password reset request failed.'
    };
  }
}

/**
 * Update the user's password
 * @param password New password
 * @returns AuthResponse with result of the operation
 */
export async function updatePassword(password: string): Promise<AuthResponse> {
  if (!password) {
    return {
      type: 'error',
      title: 'Password Update Failed',
      description: 'Password is required'
    };
  }

  try {
    const supabase = createClient();

    const { error } = await supabase.auth.updateUser({
      password
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Password Updated',
      description: 'Your password has been successfully updated.',
      redirectPath: '/dashboard'
    };
  } catch (error) {
    const authError = error as AuthError;
    let description = authError?.message || 'An unexpected error occurred';

    // Handle specific auth errors with more user-friendly messages
    if (authError?.status === 422) {
      description = 'Password must be at least 6 characters';
    }

    return {
      type: 'error',
      title: 'Password Update Failed',
      description
    };
  }
}

/**
 * Sign in with an OAuth provider
 * @param provider OAuth provider (google, github, etc)
 * @param redirectTo URL to redirect to after sign in
 * @returns AuthResponse with result of the operation
 */
export async function signInWithOAuth(
  provider: Provider,
  redirectTo: string = '/dashboard'
): Promise<AuthResponse> {
  try {
    const supabase = createClient();

    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo
      }
    });

    if (error) {
      throw error;
    }

    return {
      type: 'success',
      title: 'Redirecting...',
      description: `Signing in with ${provider}`
    };
  } catch (error) {
    const authError = error as AuthError;
    return {
      type: 'error',
      title: 'OAuth Sign-in Failed',
      description: authError?.message || 'Could not sign in with this provider'
    };
  }
}

/**
 * Handle migration signup process for existing users
 * @param email User's email address
 * @param password New password
 * @returns AuthResponse with result of the operation
 */
export async function sendMigrationPasswordReset(
  email: string,
  password: string
): Promise<AuthResponse> {
  if (!email || !password) {
    return {
      type: 'error',
      title: 'Signup Failed',
      description: 'Email and password are required'
    };
  }

  console.log('🔍 Step 0: Validating email format:', email);
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(email)) {
    console.log('❌ Email validation failed');
    return {
      type: 'error',
      title: 'Invalid Email',
      description: 'Please enter a valid email address'
    };
  }

  try {
    const supabase = createClient();
    console.log('🔄 Starting migration signup process...');

    // Step 1: Check if user exists in database and verify migration status
    console.log('🔍 Step 1: Checking user in database...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, migration_status')
      .eq('email', email)
      .single();

    if (userError || !userData) {
      console.log('❌ User not found in database:', userError);
      return {
        type: 'error',
        title: 'Signup Failed',
        description: 'User not found in the system.'
      };
    }

    // Validate migration status
    if (
      !['not_migrated', 'migration_invited'].includes(userData.migration_status)
    ) {
      if (userData.migration_status === 'migrated') {
        return {
          type: 'error',
          title: 'Already Migrated',
          description:
            'This account has already been migrated. Please sign in.',
          redirectPath: '/signin'
        };
      }

      if (userData.migration_status === 'verification_pending') {
        return {
          type: 'error',
          title: 'Verification Pending',
          description: 'Please check your email for the verification link.',
          redirectPath: '/migrate-password/check-email'
        };
      }

      return {
        type: 'error',
        title: 'Invalid Status',
        description: 'Your account is not eligible for migration.',
        redirectPath: '/signin'
      };
    }

    console.log('✅ User found in database:', {
      id: userData.id,
      email: userData.email,
      status: userData.migration_status
    });

    // Step 2: Create auth account with email verification
    console.log('🔍 Step 2: Creating auth account...');
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp(
      {
        email,
        password,
        options: {
          emailRedirectTo: getURL(
            '/auth/callback?next=/migrate-password/verify'
          ),
          data: {
            id: userData.id,
            is_migration: true
          }
        }
      }
    );

    if (signUpError) {
      console.log('❌ Error during signup:', signUpError.message);

      // Handle rate limit error specifically
      if (signUpError.status === 429) {
        return {
          type: 'error',
          title: 'Too Many Attempts',
          description: 'Please wait a few minutes before trying again.'
        };
      }

      // Handle existing user error
      if (signUpError.message?.includes('already registered')) {
        return {
          type: 'error',
          title: 'Account Exists',
          description:
            'This email is already registered. Please sign in instead.',
          redirectPath: '/signin'
        };
      }

      throw signUpError;
    }

    // Step 3: Update migration status to verification pending
    console.log('🔍 Step 3: Updating migration status...');
    const { error: updateError } = await supabase
      .from('users')
      .update({
        migration_status: 'verification_pending',
        updated_at: new Date().toISOString()
      })
      .eq('id', userData.id);

    if (updateError) {
      console.error('❌ Error updating migration status:', updateError);
      // Log error but continue as auth account is created
      console.log('⚠️ Migration status update failed, but signup successful');
    } else {
      console.log('✅ Migration status updated to verification_pending');
    }

    return {
      type: 'success',
      title: 'Verification Email Sent',
      description:
        "Please check your email to verify your account. Check your spam folder if you don't see it.",
      redirectPath: '/migrate-password/check-email'
    };
  } catch (error) {
    const authError = error as AuthError;
    console.error('❌ Error in migration signup:', {
      error,
      message: authError?.message,
      status: authError?.status
    });

    let description = 'Unable to process your request. Please try again later.';
    if (authError.message?.toLowerCase().includes('password')) {
      description = 'Password must be at least 6 characters long';
    }

    return {
      type: 'error',
      title: 'Signup Failed',
      description
    };
  }
}

export async function signUpWithOAuth(provider: Provider, redirectURL: string) {
  try {
    const supabase = createClient();

    // sign up with oauth
    const { error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: redirectURL,
        queryParams: {
          flow: 'signup'
        }
      }
    });

    if (error) {
      throw error;
    }
  } catch (error) {
    const authError = error as AuthError;
    throw authError;
  }
}
