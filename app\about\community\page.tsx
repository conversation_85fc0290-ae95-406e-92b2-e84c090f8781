import { Metadata } from 'next';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card';
import { MessageSquare, Users, Share2, ExternalLink } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Community | InternUp',
  description:
    'Join our Discord community to connect with other interns, job seekers, and companies looking for talent.'
};

export default function CommunityPage() {
  return (
    <main className="flex flex-col items-center justify-center px-4 py-12 md:py-24">
      <div className="max-w-5xl w-full space-y-12">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            Join Our Community
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Connect with other interns, job seekers, and companies in our active
            Discord community.
          </p>
        </div>

        {/* Main Card */}
        <Card className="w-full overflow-hidden border-2 border-[#118073]/20 dark:border-[#118073]/40">
          <div className="bg-[#118073] h-3"></div>
          <CardHeader className="p-6 md:p-8 bg-gradient-to-b from-[#118073]/10 to-white dark:from-[#118073]/20 dark:to-background">
            <div className="flex justify-between items-center">
              <div className="space-y-1">
                <CardTitle className="text-2xl md:text-3xl">
                  Discord Community
                </CardTitle>
                <CardDescription>
                  A place to learn, share, and grow together
                </CardDescription>
              </div>
              <div className="hidden md:flex h-12 w-12 items-center justify-center rounded-full bg-[#118073]/10 dark:bg-[#118073]/20">
                <MessageSquare className="h-6 w-6 text-[#118073] dark:text-[#118073]/80" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6 md:p-8 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-start space-x-4">
                <div className="p-2 rounded-full bg-[#118073]/10 dark:bg-[#118073]/20">
                  <Users className="h-6 w-6 text-[#118073] dark:text-[#118073]/80" />
                </div>
                <div>
                  <h3 className="font-semibold">Connect with Peers</h3>
                  <p className="text-sm text-muted-foreground">
                    Network with other interns and job seekers
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="p-2 rounded-full bg-[#118073]/10 dark:bg-[#118073]/20">
                  <MessageSquare className="h-6 w-6 text-[#118073] dark:text-[#118073]/80" />
                </div>
                <div>
                  <h3 className="font-semibold">Get Support</h3>
                  <p className="text-sm text-muted-foreground">
                    Ask questions and receive guidance from mentors
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <div className="p-2 rounded-full bg-[#118073]/10 dark:bg-[#118073]/20">
                  <Share2 className="h-6 w-6 text-[#118073] dark:text-[#118073]/80" />
                </div>
                <div>
                  <h3 className="font-semibold">Share Opportunities</h3>
                  <p className="text-sm text-muted-foreground">
                    Discover and share job openings with the community
                  </p>
                </div>
              </div>
            </div>

            <div className="rounded-lg overflow-hidden border bg-muted/50 p-6 mt-6">
              <div className="space-y-4 text-center">
                <h3 className="text-xl font-semibold">
                  Ready to join the conversation?
                </h3>
                <p className="text-muted-foreground">
                  Our Discord community is free to join and open to all. Connect
                  with like-minded individuals, share your experiences, and grow
                  your network.
                </p>
                <Button
                  size="lg"
                  className="bg-[#118073] hover:bg-[#118073]/80 text-white"
                >
                  <Link
                    href="https://discord.gg/CDYRr7m9"
                    className="flex items-center space-x-2"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <span>Join Discord</span>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
          <CardFooter className="p-6 md:p-8 bg-muted/20 border-t">
            <p className="text-sm text-muted-foreground text-center w-full">
              By joining our community, you agree to follow our community
              guidelines and code of conduct.
            </p>
          </CardFooter>
        </Card>

        {/* Additional Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
          <Card>
            <CardHeader className="bg-[#118073]/10">
              <CardTitle>Community Events</CardTitle>
              <CardDescription>
                Regular meetups and networking opportunities
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <p className="text-muted-foreground">
                We host study groups, networking events, and workshops to help
                you connect with potential employers.
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="bg-[#118073]/10">
              <CardTitle>Resource Sharing</CardTitle>
              <CardDescription>
                Access exclusive resources and learning materials
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <p className="text-muted-foreground">
                Our community members actively share resources, learning
                materials, and insights about different companies and interview
                processes.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}
