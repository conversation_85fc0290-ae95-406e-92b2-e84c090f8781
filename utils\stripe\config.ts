// utils/stripe/config.ts remains unchanged

// Create a new file: utils/stripe/plans.ts
import Stripe from 'stripe';

export const stripe = new Stripe(
  process.env.STRIPE_SECRET_KEY_LIVE ?? process.env.STRIPE_SECRET_KEY ?? '',
  {
    apiVersion: '2023-10-16',
    appInfo: {
      name: 'InternUp',
      version: '0.1.0'
    }
  }
);

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  trial_period_days?: number;
  frontend_name?: string; // Added for frontend display
}

export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    // id: 'price_1QVapyCt3M2MG4FVZanH0Ft5',
    id: 'price_1QVaseCt3M2MG4FVNaT7qSox',
    name: 'Daily Jobs Access',
    description: 'For active job seekers',
    price: 5999,
    currency: 'usd',
    interval: 'month', // Will be treated as 6-month subscription in checkout
    trial_period_days: 0,
    frontend_name: 'Essential Access Pass'
  },
  {
    // id: 'price_1QVaqsCt3M2MG4FV7bkuc3ZT',
    id: 'price_1QVascCt3M2MG4FVBsL0DHUY',
    name: 'VIP 1-Month Pass',
    description: 'For committed career builders',
    price: 3999,
    currency: 'usd',
    interval: 'month',
    trial_period_days: 0,
    frontend_name: 'Pro Monthly Pass'
  },
  {
    // id: 'price_1QVarLCt3M2MG4FVBvEQKOmM',
    id: 'price_1QVasbCt3M2MG4FVfQPWHBgS',
    name: 'VIP 6-Month Pass',
    description: 'For ambitious professionals seeking quick results',
    price: 12999,
    currency: 'usd',
    interval: 'month', // Will be treated as 6-month subscription in checkout
    trial_period_days: 0,
    frontend_name: 'Elite Semi-Annual Pass'
  },
  {
    // id: 'price_1QVarnCt3M2MG4FV4WKfuk41',
    id: 'price_1QVasUCt3M2MG4FVAdwTGrwC',
    name: 'VIP Full-Year Pass',
    description: 'For growth-focused professionals',
    price: 19999,
    currency: 'usd',
    interval: 'year',
    trial_period_days: 0,
    frontend_name: 'Ultimate Annual Pass'
  }
];