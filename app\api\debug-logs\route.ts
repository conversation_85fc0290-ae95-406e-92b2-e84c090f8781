import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function GET() {
  try {
    const logPath = path.join(process.cwd(), 'subscription-debug.log');
    const logs = await fs.readFile(logPath, 'utf-8');
    return NextResponse.json({ logs: logs.split('\n') });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to read logs' }, { status: 500 });
  }
}
