'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { getBlog } from '@/actions/admin/blog';
import type { Blog } from '@/types/blog';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Heart, BookmarkPlus, Share2, ArrowLeft } from 'lucide-react';

export default function BlogPost() {
  const params = useParams();
  const [blog, setBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setIsLoading(true);
        const blogData = await getBlog(params.id as string);
        setBlog(blogData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch blog');
      } finally {
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchBlog();
    }
  }, [params.id]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
          <Link href="/blogs">
            <Button variant="outline" className="mt-4">
              Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Blog Not Found</h2>
          <p className="text-gray-600">
            The blog post you're looking for doesn't exist.
          </p>
          <Link href="/blogs">
            <Button variant="outline" className="mt-4">
              Back to Blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Back to blog - Updated color */}
      <div className="container px-4 py-6 mx-auto">
        <Link
          href="/blogs"
          className="inline-flex items-center text-gray-600 hover:text-[#118073]"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to all articles
        </Link>
      </div>

      {/* Article Header */}
      <header className="container px-4 pt-4 pb-12 mx-auto">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-6">
            {blog.title}
          </h1>
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-4">
              <span>
                {new Date(blog.creation_date || '').toLocaleDateString()}
              </span>
              <span>•</span>
              <span>{blog.author}</span>
            </div>
            <div className="flex space-x-2">
              <Button variant="ghost" size="icon" className="rounded-full">
                <Heart className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" className="rounded-full">
                <BookmarkPlus className="h-5 w-5" />
              </Button>
              <Button variant="ghost" size="icon" className="rounded-full">
                <Share2 className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Article Content - Updated prose colors */}
      <div className="container px-4 mx-auto">
        <div className="max-w-4xl mx-auto">
          <article className="prose prose-[#118073] dark:prose-invert lg:prose-lg">
            {/* Image Block */}
            {blog.block2 && (
              <div className="my-8">
                <img
                  src={blog.block2}
                  alt="Blog Image"
                  className="w-full h-auto rounded-lg"
                />
              </div>
            )}

            {/* Main Content */}
            <div
              dangerouslySetInnerHTML={{ __html: blog.block1 || '' }}
              className="my-8"
            />

            {/* Additional Content Blocks */}
            {blog.block3 && (
              <div
                className="my-8"
                dangerouslySetInnerHTML={{ __html: blog.block3 }}
              />
            )}
            {blog.block4 && (
              <div
                className="my-8"
                dangerouslySetInnerHTML={{ __html: blog.block4 }}
              />
            )}
          </article>
        </div>
      </div>

      {/* Article Footer - Updated button colors */}
      <div className="container px-4 mx-auto my-16">
        <div className="max-w-4xl mx-auto">
          <Separator className="my-8" />
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              Last updated:{' '}
              {new Date(blog.modified_date || '').toLocaleDateString()}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="border-[#118073] text-[#118073] hover:bg-[#118073] hover:text-white"
              >
                Share Article
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
