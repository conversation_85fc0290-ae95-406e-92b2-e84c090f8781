import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

// This endpoint uses a service role to bypass RLS
export async function POST(req: NextRequest) {
  try {
    console.log('Received request to create payment account with admin privileges');

    // Parse request body
    const body = await req.json();
    const { accountId, insiderId, userEmail, userId, stripeAccountId, status } = body;

    if (!accountId || !userEmail || !stripeAccountId || !status) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create a service role client (bypassing RLS)
    const supabase = createClient();

    // Create the payment account record directly
    console.log(`Creating payment account with admin privileges, ID: ${accountId}`);

    // Try to use the admin_create_payment_account function first
    try {
      const { data, error } = await supabase.rpc('admin_create_payment_account', {
        p_id: accountId,
        p_insider_id: insiderId,
        p_email: userEmail,
        p_user_id: userId,
        p_stripe_account_id: stripeAccountId,
        p_account_status: status,
        p_payment_method: 'stripe',
        p_created_at: new Date().toISOString(),
        p_updated_at: new Date().toISOString()
      });

      if (!error) {
        console.log('Successfully created payment account record with RPC:', data);
        return NextResponse.json({ success: true, account: data });
      }
    } catch (rpcError) {
      console.error('RPC function not available or failed:', rpcError);
      // Continue to fallback method
    }

    // Fallback to direct insert with service role
    console.log('Falling back to direct insert method');

    // First check if the table has the required columns
    try {
      const { data: insertData, error: insertError } = await supabase
        .from('insider_payment_accounts')
        .insert({
          id: accountId,
          insider_id: insiderId,
          email: userEmail,
          user_id: userId,
          stripe_account_id: stripeAccountId,
          account_status: status,
          payment_method: 'stripe',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (insertError) {
        console.error('Direct insert failed:', insertError);

        // Try with only the original columns if the error is about missing columns
        if (insertError.message && insertError.message.includes('column') && insertError.message.includes('does not exist')) {
          console.log('Trying insert with only original columns');
          const { data: basicInsertData, error: basicInsertError } = await supabase
            .from('insider_payment_accounts')
            .insert({
              id: accountId,
              stripe_account_id: stripeAccountId,
              account_status: status,
              payment_method: 'stripe',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (basicInsertError) {
            console.error('Basic insert also failed:', basicInsertError);
            return NextResponse.json({
              error: 'Failed to create payment account record',
              details: basicInsertError
            }, { status: 500 });
          }

          console.log('Successfully created basic payment account record:', basicInsertData);
          return NextResponse.json({ success: true, account: basicInsertData });
        }

        return NextResponse.json({
          error: 'Failed to create payment account record',
          details: insertError
        }, { status: 500 });
      }

      console.log('Successfully created payment account record with direct insert:', insertData);
      return NextResponse.json({ success: true, account: insertData });
    } catch (insertCatchError) {
      console.error('Unexpected error during insert:', insertCatchError);
      return NextResponse.json({
        error: 'Unexpected error during insert',
        details: insertCatchError instanceof Error ? insertCatchError.message : String(insertCatchError)
      }, { status: 500 });
    }

    // This code is unreachable now as we return from each branch above
    // But keeping it as a fallback just in case
    return NextResponse.json({
      success: true,
      message: 'Account created but could not retrieve details',
      account: {
        id: accountId,
        stripe_account_id: stripeAccountId,
        account_status: status,
        payment_method: 'stripe',
        email: userEmail
      }
    });

  } catch (error) {
    console.error('Error in admin create payment account:', error);
    return NextResponse.json(
      { error: `Failed to create payment account: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}
