'use client';

import type React from 'react';

import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useUser';
import { useToast } from '@/components/ui/use-toast';
import {
  getCompanyJobs,
  createJob,
  updateJob,
  deleteJob
} from '@/actions/company/posts';
import { getCompanyDetails } from '@/actions/company/account';
import { JobsTable } from '@/components/company/jobs-table';
import { NewJobDialog } from '@/components/company/new-job-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import type {
  StartupJob,
  PaginatedResponse,
  StartupCompany
} from '@/types/startups';
import { Plus } from 'lucide-react';

export default function CompanyJobPostsPage() {
  const { user } = useUser();
  const { toast } = useToast();
  const [jobs, setJobs] = useState<StartupJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentTab, setCurrentTab] = useState('all');
  const [showNewJobDialog, setShowNewJobDialog] = useState(false);
  const [totalJobs, setTotalJobs] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [companyDetails, setCompanyDetails] = useState<StartupCompany | null>(
    null
  );

  const fetchJobs = async (page = 1, tab = currentTab, search = searchTerm) => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const statusFilter = tab !== 'all' ? tab : undefined;
      const response: PaginatedResponse<StartupJob> = await getCompanyJobs({
        page,
        searchTerm: search,
        companyId: user.startup_company || undefined,
        status: statusFilter
      });

      setJobs(response.data);
      setTotalJobs(response.count || 0);
      setCurrentPage(response.currentPage);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      toast({
        title: 'Error',
        description: 'Failed to load job posts',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch company details when user is loaded
  useEffect(() => {
    const fetchCompanyDetails = async () => {
      if (user?.id) {
        try {
          const details = await getCompanyDetails(user.id);
          setCompanyDetails(details);
        } catch (error) {
          console.error('Error fetching company details:', error);
        }
      }
    };

    fetchCompanyDetails();
  }, [user?.id]);

  useEffect(() => {
    if (user?.id) {
      fetchJobs();
    }
  }, [user?.id]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchJobs(1, currentTab, searchTerm);
  };

  const handleTabChange = (value: string) => {
    setCurrentTab(value);
    fetchJobs(1, value, searchTerm);
  };

  const handleCreateJob = async (jobData: Partial<StartupJob>) => {
    try {
      // Check if the user has a company associated
      if (!user?.startup_company) {
        toast({
          title: 'Error',
          description: 'You need to create a company profile first',
          variant: 'destructive'
        });
        return;
      }

      // Ensure company_id (which is actually the company name) is set
      if (!jobData.company_id || jobData.company_id === '') {
        // Set the company name from the user object
        jobData.company_id = user.startup_company;

        // If still empty, show error
        if (!jobData.company_id || jobData.company_id === '') {
          toast({
            title: 'Error',
            description:
              'Company name is required. Please create a company profile first.',
            variant: 'destructive'
          });
          return;
        }
      }

      // Validate required fields before submission
      if (!jobData.title) {
        toast({
          title: 'Error',
          description: 'Job title is required',
          variant: 'destructive'
        });
        return;
      }

      // Email and LinkedIn are optional, so we don't need to validate them

      const result = await createJob(jobData);

      toast({
        title: 'Success',
        description:
          'Job created successfully. It will be reviewed before publishing.'
      });
      setShowNewJobDialog(false);
      fetchJobs();
    } catch (error) {
      console.error('Error creating job:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create job',
        variant: 'destructive'
      });
    }
  };

  const handleUpdateJob = async (id: string, jobData: Partial<StartupJob>) => {
    try {
      await updateJob(id, jobData);
      toast({
        title: 'Success',
        description: 'Job updated successfully'
      });
      fetchJobs();
    } catch (error) {
      console.error('Error updating job:', error);
      toast({
        title: 'Error',
        description: 'Failed to update job',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteJob = async (id: string) => {
    try {
      await deleteJob(id);
      toast({
        title: 'Success',
        description: 'Job deleted successfully'
      });
      fetchJobs();
    } catch (error) {
      console.error('Error deleting job:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete job',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="container mx-auto">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Job Posts</h1>
          <p className="text-muted-foreground">
            Here are your latest job posts
          </p>
        </div>
        <Button onClick={() => setShowNewJobDialog(true)}>
          <Plus className="mr-2 h-4 w-4" />
          New Job
        </Button>
      </div>

      <Tabs defaultValue="all" onValueChange={handleTabChange}>
        <TabsList className="mb-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="published">Published</TabsTrigger>
          <TabsTrigger value="under_review">Under Review</TabsTrigger>
          <TabsTrigger value="not_approved">Not Approved</TabsTrigger>
        </TabsList>

        <TabsContent value={currentTab} className="mt-0">
          <form onSubmit={handleSearch} className="mb-6 flex gap-2">
            <Input
              placeholder="Search jobs"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
            <Button type="submit">Search</Button>
          </form>

          <JobsTable
            jobs={jobs}
            loading={loading}
            onDelete={handleDeleteJob}
            onEdit={handleUpdateJob}
            totalJobs={totalJobs}
            currentPage={currentPage}
            onPageChange={(page) => fetchJobs(page)}
          />

          {jobs.length === 0 && !loading && (
            <div className="mt-8 text-center">
              <p className="mb-2 text-lg font-medium">
                You haven't posted any jobs yet.
              </p>
              <p className="text-muted-foreground">
                Get started from the new job button.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      <NewJobDialog
        open={showNewJobDialog}
        onOpenChange={setShowNewJobDialog}
        onSubmit={handleCreateJob}
        companyName={user?.startup_company || ''}
        companyEmail={companyDetails?.contact_email || ''}
        companyLinkedin={companyDetails?.company_linkedin || ''}
      />
    </div>
  );
}
