export const testimonials = [
  {
    name: 'dcodesdev',
    title: 'TypeScript Developer',
    avatarFallback: 'DC',
    avatarImg: '/images/dcodes.png',
    text: "That's beautiful bro!"
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    title: '<PERSON><PERSON>per at joinOnboard',
    avatarFallback: 'SK',
    avatarImg: '/images/SuhailKakar.jpg',
    text: "If you've built this a few months ago, it would have saved me hours :D"
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    title: 'Founder of microlaunch.net',
    avatarFallback: 'SA',
    avatarImg: '/images/said.jpg',
    text: "So cool, looks really clean. Any plan to open source it? ☺️ Wanna play with it!"
  },
  {
    name: 'magicuidesign',
    title: 'UI Design Company',
    avatarFallback: 'MU',
    avatarImg: '/images/magicui.jpg',
    text: "Clean 🤌"
  },
  {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    title: '<PERSON>elo<PERSON>',
    avatarFallback: 'YR',
    avatarImg: '/images/yasmeen.jpg',
    text: "<PERSON><PERSON> would love to try this out"
  },
  {
    name: 'shadcn',
    title: '<PERSON><PERSON><PERSON>',
    avatarFallback: 'SC',
    avatarImg: '/images/shadcn.jpg',
    text: "👀"
  },
  {
    name: 'bzagrodzki',
    title: 'Developer',
    avatarFallback: 'BZ',
    avatarImg: '/images/bzrag.jpg',
    text: "Nice one! But I would prefer some more \"sans\" font 😉"
  },
  {
    name: 'MPlegas',
    title: 'Developer',
    avatarFallback: 'MP',
    avatarImg: '/images/MPlegas.jpg',
    text: "Exceptional!"
  },
  {
    name: 'kvncyf_',
    title: 'Developer',
    avatarFallback: 'KC',
    avatarImg: '/images/kvn.jpg',
    text: "Nice move."
  },
  {
    name: '0xRaduan',
    title: 'Developer',
    avatarFallback: 'RA',
    avatarImg: '/images/0xraduan.jpg',
    text: "This looks fire"
  },
  {
    name: 'Luax0',
    title: 'Developer',
    avatarFallback: 'LX',
    avatarImg: '/images/luax0.jpg',
    text: "Can't wait to see more 👀"
  },
  {
    name: 'ausrobdev',
    title: 'Developer',
    avatarFallback: 'RA',
    avatarImg: '/images/robdev.jpg',
    text: "Let me know when its ready, I'll add it to buildatlightspeed.com - we need more high quality open source boilerplates"
  }
];