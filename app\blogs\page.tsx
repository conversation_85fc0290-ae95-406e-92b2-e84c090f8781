'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Search, BookmarkPlus, Heart } from 'lucide-react';
import { getBlogs, getBlog } from '@/actions/admin/blog';
import type { Blog } from '@/types/blog';
import { useParams } from 'next/navigation';

export default function BlogPage() {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [featuredBlog, setFeaturedBlog] = useState<Blog | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const params = useParams();

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        setIsLoading(true);
        // Fetch blogs with pagination and search
        const response = await getBlogs({
          page: currentPage,
          searchTerm
        });

        setBlogs(response.data);

        // Set the first blog as featured if available
        if (response.data.length > 0) {
          const featured = await getBlog(response.data[0].id);
          setFeaturedBlog(featured);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch blogs');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBlogs();
  }, [currentPage, searchTerm]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section - Updated to match mission page theme */}
      <section className="relative bg-[#118073] text-white">
        <div className="absolute inset-0 bg-[url('/placeholder.svg?height=100&width=100')] bg-repeat opacity-5"></div>
        <div className="container px-4 py-16 md:py-24 mx-auto relative">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-white mb-6">
              Career Insights Hub
            </h1>
            <p className="text-lg md:text-xl text-white opacity-90 mb-8">
              Helping International Students Navigate Jobs, Internships, and
              More
            </p>
            <div className="relative max-w-xl mx-auto">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="search"
                placeholder="Search articles..."
                className="pl-10 pr-4 py-3 w-full rounded-full border-gray-200 bg-white/90 focus:ring-white focus:border-white"
                value={searchTerm}
                onChange={handleSearch}
              />
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-background to-transparent"></div>
      </section>

      {/* Featured Post - Updated colors */}
      {featuredBlog && (
        <section className="container px-4 py-12 mx-auto">
          <h2 className="text-2xl font-bold mb-8 flex items-center">
            <span className="w-8 h-1 bg-[#118073] rounded-full mr-3"></span>
            Featured Article
          </h2>
          <div className="gap-8 items-center">
            <div className="space-y-4">
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-4">
                <span>
                  {new Date(
                    featuredBlog.creation_date || ''
                  ).toLocaleDateString()}
                </span>
                <span>•</span>
                <span>{featuredBlog.author}</span>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold leading-tight">
                <Link
                  href={`/blogs/${featuredBlog.id}`}
                  className="hover:text-green-600 dark:hover:text-green-400 transition-colors"
                >
                  {featuredBlog.title}
                </Link>
              </h3>
              <p className="text-gray-600 dark:text-gray-300 line-clamp-3">
                {featuredBlog.block1}
              </p>
              <div className="pt-4 flex items-center justify-between">
                <Link href={`/blogs/${featuredBlog.id}`}>
                  <Button className="bg-[#118073] hover:bg-[#118073]/80 text-white">
                    Read Article
                  </Button>
                </Link>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <Heart className="h-5 w-5" />
                  </Button>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <BookmarkPlus className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Latest Articles - Updated colors */}
      <section className="container px-4 py-12 mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-bold flex items-center">
            <span className="w-8 h-1 bg-[#118073] rounded-full mr-3"></span>
            Latest Articles
          </h2>
        </div>

        {isLoading ? (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((n) => (
              <div key={n} className="animate-pulse">
                <div className="bg-gray-200 dark:bg-gray-700 h-48 rounded-lg mb-4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogs.map((blog) => (
              <Card
                key={blog.id}
                className="overflow-hidden border-gray-200 dark:border-gray-800 hover:shadow-md transition-shadow"
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 space-x-3">
                    <span>
                      {new Date(blog.creation_date || '').toLocaleDateString()}
                    </span>
                    <span>•</span>
                    <span>{blog.author}</span>
                  </div>
                  <h3 className="text-xl font-bold mt-2 line-clamp-2">
                    <Link
                      href={`/blogs/${blog.id}`}
                      className="hover:text-green-600 dark:hover:text-green-400 transition-colors"
                    >
                      {blog.title}
                    </Link>
                  </h3>
                </CardHeader>
                <CardContent>
                  <div
                    className={`w-full rounded-lg mb-4 ${!blog.block2 ? 'h-48 bg-[#118073] flex items-center justify-center' : 'h-48'}`}
                  >
                    <img
                      src={blog.block2 || '/footer_logo.png'}
                      alt={blog.title || 'Blog Image'}
                      className={`${!blog.block2 ? 'h-12' : 'h-full w-full object-cover'}`}
                    />
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 line-clamp-3">
                    {blog.block1}
                  </p>
                </CardContent>
                <CardFooter className="flex justify-between pt-0">
                  <Link
                    href={`/blogs/${blog.id}`}
                    className="text-[#118073] hover:text-[#118073]/80 font-medium hover:underline"
                  >
                    Read more
                  </Link>
                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 rounded-full"
                    >
                      <BookmarkPlus className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        <div className="mt-12 text-center">
          <Button
            variant="outline"
            className="rounded-full px-8 border-[#118073] text-[#118073] hover:bg-[#118073] hover:text-white"
            onClick={() => setCurrentPage((prev) => prev + 1)}
          >
            Load More Articles
          </Button>
        </div>
      </section>

      {/* Newsletter - Updated colors */}
      <section className="container px-4 py-16 mx-auto">
        <div className="bg-[#118073] text-white rounded-2xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Stay Updated with Career Insights
            </h2>
            <p className="text-white opacity-90 mb-8">
              Get the latest articles, resources, and career opportunities
              delivered directly to your inbox.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                className="rounded-full bg-white/90"
              />
              <Button className="bg-white hover:bg-white/90 text-[#118073] rounded-full whitespace-nowrap">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
