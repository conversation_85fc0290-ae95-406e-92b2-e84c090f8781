import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import <PERSON><PERSON> from 'stripe';
import { stripe } from '@/utils/stripe/config';
import { createClient } from '@/utils/supabase/server';
import { manageSubscriptionStatusChange, handleOneTimePayment } from '@/utils/db/stripe-db';
import { updateAccountStatus } from '@/utils/stripe/connect';
import { v4 as uuidv4 } from 'uuid';

// Set specific configuration for handling raw bodies
export const dynamic = 'force-dynamic';

// Properly read the request body as a Buffer
async function getRawBody(req: NextRequest): Promise<Buffer> {
  const arr = await new Response(req.body).arrayBuffer();
  return Buffer.from(arr);
}

export async function POST(req: NextRequest) {
  console.log('============ STRIPE WEBHOOK RECEIVED ============');
  const headersList = headers();
  const signature = headersList.get('stripe-signature');

  if (!signature) {
    console.error('No Stripe signature found');
    return NextResponse.json({ error: 'No signature found' }, { status: 400 });
  }

  console.log('Validating webhook signature...');

  let event: Stripe.Event;
  let rawBody: Buffer;

  try {
    // Get the raw request body
    rawBody = await getRawBody(req);

    try {
      // Verify the webhook signature
      event = stripe.webhooks.constructEvent(
        rawBody,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET || ''
      );

      console.log(`Webhook verified: ${event.type}`);
    } catch (err: any) {
      console.error(`Webhook signature verification failed: ${err.message}`);
      return NextResponse.json(
        { error: `Webhook Error: ${err.message}` },
        { status: 400 }
      );
    }

    const supabase = createClient();

    // Process the event
    try {
      switch (event.type) {
        case 'checkout.session.completed': {
          console.log('Processing checkout.session.completed');
          const session = event.data.object as Stripe.Checkout.Session;

          // Extract customer data
          const customerId = session.customer as string;

          if (!session.metadata?.userId) {
            console.warn('No user ID found in session metadata, attempting to find user by customer email');
            // We'll handle this later with the customer email
          }

          // Handle differently based on mode (subscription or payment)
          if (session.mode === 'subscription' && session.subscription) {
            // Handle subscription using our helper function
            await manageSubscriptionStatusChange(
              session.subscription as string,
              customerId,
              true // Creation action
            );
          } else if (session.mode === 'payment') {
            // Handle one-time payment
            await handleOneTimePayment(session, customerId);
          }

          console.log('Successfully processed checkout session');
          break;
        }

        case 'customer.subscription.updated': {
          console.log('Processing subscription update');
          const subscription = event.data.object as Stripe.Subscription;

          // Update subscription using our helper function
          await manageSubscriptionStatusChange(
            subscription.id,
            subscription.customer as string,
            false // Not a creation action
          );

          break;
        }

        case 'customer.subscription.deleted': {
          console.log('Processing subscription deletion');
          const subscription = event.data.object as Stripe.Subscription;

          // Get customer details to map to user
          const customer = await stripe.customers.retrieve(subscription.customer as string) as Stripe.Customer;
          const userEmail = customer.email;

          // Find the subscription in our database
          const { data: subscriptionData, error: subscriptionError } = await supabase
            .from('subscriptions')
            .select('id')
            .eq('subscription_id', subscription.id)
            .single();

          if (subscriptionError || !subscriptionData) {
            console.error('Error finding subscription:', subscriptionError);
            throw subscriptionError || new Error('Subscription not found');
          }

          // Update subscription status
          const { error: updateError } = await supabase
            .from('subscriptions')
            .update({
              status: 'canceled',
              modified_date: new Date().toISOString(),
              end_date: new Date().toISOString(),
              cancelable_date: new Date().toISOString()
            })
            .eq('subscription_id', subscription.id);

          if (updateError) {
            console.error('Error updating subscription:', updateError);
            throw updateError;
          }

          // Find and update user
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('id')
            .eq('email', userEmail)
            .single();

          if (userError || !userData) {
            console.error('Error finding user:', userError);
            throw userError || new Error('User not found');
          }

          // Update user subscription status
          const { error: userUpdateError } = await supabase
            .from('users')
            .update({
              is_subscription_active: false,
              vip: false
            })
            .eq('id', userData.id);

          if (userUpdateError) {
            console.error('Error updating user:', userUpdateError);
            throw userUpdateError;
          }

          break;
        }

        // Handle Stripe Connect account events
        case 'account.updated': {
          console.log('Processing account update');
          const account = event.data.object as Stripe.Account;

          // Find the insider with this Stripe account ID
          let { data: paymentAccount, error: accountError } = await supabase
            .from('insider_payment_accounts')
            .select('id')
            .eq('stripe_account_id', account.id)
            .single();

          // If no payment account found, try to find the insider using metadata
          if (accountError || !paymentAccount) {
            console.log(`No insider payment account found with Stripe account ID ${account.id}, checking metadata`);
            
            // Get the insiderId from account metadata
            const insiderId = account.metadata?.insiderId;
            const email = account.metadata?.email || account.email;
            
            if (insiderId) {
              console.log(`Found insiderId in metadata: ${insiderId}`);
              
              // Check if insider exists
              const { data: insider } = await supabase
                .from('insiders')
                .select('id')
                .eq('id', insiderId)
                .single();
              
              if (insider) {
                // Create payment account record
                const { data: newAccount, error: insertError } = await supabase
                  .from('insider_payment_accounts')
                  .upsert({
                    id: insiderId,
                    stripe_account_id: account.id,
                    payment_method: 'stripe',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                  })
                  .select()
                  .single();
                
                if (!insertError && newAccount) {
                  paymentAccount = newAccount;
                  accountError = null;
                  console.log(`Created payment account for insider ${insiderId}`);
                }
              } else if (email) {
                // Try to find insider by email
                const { data: insiderByEmail } = await supabase
                  .from('insiders')
                  .select('id')
                  .eq('user_email', email)
                  .single();
                
                if (insiderByEmail) {
                  // Create payment account record
                  const { data: newAccount, error: insertError } = await supabase
                    .from('insider_payment_accounts')
                    .upsert({
                      id: insiderByEmail.id,
                      stripe_account_id: account.id,
                      payment_method: 'stripe',
                      created_at: new Date().toISOString(),
                      updated_at: new Date().toISOString()
                    })
                    .select()
                    .single();
                  
                  if (!insertError && newAccount) {
                    paymentAccount = newAccount;
                    accountError = null;
                    console.log(`Created payment account for insider ${insiderByEmail.id} found by email`);
                  }
                }
              }
            }
            
            if (accountError || !paymentAccount) {
              console.warn(`Could not locate insider for Stripe account ${account.id}`);
              break;
            }
          }

          // Update the account status
          await updateAccountStatus(paymentAccount.id, account.id);
          console.log(`Updated account status for insider ${paymentAccount.id}`);

          break;
        }

        case 'payout.created': {
          console.log('Processing payout creation');
          const payout = event.data.object as Stripe.Payout;

          // Check if this is a payout to an insider
          if (!payout.metadata?.insider_payout_id) {
            console.log('Not an insider payout');
            break;
          }

          // Update the payout status
          const { error: payoutError } = await supabase
            .from('insider_payouts')
            .update({
              status: 'processing',
              stripe_payout_id: payout.id,
              expected_arrival_date: payout.arrival_date ? new Date(payout.arrival_date * 1000).toISOString() : null,
              updated_at: new Date().toISOString()
            })
            .eq('id', payout.metadata.insider_payout_id);

          if (payoutError) {
            console.error('Error updating payout status:', payoutError);
          }

          break;
        }

        case 'payout.paid': {
          console.log('Processing payout success');
          const payout = event.data.object as Stripe.Payout;

          // Check if this is a payout to an insider
          if (!payout.metadata?.insider_payout_id) {
            console.log('Not an insider payout');
            break;
          }

          // Update the payout status
          const { error: payoutError } = await supabase
            .from('insider_payouts')
            .update({
              status: 'completed',
              arrival_date: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .eq('id', payout.metadata.insider_payout_id);

          if (payoutError) {
            console.error('Error updating payout status:', payoutError);
          }

          break;
        }

        case 'payout.failed': {
          console.log('Processing payout failure');
          const payout = event.data.object as Stripe.Payout;

          // Check if this is a payout to an insider
          if (!payout.metadata?.insider_payout_id || !payout.metadata?.insider_id) {
            console.log('Not an insider payout');
            break;
          }

          // Update the payout status
          const { error: payoutError } = await supabase
            .from('insider_payouts')
            .update({
              status: 'failed',
              updated_at: new Date().toISOString()
            })
            .eq('id', payout.metadata.insider_payout_id);

          if (payoutError) {
            console.error('Error updating payout status:', payoutError);
          }

          // Refund the amount back to the insider's available earnings
          const { data: insiderPayout, error: fetchError } = await supabase
            .from('insider_payouts')
            .select('amount, insider_id')
            .eq('id', payout.metadata.insider_payout_id)
            .single();

          if (fetchError || !insiderPayout) {
            console.error('Error fetching payout:', fetchError);
            break;
          }

          // Get the insider's current earnings
          const { data: insider, error: insiderError } = await supabase
            .from('insiders')
            .select('earnings_available, earnings_paid')
            .eq('id', insiderPayout.insider_id)
            .single();

          if (insiderError || !insider) {
            console.error('Error fetching insider:', insiderError);
            break;
          }

          // Update the insider's earnings
          await supabase
            .from('insiders')
            .update({
              earnings_available: (insider.earnings_available || 0) + insiderPayout.amount,
              earnings_paid: Math.max(0, (insider.earnings_paid || 0) - insiderPayout.amount)
            })
            .eq('id', insiderPayout.insider_id);

          break;
        }

        // Handle other events you need
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return NextResponse.json({ received: true }, { status: 200 });
    } catch (error) {
      console.error(`Error processing webhook: ${error}`);
      return NextResponse.json(
        { error: 'Webhook handler failed. Check server logs for more details.' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error(`Error handling webhook: ${error}`);
    return NextResponse.json(
      { error: 'Webhook handler failed. Check server logs for more details.' },
      { status: 500 }
    );
  }
}
