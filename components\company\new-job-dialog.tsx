"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import type { StartupJob, StartupJobFormData } from "@/types/startups"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { MultiSelect } from "@/components/ui/multi-select"
import { useToast } from "@/components/ui/use-toast"

const jobFormSchema = z.object({
  title: z.string().min(2, "Job title must be at least 2 characters"),
  company_id: z.string().min(1, "Company name is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  location: z.string().min(1, "Location is required"),
  work_type: z.string().min(1, "Work type is required"),
  term: z.string().min(1, "Term is required"),
  salary: z.string().optional(),
  sponsorship: z.string().optional(),
  min_internship_duration: z.string().optional(),
  min_weekly_hours: z.string().optional(),
  job_link: z.string().optional(),
  recruiter_email: z.string().email().optional(),
  recruiter_linkedin: z.string().optional(),
  payment_negotiable: z.boolean().default(false),
  other_compensation: z.string().default("no"),
  in_other_market: z.boolean().default(false),
  provide_linkedin_endorsement: z.boolean().default(false),
  live: z.boolean().default(false),
  required_skills: z.array(z.string()).optional(),
})

interface NewJobDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (data: Partial<StartupJob>) => Promise<void>
  companyName: string
  companyEmail?: string
  companyLinkedin?: string
}

export function NewJobDialog({ 
  open, 
  onOpenChange, 
  onSubmit, 
  companyName,
  companyEmail = "",
  companyLinkedin = ""
}: NewJobDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const form = useForm<StartupJobFormData>({
    resolver: zodResolver(jobFormSchema),
    defaultValues: {
      title: "",
      company_id: companyName,
      description: "",
      location: "",
      work_type: "",
      term: "",
      salary: "",
      sponsorship: "",
      min_internship_duration: "",
      min_weekly_hours: "",
      job_link: "",
      recruiter_email: companyEmail,
      recruiter_linkedin: companyLinkedin,
      payment_negotiable: false,
      other_compensation: "no",
      in_other_market: false,
      provide_linkedin_endorsement: false,
      live: false,
      required_skills: [],
    },
  })

  useEffect(() => {
    if (companyName) {
      form.setValue("company_id", companyName);
    }
    
    if (companyEmail) {
      form.setValue("recruiter_email", companyEmail);
    }
    
    if (companyLinkedin) {
      form.setValue("recruiter_linkedin", companyLinkedin);
    }
  }, [companyName, companyEmail, companyLinkedin, form]);

  const validateCompanyName = () => {
    const currentCompanyName = form.getValues("company_id");
    if (!currentCompanyName) {
      if (companyName) {
        form.setValue("company_id", companyName);
        return true;
      }
      toast({
        title: "Error",
        description: "Company name is required. Please ensure you have a company profile.",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const handleSubmit = async (values: StartupJobFormData) => {
    if (!validateCompanyName()) {
      return;
    }
    
    setIsSubmitting(true)
    try {
      await onSubmit(values)
      form.reset()
      onOpenChange(false); // Close dialog after successful submission
    } catch (error) {
      console.error("Error in handleSubmit:", error);
      toast({
        title: "Error",
        description: "Failed to create job. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create A Startup Job</DialogTitle>
          <DialogDescription>Fill in the details to create a new job posting.</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title *</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="salary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Salary(Annual)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="work_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work Type * (which type you accept?)</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Full Time">Full Time</SelectItem>
                        <SelectItem value="Part Time">Part Time</SelectItem>
                        <SelectItem value="Contract">Contract</SelectItem>
                        <SelectItem value="Internship">Internship</SelectItem>
                        <SelectItem value="Freelance">Freelance</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="term"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Term</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Permanent">Permanent</SelectItem>
                        <SelectItem value="Temporary">Temporary</SelectItem>
                        <SelectItem value="Internship">Internship</SelectItem>
                        <SelectItem value="Summer">Summer</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="min_weekly_hours"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Weekly Hours(Intern)</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="5-10">5-10 hours</SelectItem>
                        <SelectItem value="10-20">10-20 hours</SelectItem>
                        <SelectItem value="20-30">20-30 hours</SelectItem>
                        <SelectItem value="30-40">30-40 hours</SelectItem>
                        <SelectItem value="40+">40+ hours</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="min_internship_duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Duration(Intern)</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1 month">1 month</SelectItem>
                        <SelectItem value="2 months">2 months</SelectItem>
                        <SelectItem value="3 months">3 months</SelectItem>
                        <SelectItem value="6 months">6 months</SelectItem>
                        <SelectItem value="1 year">1 year</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="job_link"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Link</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Preferred way of receiving resumes</h3>
              <p className="text-sm text-muted-foreground">These details will update your company profile.</p>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="recruiter_email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Contact Email</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="recruiter_linkedin"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company LinkedIn</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      rows={6}
                      placeholder="Describe the job responsibilities, requirements, and any other relevant information..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="required_skills"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Required Skills</FormLabel>
                  <FormControl>
                    <MultiSelect
                      options={[]}
                      selected={field.value || []}
                      onChange={field.onChange}
                      placeholder="Select or add skills..."
                      // creatable
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="payment_negotiable"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Payment negotiable</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="provide_linkedin_endorsement"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Provide LinkedIn Endorsement/Recommendation</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                disabled={isSubmitting}
                onClick={() => {
                  // Ensure company_id (which is actually company name) is set before submission
                  if (companyName) {
                    form.setValue("company_id", companyName);
                  }
                  
                  // Check for validation errors
                  const formValues = form.getValues();
                  
                  // Check for required fields and show toast for validation errors
                  const errors = form.formState.errors;
                  if (Object.keys(errors).length > 0) {
                    toast({
                      title: "Validation Error",
                      description: "Please fill in all required fields correctly",
                      variant: "destructive",
                    });
                    return;
                  }
                  
                  // Validate company name
                  if (!validateCompanyName()) {
                    return;
                  }
                  
                  // Manually trigger form validation and submission
                  form.handleSubmit((data) => {
                    handleSubmit(data);
                  })();
                }}
              >
                {isSubmitting ? "Creating..." : "Save"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
