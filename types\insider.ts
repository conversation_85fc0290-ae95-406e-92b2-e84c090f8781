export interface Insider {
  public_firm_companies: any;
  id: string;
  account_status: string | null;
  avalable_time: string | null;
  avalilable_day: string | null;
  avatar: string | null;
  blocked: string | null;
  direction: string | null;
  first_name: string | null;
  last_active: string | null;
  last_name: string | null;
  linkedin: string | null;
  position: string | null;
  profile_approved: string | null;
  public_firm: string | null;
  referral_approved: string | null;
  referral_approved_count: string | null;
  referral_decline: string | null;
  referral_decline_count: string | null;
  requirements: string | null;
  services: string[] | null;
  start_up_company: string | null;
  user_email: string | null;
  worktype: string[] | null;
  creation_date: string | null;
  modified_date: string | null;
  slug: string | null;
  creator: string | null;
}

export interface InsiderFormData {
  first_name: string;
  last_name?: string;
  referral_approved?: string;
  referral_decline?: string;
  referral_approved_count?: string[];
  referral_decline_count?: string[];
  position: string;
  start_up_company: string;
  public_firm: string;
  linkedin?: string;
  avatar?: string;
  services: string[];
  avalable_time?: string;
  avalilable_day?: string;
  worktype?: string[];
  requirements?: string;
  user_email: string;
  creation_date?: string;
  modified_date?: string;
  slug?: string;
  creator?: string;
  account_status?: string;
}

export interface InsidersQueryParams {
  page: number;
  searchTerm?: string;
  userType?: string;
}
