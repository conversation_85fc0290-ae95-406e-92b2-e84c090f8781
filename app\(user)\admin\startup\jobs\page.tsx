'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import Image from 'next/image';
import { PencilIcon, XCircleIcon, ExternalLink } from 'lucide-react';
import { getJobs, updateJob, deleteJob } from '@/actions/admin/startup/jobs';
import { getAllCompanies } from '@/actions/admin/startup/companies';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { StartupJob, StartupCompany } from '@/types/startups';
import JobFormDialog from './startup-job-form';
import Link from 'next/link';

export default function JobsPage() {
  const [jobs, setJobs] = useState<StartupJob[]>([]);
  const [companies, setCompanies] = useState<StartupCompany[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCompany, setSelectedCompany] = useState('');
  const [editJob, setEditJob] = useState<StartupJob | null>(null);

  useEffect(() => {
    fetchCompanies();
  }, []);

  useEffect(() => {
    fetchJobs();
  }, [currentPage, selectedCompany, searchTerm]);

  const fetchCompanies = async () => {
    try {
      const data = await getAllCompanies('name', 'asc');
      setCompanies(data || []);
    } catch (error) {
      console.error('Error fetching companies:', error);
    }
  };

  const fetchJobs = async () => {
    setIsLoading(true);
    try {
      const {
        data,
        totalPages: pages,
        totalCount
      } = await getJobs({
        page: currentPage,
        searchTerm,
        companyId: selectedCompany === 'all' ? '' : selectedCompany
      });
      setJobs(data || []);
      setTotalPages(pages || 1);
      setTotalCount(totalCount || 0);
    } catch (error) {
      console.error('Error fetching jobs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleJobSaved = () => {
    setIsCreateDialogOpen(false);
    setEditJob(null);
    fetchJobs();
  };

  const handleDeleteJob = async (id: string) => {
    if (confirm('Are you sure you want to delete this job?')) {
      try {
        await deleteJob(id);
        fetchJobs();
      } catch (error) {
        console.error('Error deleting job:', error);
      }
    }
  };

  const handleToggleLive = async (id: string, isLive: boolean) => {
    try {
      await updateJob(id, { live: isLive });
      fetchJobs();
    } catch (error) {
      console.error('Error updating job status:', error);
    }
  };

  const handleEditJob = (job: StartupJob) => {
    setEditJob(job);
    setIsCreateDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-2">Startup Posts</h1>
      <p className="text-gray-600 mb-6">Here are all startup posts</p>

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <p className="text-sm text-gray-500">
            Showing {jobs.length} of {totalCount} results
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative w-64">
            <Input
              placeholder="Search job titles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <Select
            value={selectedCompany}
            onValueChange={(value) => {
              setSelectedCompany(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Companies</SelectItem>
              {companies.map((company) => (
                <SelectItem key={company.id} value={company.id}>
                  {company.name || ''}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            onClick={() => {
              setEditJob(null);
              setIsCreateDialogOpen(true);
            }}
          >
            Create
          </Button>
        </div>
      </div>

      <ScrollArea className="h-[600px] w-full rounded-md border">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 sticky top-0">
              <th className="p-3 text-left font-medium text-gray-500">Logo</th>
              <th className="p-3 text-left font-medium text-gray-500">
                Company
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Job Title
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Work Type
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Applied
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Posted
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Live</th>
              <th className="p-3 text-left font-medium text-gray-500">
                Initial Approve
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Delete
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Edit</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : jobs.length === 0 ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  No jobs found
                </td>
              </tr>
            ) : (
              jobs.map((job) => (
                <tr key={job.id} className="border-b">
                  <td className="p-3">
                    {job.company &&
                    typeof job.company !== 'string' &&
                    job.company.logo_url ? (
                      <div className="w-10 h-10 relative">
                        <Image
                          src={job.company.logo_url || '/placeholder.svg'}
                          alt={job.company.name || 'Company logo'}
                          fill
                          className="object-contain rounded"
                        />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-xs text-gray-500">No logo</span>
                      </div>
                    )}
                  </td>
                  <td className="p-3">
                    {job.company && typeof job.company !== 'string'
                      ? job.company.name
                      : 'N/A'}
                  </td>
                  <td className="p-3">
                    <Link
                      href={`/startups/${job.id}`}
                      className="text-gray-900 hover:text-[#118073] font-medium underline-offset-4 hover:underline flex items-center gap-2 group"
                    >
                      {job.title || 'N/A'}
                      <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Link>
                  </td>
                  <td className="p-3">{job.work_type || 'N/A'}</td>
                  <td className="p-3">{job.applications_count || 0}</td>
                  <td className="p-3">
                    {job.created_at
                      ? new Date(job.created_at).toLocaleDateString('en-US', {
                          month: 'short',
                          day: '2-digit',
                          year: '2-digit'
                        })
                      : 'N/A'}
                  </td>
                  <td className="p-3">
                    <Switch
                      className="data-[state=checked]:bg-[#118073]"
                      checked={job.live}
                      onCheckedChange={(checked) =>
                        handleToggleLive(job.id, checked)
                      }
                    />
                  </td>
                  <td className="p-3">
                    {job.is_first_job && (
                      <span className="bg-gray-800 text-white text-xs px-2 py-1 rounded">
                        First Job
                      </span>
                    )}
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleDeleteJob(job.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <XCircleIcon className="h-5 w-5" />
                    </button>
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleEditJob(job)}
                      className="text-[#118073] hover:text-green-700"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </ScrollArea>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1 || isLoading}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages || isLoading}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      <JobFormDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        job={editJob}
        onSave={handleJobSaved}
      />
    </div>
  );
}
