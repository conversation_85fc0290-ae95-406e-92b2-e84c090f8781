'use server';

import { createClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';

/**
 * Records a file download event in the download_file_tracker table
 *
 * @param fileId - The ID of the file that was downloaded/clicked
 * @param userId - The ID of the user who downloaded the file
 * @returns An object indicating success or failure
 */
export async function trackFileDownload(
  fileId: string,
  userId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();

  try {
    // Create a new entry in the download_file_tracker table
    const { error } = await supabase.from('download_file_tracker').insert({
      id: uuidv4(),
      user_id: userId,
      file_id: fileId,
      clicked_at: new Date().toISOString()
    });

    if (error) {
      console.error('Error tracking file download:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Exception tracking file download:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error tracking download'
    };
  }
}

/**
 * Checks if a download_file_tracker entry already exists for a given user and file.
 *
 * @param fileId - The ID of the file.
 * @param userId - The ID of the user.
 * @returns A boolean indicating if the tracker entry exists.
 */
export async function checkExistingFileTracker(
  fileId: string,
  userId: string
): Promise<boolean> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('download_file_tracker')
      .select('id')
      .eq('user_id', userId)
      .eq('file_id', fileId)
      .limit(1)
      .single();

    if (error && error.code !== 'PGRST116') {
      // PGRST116: No rows found
      console.error('Error checking for existing file tracker:', error);
      return false; // Assume it doesn't exist on error, to be safe for tracking
    }

    return !!data; // True if data exists, false otherwise
  } catch (e) {
    console.error('Exception checking for existing file tracker:', e);
    return false;
  }
}
