import { cache } from 'react';
import { bubbleClient } from '@/utils/bubble/client';


  export const getTokens = () => {
    if (typeof window !== 'undefined') {
      try {
        const token = localStorage.getItem('bubble_auth_token');
        const user_id = localStorage.getItem('bubble_user_id');
        return { token, user_id };
      } catch (error) {
        console.error('Error in getTokens:', error);
        return { token: null, user_id: null };
      }
    }
    // Return null tokens if not in a browser environment
    return { token: null, user_id: null };
  };
  export const setTokens = (token: string, user_id: string) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem('bubble_auth_token', token);
        localStorage.setItem('bubble_user_id', user_id); 
      } catch (error) { 
        console.error('Error in setTokens:', error);
      }
    }} 