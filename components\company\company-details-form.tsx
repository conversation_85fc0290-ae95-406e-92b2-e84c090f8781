'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { StartupCompany } from '@/types/startups';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { ImageUpload } from '@/components/ui/image-upload';

const companyFormSchema = z.object({
  name: z.string().min(2, 'Company name must be at least 2 characters'),
  description: z.string().optional(),
  industry: z.string().optional(),
  founder_school: z.string().optional(),
  contact_email: z.string().email('Please enter a valid email').optional(),
  internal_email: z.string().email('Please enter a valid email').optional(),
  founder_linkedin: z.string().optional(),
  company_crunchbase: z.string().optional(),
  company_linkedin: z.string().optional(),
  company_website: z.string().optional(),
  funding: z.string().optional(),
  legally_incorporated: z.string().optional(),
  logo_url: z.string().optional(),
  year_of_incorporation: z.string().optional(),
  sponsorship: z.string().optional(),
  parse_resumes_to_csv: z.boolean().default(false),
  open_for_reverse_hiring: z.boolean().optional()
});

type CompanyFormValues = z.infer<typeof companyFormSchema>;

interface CompanyDetailsFormProps {
  initialData: StartupCompany;
  onSubmit: (data: Partial<StartupCompany>) => Promise<void>;
  isNewCompany?: boolean;
  onCancel?: () => void;
}

export function CompanyDetailsForm({
  initialData,
  onSubmit,
  isNewCompany = false,
  onCancel
}: CompanyDetailsFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      name: initialData.name || '',
      description: initialData.description || '',
      industry: initialData.industry || '',
      founder_school: initialData.founder_school || '',
      contact_email: initialData.contact_email || '',
      internal_email: initialData.internal_email || '',
      founder_linkedin: initialData.founder_linkedin || '',
      company_crunchbase: initialData.company_crunchbase || '',
      company_linkedin: initialData.company_linkedin || '',
      company_website: initialData.company_website || '',
      funding: initialData.funding || '',
      legally_incorporated: initialData.legally_incorporated || '',
      logo_url: initialData.logo_url || '',
      year_of_incorporation: initialData.year_of_incorporation || '',
      sponsorship: initialData.sponsorship || '',
      parse_resumes_to_csv: Boolean(initialData.parse_resumes_to_csv),
      open_for_reverse_hiring: initialData.open_for_reverse_hiring === true
    }
  });

  const handleSubmit = async (values: CompanyFormValues) => {
    setIsSubmitting(true);
    try {
      await onSubmit({
        ...values,
        parse_resumes_to_csv: values.parse_resumes_to_csv ? 'true' : 'false'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <div className="flex gap-8">
              <div className="w-24">
                <FormField
                  control={form.control}
                  name="logo_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo</FormLabel>
                      <FormControl>
                        <ImageUpload
                          value={field.value || ''}
                          onChange={field.onChange}
                          className="h-24 w-24"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex-1">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name *</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Website</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contact_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Recruiter Email * (For Receiving Resumes)
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="internal_email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Internal Email * (For InternUp)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="founder_linkedin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Recruiter LinkedIn*</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="company_linkedin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company LinkedIn</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="funding"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Whether fundraising</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="yes">Yes</SelectItem>
                        <SelectItem value="no">No</SelectItem>
                        <SelectItem value="planning">Planning</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sponsorship"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sponsorship</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="yes">Yes</SelectItem>
                        <SelectItem value="no">No</SelectItem>
                        <SelectItem value="case_by_case">
                          Case by Case
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="year_of_incorporation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Year of Legal Incorporation</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Firm Introduction</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      rows={6}
                      placeholder="Tell us about your company..."
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parse_resumes_to_csv"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value || false}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Do you need us parse resumes to CSV file?
                    </FormLabel>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="open_for_reverse_hiring"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      I am open for candidates to reach me directly and pitch
                      him/herself. I can see if I can create a position for the
                      fitted ones
                    </FormLabel>
                  </div>
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-4 mt-6">
              {!isNewCompany && onCancel && (
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting
                  ? 'Saving...'
                  : isNewCompany
                    ? 'Create Company'
                    : 'Save Changes'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
