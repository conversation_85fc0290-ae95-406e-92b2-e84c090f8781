/**
 * Utility functions for tracking user actions on jobs
 */
import { trackFileAndJobClick } from '@/actions/job-click-history';

/**
 * Tracks when a user clicks on a specific job within a daily_job file
 * Uses a combined server action to create both the file tracker and job click records
 *
 * @param fileId - The ID of the file containing the job
 * @param userId - The ID of the user clicking the job
 * @param jobDetails - Details about the specific job being clicked
 * @returns A promise that resolves to a boolean indicating success
 */
export async function trackJob<PERSON>lick(
  fileId: string,
  userId: string,
  jobDetails: {
    keyword?: string;
    jobTitle?: string;
    company?: string;
  }
): Promise<boolean> {
  try {
    if (!userId || !fileId) {
      return false;
    }

    // Track both the file access and the specific job click in one operation
    const result = await trackFileAndJobClick(fileId, userId, jobDetails);

    if (!result.success) {
      console.error('Error tracking job click:', result.error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in trackJobClick utility:', error);
    return false;
  }
}
