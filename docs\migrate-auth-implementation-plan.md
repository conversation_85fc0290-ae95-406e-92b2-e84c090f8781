# Supabase Auth Migration Implementation Plan

## Step-by-Step Migration Plan

### Phase 1: Database Schema Preparation

1. **Add migration status field to users table in Supabase**

   - Connect to your Supabase dashboard
   - Add a new column `migration_status` to the `users` table with type `migration_status_enum`
   - `migration_status_enum` are of the following:
     1. `requires_password_reset` - **Default** state for users who need to set a new password in Supabase
     2. `password_reset_sent` - User has been sent a verification email but hasn't completed the process
     3. `migrated` - User has successfully completed migration to Supabase
     4. `migration_failed` - There was an error during migration that needs attention

2. **Create migration tracking scripts**
   - Create a new utility file `actions/auth-migration.ts` (containing Server Actions for migration status management)
   - **Functions to implement:**
     - `getMigrationStatus(userId: string): Promise<MigrationStatus | null>`: Fetches the current `migration_status` for a given user ID
     - `updateMigrationStatus(userId: string, newStatus: MigrationStatus): Promise<{ success: true } | { success: false, error: any }>`: Updates the `migration_status`

### Phase 2: Authentication Flow Implementation

1. **Migration Password Reset Flow**

   - User arrives at password migration page with their email in URL parameters
   - System checks if user exists in Supabase Auth:
     - If not, creates the user in Supabase Auth using admin API
   - System sends a password reset email using Supabase's password reset functionality
   - User's migration status is updated to 'password_reset_sent'
   - User receives email with password reset link
   - User clicks link and sets their new password
   - After setting new password, user can sign in using Supabase authentication
   - Migration status is updated to 'migrated' upon successful password reset

2. **Error Handling**
   - Handle cases where user creation fails
   - Handle cases where password reset email sending fails
   - Handle cases where password reset fails
   - Provide clear error messages and recovery paths for users

### Phase 3: User Communication

1. **Create notification components**

   - Build clear success/error messages throughout the migration flow
   - Implement loading states during async operations

2. **Email templates**
   - Use Supabase's built-in password reset email template
   - Ensure emails contain clear instructions for password reset process

### Phase 4: Post-Migration Cleanup

1. **Monitor migration progress**

   - Track number of users successfully migrated
   - Monitor for failed migrations
   - Address any systematic issues discovered

2. **Clean up auth flows**
   - Remove migration-specific components once all users are migrated
   - Simplify auth to standard Supabase flow

### Success Criteria

1. All existing users can successfully:

   - Get created in Supabase Auth if they don't exist
   - Receive password reset emails
   - Set up their new passwords
   - Sign in using Supabase authentication

2. Migration status is properly tracked and updated throughout the process

3. Clear error handling and recovery paths are available for all failure cases

4. System maintains security throughout the migration process
