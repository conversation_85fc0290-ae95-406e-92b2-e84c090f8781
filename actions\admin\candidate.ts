import { createClient } from '@/utils/supabase/client';
import { deleteImage } from '@/utils/supabase/storage/client';
import type {
  User,
  CandidateUpdateData,
  CandidatePreferencesUpdateData,
  CandidateQueryParams
} from '@/types/user';
import PaginatedResponse from '@/types/pagination';

export async function getCandidate(id: string): Promise<User> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', id)
    .eq('user_type', 'Candidate')
    .single();

  if (error) {
    console.error('Error fetching candidate:', error);
    throw new Error('Failed to fetch candidate');
  }

  return data as User;
}

export async function updateCandidateProfile(
  id: string,
  profileData: CandidateUpdateData
): Promise<User> {
  const supabase = createClient();

  try {
    // First, verify the user exists and is a candidate
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .eq('user_type', 'Candidate')
      .single();

    if (fetchError || !existingUser) {
      console.error('Error fetching existing user:', fetchError);
      throw new Error('User not found or not a candidate');
    }

    // Clean up the update data by removing undefined values
    const cleanedData = Object.entries(profileData).reduce(
      (acc, [key, value]) => {
        if (value !== undefined && key !== 'modified_at') {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, any>
    );

    // Perform the update
    const { data, error } = await supabase
      .from('users')
      .update({
        ...cleanedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating candidate profile:', error);
      throw new Error(`Failed to update candidate profile: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data returned after update');
    }

    return data as User;
  } catch (error) {
    console.error('Error in updateCandidateProfile:', error);
    throw error instanceof Error
      ? error
      : new Error('Failed to update candidate profile');
  }
}

export async function updateCandidatePreferences(
  id: string,
  preferences: CandidatePreferencesUpdateData
): Promise<User> {
  const supabase = createClient();

  try {
    // First, verify the user exists and is a candidate
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .eq('user_type', 'Candidate')
      .single();

    if (fetchError || !existingUser) {
      console.error('Error fetching existing user:', fetchError);
      throw new Error('User not found or not a candidate');
    }

    // Clean up preferences data
    const { modified_at, ...cleanedPreferences } = preferences;

    // Perform the update
    const { data, error } = await supabase
      .from('users')
      .update({
        ...cleanedPreferences,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating candidate preferences:', error);
      throw new Error(
        `Failed to update candidate preferences: ${error.message}`
      );
    }

    if (!data) {
      throw new Error('No data returned after update');
    }

    return data as User;
  } catch (error) {
    console.error('Error in updateCandidatePreferences:', error);
    throw error instanceof Error
      ? error
      : new Error('Failed to update candidate preferences');
  }
}

export async function getCandidates({
  page = 1,
  searchTerm = '',
  location = '',
  workType = '',
  sponsorship = ''
}: CandidateQueryParams): Promise<PaginatedResponse<User>> {
  const supabase = createClient();
  const pageSize = 10;
  const startIndex = (page - 1) * pageSize;

  let query = supabase
    .from('users')
    .select('*', { count: 'exact' })
    .eq('user_type', 'Candidate');

  if (searchTerm) {
    query = query.or(
      `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`
    );
  }

  if (location) {
    query = query.eq('location', location);
  }

  if (workType) {
    query = query.contains('work_types', [workType]);
  }

  if (sponsorship) {
    query = query.eq('need_sponsorship', sponsorship);
  }

  const { data, error, count } = await query
    .order('created_at', { ascending: false })
    .range(startIndex, startIndex + pageSize - 1);

  if (error) {
    console.error('Error fetching candidates:', error);
    throw new Error('Failed to fetch candidates');
  }

  const totalPages = Math.ceil((count || 0) / pageSize);

  return {
    data: data as User[],
    totalPages,
    totalCount: count ?? 0
  };
}
