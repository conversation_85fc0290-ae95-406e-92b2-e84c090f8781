import { createClient } from '@/utils/supabase/server';
import {
  MigrationStatus,
  MigrationStatusData,
  MigrationStatusResponse,
  UsersByStatusResponse,
  MigrationError,
  MIGRATION_ERROR_CODES,
  MigrationErrorCode
} from '@/types/auth-migration';
import { User } from '@/types/user';

const DEFAULT_PAGE_SIZE = 100;
const MAX_PAGE_SIZE = 1000;

/**
 * Validates if a migration status is valid
 * @param status The status to validate
 * @returns boolean indicating if the status is valid
 */
function isValidMigrationStatus(status: string): status is MigrationStatus {
  return Object.values(MigrationStatus).includes(status as MigrationStatus);
}

/**
 * Get the migration status for a user
 * @param userId The ID of the user to get the migration status for
 * @returns The migration status for the user
 * @throws {MigrationError} If there is an error fetching the status
 */
export async function getMigrationStatus(
  userId: string
): Promise<MigrationStatusResponse> {
  try {
    if (!userId) {
      throw new MigrationError(
        'User ID is required',
        MIGRATION_ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('migration_status')
      .eq('id', userId)
      .single();

    if (error) {
      throw new MigrationError(
        `Error fetching migration status: ${error.message}`,
        MIGRATION_ERROR_CODES.DATABASE_ERROR
      );
    }

    if (!data) {
      throw new MigrationError(
        `No data found for user: ${userId}`,
        MIGRATION_ERROR_CODES.USER_NOT_FOUND,
        404
      );
    }

    if (!isValidMigrationStatus(data.migration_status)) {
      throw new MigrationError(
        `Invalid migration status: ${data.migration_status}`,
        MIGRATION_ERROR_CODES.INVALID_STATUS
      );
    }

    return {
      success: true,
      data: data.migration_status
    };
  } catch (error) {
    if (error instanceof MigrationError) {
      return {
        success: false,
        error: error.message
      };
    }
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Update the migration status for a user
 * @param userId The ID of the user to update the migration status for
 * @param statusData The new migration status data
 * @returns The updated migration status
 * @throws {MigrationError} If there is an error updating the status
 */
export async function updateMigrationStatus(
  userId: string,
  statusData: MigrationStatusData
): Promise<MigrationStatusResponse> {
  try {
    if (!userId) {
      throw new MigrationError(
        'User ID is required',
        MIGRATION_ERROR_CODES.VALIDATION_ERROR,
        400
      );
    }

    if (!isValidMigrationStatus(statusData.migration_status)) {
      throw new MigrationError(
        `Invalid migration status: ${statusData.migration_status}`,
        MIGRATION_ERROR_CODES.INVALID_STATUS,
        400
      );
    }

    const supabase = createClient();
    statusData.updated_at = new Date().toISOString();

    const { data, error } = await supabase
      .from('users')
      .update(statusData)
      .eq('id', userId)
      .select('migration_status')
      .single();

    if (error) {
      throw new MigrationError(
        `Error updating migration status: ${error.message}`,
        MIGRATION_ERROR_CODES.DATABASE_ERROR
      );
    }

    if (!data) {
      throw new MigrationError(
        `No data found for user: ${userId}`,
        MIGRATION_ERROR_CODES.USER_NOT_FOUND,
        404
      );
    }

    return {
      success: true,
      data: data.migration_status
    };
  } catch (error) {
    if (error instanceof MigrationError) {
      return {
        success: false,
        error: error.message
      };
    }
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}

/**
 * Get users by migration status
 * @param status The migration status to filter by
 * @param limit The number of users to return (default: 100, max: 1000)
 * @param offset The offset to start the results from
 * @returns The users that match the migration status
 * @throws {MigrationError} If there is an error fetching the users
 */
export async function getUsersByMigrationStatus(
  status: MigrationStatus,
  limit: number = DEFAULT_PAGE_SIZE,
  offset: number = 0
): Promise<UsersByStatusResponse> {
  try {
    if (!isValidMigrationStatus(status)) {
      throw new MigrationError(
        `Invalid migration status: ${status}`,
        MIGRATION_ERROR_CODES.INVALID_STATUS,
        400
      );
    }

    const pageSize = Math.min(limit, MAX_PAGE_SIZE);
    const supabase = createClient();

    const { data, error } = await supabase
      .from('users')
      .select('*', { count: 'exact' })
      .eq('migration_status', status)
      .range(offset, offset + pageSize - 1);

    if (error) {
      throw new MigrationError(
        `Error fetching users by migration status: ${error.message}`,
        MIGRATION_ERROR_CODES.DATABASE_ERROR
      );
    }

    return {
      success: true,
      data: data as User[],
      total: data.length
    };
  } catch (error) {
    if (error instanceof MigrationError) {
      return {
        success: false,
        error: error.message
      };
    }
    return {
      success: false,
      error: 'An unexpected error occurred'
    };
  }
}
