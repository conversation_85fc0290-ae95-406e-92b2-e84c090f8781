---
title: Developing in Production
description: Setting up Stripe for Production
---

import { Step, Steps } from 'fumadocs-ui/components/steps';

If you don't already have a Stripe account, create one now.

For the following steps, make sure you have the ["Test Mode" toggle](https://stripe.com/docs/testing) switched on.

## Development in Production

We need to create a webhook in the `Developers` section of Stripe. Pictured in the architecture diagram above, this webhook is the piece that connects Stripe to your Vercel Serverless Functions.

<div className="steps">
<div className="step">
Click the "Add Endpoint" button on the [test Endpoints page](https://dashboard.stripe.com/test/webhooks).
</div>
<div className="step">
Enter your production deployment URL followed by `/api/webhooks` for the endpoint URL. (e.g. `https://your-deployment-url.vercel.app/api/webhooks`)
</div>
<div className="step">
Click `Select events` under the `Select events to listen to` heading.
</div>
<div className="step">
Click the following events:

- 'product.created'
- 'product.updated'
- 'product.deleted'
- 'price.created'
- 'price.updated'
- 'price.deleted'
- 'checkout.session.completed'
- 'customer.subscription.created'
- 'customer.subscription.updated'
- 'customer.subscription.deleted'
</div>
<div className="step">
Copy `Signing secret` as we'll need that in the next step (e.g `whsec_xxx`) (!be careful not to copy the webhook ID `we_xxxx`).
</div>
<div className="step">
In addition to the `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY` and the `STRIPE_SECRET_KEY` we've set earlier during deployment, we need to add the webhook secret as `STRIPE_WEBHOOK_SECRET` env var.
</div>
<div className="step">
By now your .env file should look like this:

```bash title=".env"
# Get these from Stripe dashboard
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_51PXT**********************************PpPple1"
STRIPE_SECRET_KEY="sk_live_5********************************************I7h"
STRIPE_WEBHOOK_SECRET="whsec_la483**********************************d2118"
```

</div>
</div>

## Create product and pricing information

Your application's webhook listens for product updates on Stripe and automatically propagates them to your Supabase database. So with your webhook listener running, you can now create your product and pricing information in the [Stripe Dashboard](https://dashboard.stripe.com/test/products).

Stripe Checkout currently supports pricing that bills a predefined amount at a specific interval. More complex plans (e.g., different pricing tiers or seats) are not yet supported.

For example, you can create business models with different pricing tiers, e.g.:

<div className="border-2 rounded-lg mx-8 px-10">
  | Product | Monthly Price | Yearly Price |
  |-------------|----------------|--------------| 
  | Hobby | 10 USD | 100 USD |
  | Freelancer | 20 USD | 200 USD |
  | Pro | 40 USD | 400 USD |
</div>

Optionally, to speed up the setup, we have added a <strong>fixtures file</strong> `utils/stripe/fixtures/stripe-fixtures.json` to bootstrap test product and pricing data in your Stripe account. Edit this file according to your products pricing scheme. The [Stripe CLI](https://stripe.com/docs/stripe-cli#install) `fixtures` command executes a series of API requests defined in this JSON file. Simply run `pnpm stripe:fixtures`.

**Important:** Make sure that you've configured your Stripe webhook correctly and redeployed with all needed environment variables.

## Configure the Stripe customer portal

<div className="steps">
  <div className="step">
    Set your custom branding in the
    [settings](https://dashboard.stripe.com/settings/branding).
  </div>
  <div className="step">
    Configure the Customer Portal
    [settings](https://dashboard.stripe.com/test/settings/billing/portal).
  </div>
  <div className="step">
    Toggle on "Allow customers to update their payment methods".
  </div>
  <div className="step">
    Toggle on "Allow customers to update subscriptions".
  </div>
  <div className="step">
    Toggle on "Allow customers to cancel subscriptions".
  </div>
  <div className="step">Add the products and prices that you want.</div>
  <div className="step">
    Set up the required business information and links.
  </div>
</div>
