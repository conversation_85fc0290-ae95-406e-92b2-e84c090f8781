'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { Upload } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import type { CreateReferralInput } from '@/types/referral';
import { uploadFile } from '@/utils/supabase/storage/client';
import {
  createReferral,
  getWeeklyReferralCount,
  checkReferralExists
} from '@/actions/job-market/publicFirm-referrals';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { v4 as uuidv4 } from 'uuid';
import { useReferralLimit } from '@/hooks/useReferralLimit';
import { Textarea } from '@/components/ui/textarea';

// Define the form validation schema
const referralFormSchema = z
  .object({
    id: z.string().uuid(),
    requested_company: z.string().min(1, 'Company name is required'),
    job_title: z.string().min(1, 'Job title is required'),
    job_listing_url: z
      .string()
      .url('Please enter a valid URL starting with http:// or https://'),
    job_id: z.string().uuid(),
    candidate_linkedin: z
      .string()
      .url(
        'Please enter a valid LinkedIn URL starting with http:// or https://'
      ),
    candidate_phone_number: z
      .string()
      .regex(/^\+?[\d\s-()]+$/, 'Please enter a valid phone number'),
    candidate_resume: z
      .string()
      .min(1, 'Resume is required')
      .or(z.instanceof(File)),
    useExistingResume: z.boolean().default(true),
    self_introduction: z
      .string()
      .min(1, 'Self-introduction is required.')
      .max(
        500,
        'Self-introduction must be 100 words or less (approx. 500 characters).'
      )
  })
  .refine(
    (data) => {
      // Validate that either useExistingResume is true OR resume is provided
      return data.useExistingResume === true || data.candidate_resume;
    },
    {
      message: 'Please provide a resume or use your existing resume',
      path: ['candidate_resume']
    }
  );

type ReferralFormValues = z.infer<typeof referralFormSchema>;

interface ReferralRequestButtonProps {
  jobId: string;
  jobTitle: string;
  applyLink?: string;
  referralLink: string;
  companyName: string;
  companyId?: string;
  hasApplied: boolean;
  fullWidth?: boolean;
  disabled?: boolean;
  user: any;
  jobType?: 'startup' | 'publicFirm';
  onApplicationSuccess?: (application: any) => void;
}

export const ReferralRequestButton = ({
  jobId,
  jobTitle,
  applyLink,
  companyName,
  user,
  disabled = false,
  fullWidth = false
}: ReferralRequestButtonProps) => {
  const [isLimitDialogOpen, setIsLimitDialogOpen] = useState(false);
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [isPremiumDialogOpen, setIsPremiumDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingResume, setUploadingResume] = useState(false);
  const [selectedFileName, setSelectedFileName] = useState<string>('');
  const [weeklyReferralCount, setWeeklyReferralCount] = useState<number>(0);
  const [hasReachedLimit, setHasReachedLimit] = useState(false);
  const [hasRequestedReferral, setHasRequestedReferral] = useState(false);
  const [isCheckingReferral, setIsCheckingReferral] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const { toast } = useToast();
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  // Check if user has premium access (VIP or active subscription)
  const hasPremiumAccess = user?.vip === true;

  // Fetch the user's weekly referral count
  const checkWeeklyReferralLimit = async () => {
    if (!user?.id) return;

    try {
      setIsCheckingReferral(true);
      const count = await getWeeklyReferralCount(user.id);
      setWeeklyReferralCount(count);
      setHasReachedLimit(count >= 2); // 2 is the weekly limit
    } catch (error) {
      console.error('Error checking referral limit:', error);
      toast({
        title: 'Error',
        description:
          'Could not check your weekly referral count. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsCheckingReferral(false);
    }
  };

  // Check if user has already requested a referral for this job
  useEffect(() => {
    const checkExistingReferral = async () => {
      if (!user?.id || !jobId) return;

      try {
        setIsCheckingReferral(true);
        const exists = await checkReferralExists(user.id, jobId);
        setHasRequestedReferral(exists);
      } catch (error) {
        console.error('Error checking referral status:', error);
      } finally {
        setIsCheckingReferral(false);
      }
    };

    checkExistingReferral();
  }, [user?.id, jobId]);

  const form = useForm<ReferralFormValues>({
    resolver: zodResolver(referralFormSchema),
    defaultValues: {
      id: uuidv4(),
      requested_company: companyName,
      job_title: jobTitle,
      job_listing_url: applyLink || '',
      job_id: jobId,
      candidate_linkedin: user?.linkedin_url ?? '',
      candidate_phone_number: user?.phone_number ?? '',
      candidate_resume: '',
      useExistingResume: !!user?.resume_url,
      self_introduction: user?.bio ?? ''
    },
    mode: 'onChange'
  });

  useEffect(() => {
    if (user && companyName && jobTitle) {
      form.reset({
        id: uuidv4(),
        requested_company: companyName,
        job_title: jobTitle,
        job_listing_url: applyLink || '',
        job_id: jobId,
        candidate_linkedin: user?.linkedin_url ?? '',
        candidate_phone_number: user?.phone_number ?? '',
        candidate_resume: '',
        useExistingResume: !!user?.resume_url,
        self_introduction: user?.bio ?? ''
      });

      form.trigger();
    }
  }, [user, companyName, jobTitle, applyLink, jobId, form]);

  const handleClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (disabled) return;

    if (!user) {
      router.push('/signin');
      return;
    }

    // Check if user has premium access
    if (!hasPremiumAccess) {
      setIsPremiumDialogOpen(true);
      return;
    }

    // Check weekly referral limit before showing the dialog
    await checkWeeklyReferralLimit();
    setIsLimitDialogOpen(true);
  };

  const handleContinueToForm = () => {
    if (hasReachedLimit) {
      toast({
        title: 'Weekly Limit Reached',
        description:
          'You have already used your 2 referral requests this week. Please try again later.',
        variant: 'destructive'
      });
      setIsLimitDialogOpen(false);
      return;
    }

    setIsLimitDialogOpen(false);
    setIsFormDialogOpen(true);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (7MB limit)
    if (file.size > 7 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please upload a file smaller than 7MB.',
        variant: 'destructive'
      });
      return;
    }

    // Check file type (PDF only)
    if (file.type !== 'application/pdf') {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a PDF file only.',
        variant: 'destructive'
      });
      return;
    }

    setSelectedFileName(file.name);
    form.setValue('candidate_resume', file);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const validateForm = () => {
    // Manual validation of all fields to ensure prepopulated ones are properly checked
    const values = form.getValues();
    let isValid = true;

    // Check all required fields
    if (!values.requested_company) {
      form.setError('requested_company', {
        message: 'Company name is required'
      });
      isValid = false;
    } else {
      form.clearErrors('requested_company');
    }

    if (!values.job_title) {
      form.setError('job_title', { message: 'Job title is required' });
      isValid = false;
    } else {
      form.clearErrors('job_title');
    }

    if (!values.job_listing_url || !values.job_listing_url.startsWith('http')) {
      form.setError('job_listing_url', { message: 'Valid URL is required' });
      isValid = false;
    } else {
      form.clearErrors('job_listing_url');
    }

    if (!values.candidate_phone_number) {
      form.setError('candidate_phone_number', {
        message: 'Phone number is required'
      });
      isValid = false;
    } else {
      form.clearErrors('candidate_phone_number');
    }

    if (
      !values.candidate_linkedin ||
      !values.candidate_linkedin.startsWith('http')
    ) {
      form.setError('candidate_linkedin', {
        message: 'Valid LinkedIn URL is required'
      });
      isValid = false;
    } else {
      form.clearErrors('candidate_linkedin');
    }

    // Resume validation
    if (!values.useExistingResume && !values.candidate_resume) {
      form.setError('candidate_resume', { message: 'Resume is required' });
      isValid = false;
    } else {
      form.clearErrors('candidate_resume');
    }

    // Self-introduction validation
    if (!values.self_introduction) {
      form.setError('self_introduction', {
        message: 'Self-introduction is required'
      });
      isValid = false;
    } else if (values.self_introduction.length > 500) {
      form.setError('self_introduction', {
        message:
          'Self-introduction must be 100 words or less (approx. 500 characters).'
      });
      isValid = false;
    } else {
      form.clearErrors('self_introduction');
    }

    return isValid;
  };

  const onSubmit = async (data: ReferralFormValues) => {
    setIsSubmitting(true);
    try {
      // Check weekly referral limit one more time before submitting
      const currentCount = await getWeeklyReferralCount(user.id);

      if (currentCount >= 2) {
        toast({
          title: 'Weekly Limit Reached',
          description:
            'You have already used your 2 referral requests this week. Please try again later.',
          variant: 'destructive'
        });
        setIsSubmitting(false);
        setIsFormDialogOpen(false);
        return;
      }

      let resumeUrl = '';

      // Check if user wants to use existing resume
      if (data.useExistingResume) {
        if (!user?.resume_url) {
          console.error('❌ No existing resume found');
          toast({
            title: 'Resume Required',
            description: 'No existing resume found. Please upload a resume.',
            variant: 'destructive'
          });
          setIsSubmitting(false);
          return;
        }
        resumeUrl = user.resume_url;
      } else {
        // Upload new resume if it's a file
        if (data.candidate_resume instanceof File) {
          setUploadingResume(true);
          try {
            const result = await uploadFile({
              file: data.candidate_resume,
              bucket: 'candidate-resumes',
              folder: `public_referral_user_${user.id || user.email}_${jobId}`
            });

            if (result.error) {
              console.error('❌ Resume upload failed:', result.error);
              toast({
                title: 'Resume Upload Failed',
                description: result.error,
                variant: 'destructive'
              });
              setIsSubmitting(false);
              return;
            }

            resumeUrl = result.fileUrl;
          } catch (uploadError) {
            console.error('❌ Exception during resume upload:', uploadError);
            toast({
              title: 'Resume Upload Error',
              description: 'An error occurred while uploading your resume.',
              variant: 'destructive'
            });
            setIsSubmitting(false);
            return;
          } finally {
            setUploadingResume(false);
          }
        } else if (
          typeof data.candidate_resume === 'string' &&
          data.candidate_resume
        ) {
          // If it's already a URL string
          resumeUrl = data.candidate_resume;
        } else {
          console.error('❌ No resume provided');
          toast({
            title: 'Resume Required',
            description: 'Please provide a resume.',
            variant: 'destructive'
          });
          setIsSubmitting(false);
          return;
        }
      }

      // Prepare the referral request data
      const referralData: CreateReferralInput = {
        id: data.id,
        requested_company: data.requested_company,
        job_title: data.job_title,
        job_listing_url: data.job_listing_url,
        job_id: jobId,
        candidate_id: user.id,
        candidate_linkedin: data.candidate_linkedin,
        candidate_phone_number: data.candidate_phone_number,
        candidate_resume: resumeUrl,
        status: 'pending',
        self_introduction: data.self_introduction
      };

      // Submit the referral request using the server action
      try {
        const result = await createReferral(referralData);

        toast({
          title: 'Referral Request Submitted',
          description:
            'Your referral request has been submitted successfully. You will be notified when an insider responds.'
        });

        // Update the referral count
        setWeeklyReferralCount((prev) => prev + 1);
        setHasReachedLimit(weeklyReferralCount + 1 >= 2);

        // Close the dialog
        setIsFormDialogOpen(false);

        // Update the button state
        setHasRequestedReferral(true);

        // Refresh the page to update UI if needed
        router.refresh();
      } catch (submitError) {
        console.error('❌ Error during server action call:', submitError);
        toast({
          title: 'Submission Error',
          description: 'Could not submit referral request. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('❌ Top-level error in submission process:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit referral request. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form state when dialog opens/closes
  useEffect(() => {
    if (isFormDialogOpen) {
      // Reset the form with initial values when dialog opens
      form.reset({
        id: uuidv4(),
        requested_company: companyName,
        job_title: jobTitle,
        job_listing_url: applyLink || '',
        job_id: jobId,
        candidate_linkedin: user?.linkedin_url ?? '',
        candidate_phone_number: user?.phone_number ?? '',
        candidate_resume: '',
        useExistingResume: !!user?.resume_url,
        self_introduction: user?.bio ?? ''
      });

      // Reset submission attempt state
      setHasAttemptedSubmit(false);
    }
  }, [isFormDialogOpen, companyName, jobTitle, applyLink, jobId, user, form]);

  // Modify the form watch effect to better handle error state
  useEffect(() => {
    if (hasAttemptedSubmit) {
      const subscription = form.watch(() => {
        // Check if the form can be submitted - either directly check validity
        // or individually validate each required field
        const requiredFields = [
          'requested_company',
          'job_title',
          'job_listing_url',
          'candidate_phone_number',
          'candidate_linkedin',
          'self_introduction'
        ];

        // Check if all required fields are filled and valid
        const hasErrors = requiredFields.some(
          (field) => form.getFieldState(field as any).error !== undefined
        );

        // If there's no resume and we're not using an existing one, check that too
        if (
          !form.getValues('useExistingResume') &&
          (!form.getValues('candidate_resume') ||
            form.getFieldState('candidate_resume').error)
        ) {
          // Still has errors
        } else if (!hasErrors) {
          // No errors detected, clear the error state
          setHasAttemptedSubmit(false);
        }
      });

      return () => subscription.unsubscribe();
    }
  }, [form, hasAttemptedSubmit]);

  return (
    <>
      <Button
        variant="outline"
        className={`${fullWidth ? 'w-full' : ''} border-[#36BA98] text-[#36BA98] hover:bg-[#36BA98] hover:text-white`}
        onClick={handleClick}
        disabled={disabled || isCheckingReferral || hasRequestedReferral}
      >
        {isCheckingReferral
          ? 'Checking...'
          : hasRequestedReferral
            ? 'Referral Requested'
            : 'Request Referral'}
      </Button>

      {/* Premium Feature Dialog */}
      <Dialog open={isPremiumDialogOpen} onOpenChange={setIsPremiumDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Premium Feature</DialogTitle>
            <DialogDescription>
              Referral requests are only available to premium users. Upgrade
              your account to access this feature.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert>
              <AlertTitle>Subscription Required</AlertTitle>
              <AlertDescription>
                This feature is only available to users with VIP status.
                Subscribe to gain access to exclusive referral opportunities.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsPremiumDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-[#36BA98] hover:bg-[#2da885] text-white"
              onClick={() => {
                setIsPremiumDialogOpen(false);
                router.push('/membership'); // Update this to your subscription page path
              }}
            >
              Upgrade Now
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Weekly Limit Dialog */}
      <Dialog open={isLimitDialogOpen} onOpenChange={setIsLimitDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Referral Request Limit</DialogTitle>
            <DialogDescription>
              Please note that you can only request up to{' '}
              <span className="font-bold">2 referrals per week</span>. This
              helps ensure quality and fairness for all users.
            </DialogDescription>
          </DialogHeader>

          <div className="py-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">
                Your weekly referral requests:
              </p>
              <Badge
                variant={weeklyReferralCount >= 2 ? 'destructive' : 'secondary'}
              >
                {weeklyReferralCount} / 2
              </Badge>
            </div>
          </div>

          {hasReachedLimit && (
            <Alert variant="destructive">
              <AlertTitle>Weekly Limit Reached</AlertTitle>
              <AlertDescription>
                You have already used your 2 referral requests this week. Please
                try again later.
              </AlertDescription>
            </Alert>
          )}

          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setIsLimitDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-[#36BA98] hover:bg-[#2da885] text-white"
              onClick={handleContinueToForm}
              disabled={hasReachedLimit}
            >
              Continue
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Referral Request Form Dialog */}
      <Dialog
        open={isFormDialogOpen}
        onOpenChange={(open) => {
          setIsFormDialogOpen(open);
          if (!open) {
            // Reset form state when dialog closes
            setHasAttemptedSubmit(false);
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Request Referral</DialogTitle>
            <DialogDescription>
              Please fill out the form below to request a referral for{' '}
              {jobTitle} at {companyName}.
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form
              onSubmit={(e) => {
                e.preventDefault();

                // Manual validation to ensure prepopulated fields are checked
                const isValid = validateForm();

                if (isValid) {
                  const data = form.getValues();
                  onSubmit(data);
                } else {
                  setHasAttemptedSubmit(true);
                }
              }}
              className="space-y-3"
            >
              {hasAttemptedSubmit &&
                Object.keys(form.formState.errors).length > 0 && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertTitle>Missing Required Fields</AlertTitle>
                    <AlertDescription>
                      Please fill in all required fields marked with an asterisk
                      (*).
                    </AlertDescription>
                  </Alert>
                )}

              <FormField
                control={form.control}
                name="requested_company"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Company Name <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="job_title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Role Title <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="job_listing_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Job Listing URL <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="https://..." />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="candidate_phone_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Your Phone Number <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="(*************" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="candidate_linkedin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Your LinkedIn Profile{' '}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="https://linkedin.com/..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="self_introduction"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Self Introduction (max 100 words / 500 chars){' '}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Introduce yourself in the third person..."
                        rows={4}
                        maxLength={500}
                        className="resize-none"
                      />
                    </FormControl>
                    <FormDescription>
                      Write a brief introduction about yourself in the third
                      person. (Current characters: {field.value?.length || 0} /
                      500)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium flex items-center gap-1">
                    Resume <span className="text-red-500">*</span>
                  </h3>
                  {user?.resume_url && (
                    <Badge
                      variant="outline"
                      className="bg-green-50 text-green-700"
                    >
                      Resume Available
                    </Badge>
                  )}
                </div>

                {user?.resume_url ? (
                  <>
                    <div className="p-3 border rounded-md bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 flex-shrink-0 bg-blue-100 rounded-md flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-blue-700"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </div>
                        <div className="flex-1 overflow-hidden">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {user.resume_url.split('/').pop() || 'Your Resume'}
                          </p>
                          <p className="text-xs text-gray-500">
                            Will be used for your referral request
                          </p>
                        </div>
                        <a
                          href={user.resume_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:text-blue-800"
                        >
                          View
                        </a>
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="useExistingResume"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 py-2">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 text-[#36BA98] border-gray-300 rounded"
                            />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Use my existing resume</FormLabel>
                            <FormDescription>
                              Uncheck to upload a different resume for this
                              referral request
                            </FormDescription>
                          </div>
                        </FormItem>
                      )}
                    />

                    {!form.watch('useExistingResume') && (
                      <FormField
                        control={form.control}
                        name="candidate_resume"
                        render={({ field: { value, onChange, ...field } }) => (
                          <FormItem className="mb-1">
                            <FormLabel className="text-sm flex items-center gap-1">
                              Upload New Resume{' '}
                              <span className="text-red-500">*</span>
                            </FormLabel>
                            <FormControl>
                              <div className="flex gap-2">
                                <input
                                  type="file"
                                  ref={fileInputRef}
                                  accept=".pdf"
                                  onChange={handleFileChange}
                                  className="hidden"
                                />
                                <div className="flex-1 px-3 py-2 border rounded-md text-sm">
                                  {selectedFileName || 'No file selected'}
                                </div>
                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={handleUploadClick}
                                  disabled={uploadingResume}
                                >
                                  <Upload className="h-4 w-4 mr-2" />
                                  {uploadingResume ? 'Uploading...' : 'Upload'}
                                </Button>
                              </div>
                            </FormControl>
                            <FormDescription className="text-xs">
                              Please upload your resume (PDF only, max 7MB)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </>
                ) : (
                  <FormField
                    control={form.control}
                    name="candidate_resume"
                    render={({ field: { value, onChange, ...field } }) => (
                      <FormItem className="mb-1">
                        <FormLabel className="text-sm flex items-center gap-1">
                          Upload Resume <span className="text-red-500">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="flex gap-2">
                            <input
                              type="file"
                              ref={fileInputRef}
                              accept=".pdf"
                              onChange={handleFileChange}
                              className="hidden"
                            />
                            <div className="flex-1 px-3 py-2 border rounded-md text-sm">
                              {selectedFileName || 'No file selected'}
                            </div>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handleUploadClick}
                              disabled={uploadingResume}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              {uploadingResume ? 'Uploading...' : 'Upload'}
                            </Button>
                          </div>
                        </FormControl>
                        <FormDescription className="text-xs">
                          Please upload your resume (PDF only, max 7MB)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              <div className="text-sm text-muted-foreground mt-4">
                <span className="text-red-500">*</span> Required fields
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsFormDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#36BA98] hover:bg-[#2da885]"
                  disabled={isSubmitting || uploadingResume}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Request'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};
