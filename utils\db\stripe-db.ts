import { createClient } from '@/utils/supabase/server';
import <PERSON><PERSON> from 'stripe';
import { v4 as uuidv4 } from 'uuid';

// Create or retrieve a customer from Stripe
export async function createOrRetrieveCustomer({
  uuid,
  email
}: {
  uuid: string;
  email: string;
}): Promise<string> {
  const supabase = createClient();
  const { stripe } = await import('@/utils/stripe/config');
  
  try {
    // First, try to find an existing customer in Stripe by email
    const customers = await stripe.customers.list({
      email: email,
      limit: 1
    });
    
    if (customers.data.length > 0) {
      // Customer exists, return their ID
      return customers.data[0].id;
    }
    
    // If no customer found by email, check our subscriptions table
    const { data: subscriptionData } = await supabase
      .from('subscriptions')
      .select('subscription_id')
      .eq('owner_email', email)
      .limit(1);
      
    if (subscriptionData && subscriptionData.length > 0) {
      try {
        // If user has a subscription, retrieve the customer ID from Stripe
        const subscription = await stripe.subscriptions.retrieve(subscriptionData[0].subscription_id);
        return subscription.customer as string;
      } catch (err) {
        console.error('Error retrieving subscription:', err);
        // If subscription retrieval fails, continue to create a new customer
      }
    }
    
    // Otherwise, create a new customer in Stripe
    const customerData = {
      metadata: {
        supabaseUUID: uuid,
        userEmail: email
      },
      email
    };
    
    const customer = await stripe.customers.create(customerData);
    return customer.id;
  } catch (error) {
    console.error('Error in createOrRetrieveCustomer:', error);
    throw error;
  }
}

// Manage subscription status change
export async function manageSubscriptionStatusChange(
  subscriptionId: string,
  customerId: string,
  createAction = false
): Promise<void> {
  const supabase = createClient();
  const { stripe } = await import('@/utils/stripe/config');
  
  // Get subscription details from Stripe
  const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
    expand: ['default_payment_method', 'items.data.price.product', 'customer']
  });
  
  // Get customer details to map to user
  const customer = await stripe.customers.retrieve(customerId) as Stripe.Customer;
  
  // Get the user email from customer
  const userEmail = customer.email || customer.metadata?.userEmail || '';
  
  if (!userEmail) {
    console.error('No user email found in customer data');
    throw new Error('Unable to find user email in customer data');
  }
  
  // Get user ID from database
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('email', userEmail)
    .single();
    
  if (userError || !userData) {
    console.error('Error finding user by email:', userError);
    throw new Error('Unable to find user by email');
  }
  
  // Format subscription data for your table
  const subscriptionData = {
    id: uuidv4(), // Generate a unique ID
    cancelable_date: subscription.cancel_at ? new Date(subscription.cancel_at * 1000).toISOString() : null,
    end_date: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null,
    owner_email: userEmail,
    owner_id: userData.id, // Set the actual user ID
    plan: (subscription.items.data[0]?.price.product as Stripe.Product).name,
    start_date: new Date(subscription.current_period_start * 1000).toISOString(),
    status: subscription.status,
    subscription_id: subscription.id,
    creation_date: new Date(subscription.created * 1000).toISOString(),
    modified_date: new Date().toISOString(),
    slug: (subscription.items.data[0]?.price.product as Stripe.Product).id,
    creator: userEmail
  };
  
  // Check if subscription already exists
  const { data: existingSubscription, error: checkError } = await supabase
    .from('subscriptions')
    .select('id')
    .eq('subscription_id', subscription.id)
    .maybeSingle();
    
  if (checkError) {
    console.error('Error checking existing subscription:', checkError);
    throw checkError;
  }
  
  let subscriptionError;
  
  if (existingSubscription) {
    // Update existing subscription
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: subscription.status,
        modified_date: new Date().toISOString(),
        end_date: subscription.current_period_end ? new Date(subscription.current_period_end * 1000).toISOString() : null,
        cancelable_date: subscription.cancel_at ? new Date(subscription.cancel_at * 1000).toISOString() : null,
        plan: (subscription.items.data[0]?.price.product as Stripe.Product).name
      })
      .eq('id', existingSubscription.id);
      
    subscriptionError = error;
  } else {
    // Insert new subscription
    const { error } = await supabase
      .from('subscriptions')
      .insert([subscriptionData]);
      
    subscriptionError = error;
  }
  
  if (subscriptionError) {
    console.error('Error managing subscription record:', subscriptionError);
    throw subscriptionError;
  }

  // Determine if this is a subscription type that should have VIP status
  // Based on the new subscription plan names
  const planName = (subscription.items.data[0]?.price.product as Stripe.Product).name;
  
  // Check if the plan is one of the VIP plans or higher tier plans
  const isVipPlan = planName.includes('VIP') || 
                    planName === 'Pro Monthly Pass' || 
                    planName === 'Elite Semi-Annual Pass' || 
                    planName === 'Ultimate Annual Pass';

  // Update the user record
  const { error: userUpdateError } = await supabase
    .from('users')
    .update({
      is_subscription_active: true,
      vip: isVipPlan,
      subscription_id: subscriptionData.id // Set the subscription ID reference
    })
    .eq('id', userData.id);

  if (userUpdateError) {
    console.error('Error updating user:', userUpdateError);
    throw userUpdateError;
  }
}

// Handle one-time payments
export async function handleOneTimePayment(
  session: Stripe.Checkout.Session,
  customerId: string
): Promise<void> {
  const supabase = createClient();
  const { stripe } = await import('@/utils/stripe/config');
  
  // Get customer details
  const customer = await stripe.customers.retrieve(customerId) as Stripe.Customer;
  const userEmail = customer.email || customer.metadata?.userEmail || '';
  
  if (!userEmail) {
    console.error('No user email found in customer data');
    throw new Error('Unable to find user email in customer data');
  }
  
  // Get user ID from database
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('email', userEmail)
    .single();
    
  if (userError || !userData) {
    console.error('Error finding user by email:', userError);
    throw new Error('Unable to find user by email');
  }
  
  // Get product details if available
  let productName = session.metadata?.plan || 'One-time Payment';
  let productId = session.metadata?.productId || 'one_time';
  
  // Create subscription data (even though it's a one-time payment)
  const subscriptionData = {
    id: uuidv4(), // Generate a unique ID
    cancelable_date: null,
    end_date: new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000).toISOString(), // Far future
    owner_email: userEmail,
    owner_id: userData.id, // Set the actual user ID
    plan: productName,
    start_date: new Date().toISOString(),
    status: 'active' as const,
    subscription_id: session.id, // Using session ID instead of subscription ID
    creation_date: new Date().toISOString(),
    modified_date: new Date().toISOString(),
    slug: productId,
    creator: userEmail,
    bubble_id: session.id
  };
  
  // Upsert subscription data
  const { error: subscriptionError } = await supabase
    .from('subscriptions')
    .upsert([subscriptionData]);
    
  if (subscriptionError) {
    console.error('Error upserting one-time payment:', subscriptionError);
    throw subscriptionError;
  }

  // Update user table to set is_subscription_active to true, but NOT vip
  const { error: userUpdateError } = await supabase
    .from('users')
    .update({
      is_subscription_active: true,
      vip: false, // One-time payments don't get VIP status
      subscription_id: subscriptionData.id // Set the subscription ID reference
    })
    .eq('id', userData.id);

  if (userUpdateError) {
    console.error('Error updating user:', userUpdateError);
    throw userUpdateError;
  }
}
