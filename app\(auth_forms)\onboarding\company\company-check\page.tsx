'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/hooks/useUser';
import Image from 'next/image';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { updateCompanyAccount } from '@/actions/admin/account';
import { getCompanyByName } from '@/actions/admin/startup/companies';

import { BuildingIcon } from 'lucide-react';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle
} from '@/components/ui/dialog';
import { Loader2 } from 'lucide-react';

// Define the form schema
const formSchema = z.object({
  company_name: z.string().min(1, { message: 'Company name is required' })
});

type FormValues = z.infer<typeof formSchema>;

export default function CompanyCheckPage() {
  const router = useRouter();
  const { user } = useUser();
  const [isSearching, setIsSearching] = useState(false);
  const [foundCompany, setFoundCompany] = useState<any>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [isCheckingCompany, setIsCheckingCompany] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      company_name: ''
    }
  });

  // Function to check if company exists in the database
  const checkCompanyExists = async (companyName: string) => {
    try {
      setIsCheckingCompany(true);
      // Call the getCompanyByName API function to check if company exists in database
      const company = await getCompanyByName(companyName);

      if (company) {
        // Company found, store it and show dialog
        setFoundCompany({
          id: company.id || '',
          name: company.name || '',
          industry: company.industry || '',
          location: company.location || '',
          description: company.description || ''
        });
        // Show the dialog automatically
        setShowDialog(true);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking company:', error);
      toast.error('Failed to check if company exists. Please try again.');
      return false;
    } finally {
      setIsCheckingCompany(false);
    }
  };

  // Function to update user's company association
  const updateUserCompany = async (companyName: string) => {
    if (!user?.id) return;

    try {
      await updateCompanyAccount(user.id, {
        startup_company: companyName
      });
      toast.success('Your company association has been updated');
    } catch (error) {
      console.error('Error updating user company:', error);
      toast.error('Failed to update your company association');
    }
  };

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    // Step 1: Check if company exists
    const companyExists = await checkCompanyExists(values.company_name);

    if (!companyExists) {
      // Company doesn't exist, store company name for later and proceed to onboarding
      await updateUserCompany(values.company_name);
      toast.success('Proceeding to company information form');
      router.push('/onboarding/company/company-info');
    }
    // If company exists, the dialog is shown by checkCompanyExists function
  };

  // Handle "Yes, update" button in dialog
  const handleUpdateCompany = async () => {
    if (foundCompany && user?.id) {
      await updateUserCompany(foundCompany.name);
      toast.success('Proceeding to company information form');
      router.push('/onboarding/company/company-info');
    }
    setShowDialog(false);
  };

  // Handle "No, different company" button in dialog
  const handleDifferentCompany = () => {
    setShowDialog(false);
    form.reset();
    toast.info('Please enter a different company name');
  };

  // Handle "Skip to home" button in dialog
  const handleSkipToHomepage = async () => {
    if (foundCompany && user?.id) {
      await updateUserCompany(foundCompany.name);
      toast.success('Company association updated successfully');
      router.push('/');
    }
    setShowDialog(false);
  };

  return (
    <div className="container max-w-md mx-auto py-16">
      <div className="flex justify-center mb-8">
        <div className="rounded-full bg-[#ebfaf5] p-4">
          <Image
            src="/favicon_new.png"
            alt="InternUp Logo"
            width={90}
            height={90}
            className="mx-auto"
          />
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-center bg-[#36BA98] bg-clip-text text-transparent">
            Find Your Company
          </CardTitle>
          <CardDescription className="text-center">
            Enter your company name to check if it already exists in our
            database.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="company_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter your company name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full bg-[#36BA98] hover:bg-[#2da888]"
                disabled={isCheckingCompany}
              >
                {isCheckingCompany ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Searching...
                  </>
                ) : (
                  'Continue'
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-2">
          <p className="text-sm text-gray-500 text-center">
            If your company is found, you'll be asked if you want to update its
            information. Otherwise, you'll proceed to set up a new company
            profile.
          </p>
        </CardFooter>
      </Card>

      {/* Dialog for existing company */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[550px] p-0 overflow-hidden">
          <DialogHeader className="p-6 pb-4 border-b">
            <div className="flex items-center gap-2">
              <BuildingIcon className="h-6 w-6 text-[#36BA98]" />
              <DialogTitle className="text-xl text-[#36BA98]">
                Company Found
              </DialogTitle>
            </div>
            <DialogDescription className="text-gray-600 mt-2">
              We found a company named "
              <span className="font-medium">{foundCompany?.name}</span>" in our
              database. What would you like to do?
            </DialogDescription>
          </DialogHeader>

          <div className="p-6">
            <div className="bg-gray-50 p-5 rounded-lg border border-gray-100">
              <h3 className="font-medium text-gray-800 mb-3">
                Company Details
              </h3>
              {foundCompany && (
                <div className="space-y-2 text-sm">
                  <div className="flex">
                    <span className="font-medium w-32 text-gray-700">
                      Name:
                    </span>
                    <span className="text-gray-900">{foundCompany.name}</span>
                  </div>

                  <div className="flex">
                    <span className="font-medium w-32 text-gray-700">
                      Industry:
                    </span>
                    <span className="text-gray-900">
                      {foundCompany.industry || 'Not specified'}
                    </span>
                  </div>

                  <div className="flex">
                    <span className="font-medium w-32 text-gray-700">
                      Location:
                    </span>
                    <span className="text-gray-900">
                      {foundCompany.location || 'Not specified'}
                    </span>
                  </div>

                  {foundCompany.description && (
                    <div className="mt-2">
                      <span className="font-medium text-gray-700">
                        Description:
                      </span>
                      <p className="text-gray-900 mt-1">
                        {foundCompany.description.length > 120
                          ? foundCompany.description.substring(0, 120) + '...'
                          : foundCompany.description}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row justify-end p-6 border-t bg-gray-50">
            <div className="grid grid-cols-3 w-full gap-2">
              <Button
                variant="outline"
                onClick={handleDifferentCompany}
                className="border-gray-300"
              >
                Different company
              </Button>
              <Button
                variant="outline"
                onClick={handleSkipToHomepage}
                className="border-gray-300"
              >
                Skip to home
              </Button>
              <Button
                onClick={handleUpdateCompany}
                className="bg-[#36BA98] hover:bg-[#2da888] text-white"
              >
                Continue
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
