import { createClient } from '@/utils/supabase/client';
import { deleteImage } from '@/utils/supabase/storage/client';
import type { CompanyUpdateData, User } from '@/types/user';

export type AdminUpdateData = {
  first_name?: string | null;
  last_name?: string | null;
  bio?: string | null;
  location?: string | null;
  avatar_url?: string | null;
};

export async function updateAdminProfile(
  id: string,
  profileData: AdminUpdateData
): Promise<User> {
  const supabase = createClient();

  try {
    // First, verify the user exists and is an admin
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .eq('user_type', 'Admin')
      .single();

    if (fetchError || !existingUser) {
      console.error('Error fetching existing user:', fetchError);
      throw new Error('User not found or not an admin');
    }

    // Clean up the update data by removing undefined values
    const cleanedData = Object.entries(profileData).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, any>
    );

    // Perform the update
    const { data, error } = await supabase
      .from('users')
      .update({
        ...cleanedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating admin profile:', error);
      throw new Error(`Failed to update admin profile: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data returned after update');
    }

    return data as User;
  } catch (error) {
    console.error('Error in updateAdminProfile:', error);
    throw error instanceof Error
      ? error
      : new Error('Failed to update admin profile');
  }
}

export async function updateCompanyAccount(
  id: string,
  profileData: CompanyUpdateData
): Promise<User> {
  const supabase = createClient();

  try {
    // First, verify the user exists and is a company
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .eq('user_type', 'Company')
      .single();

    if (fetchError || !existingUser) {
      console.error('Error fetching existing user:', fetchError);
      throw new Error('User not found or not a company');
    }

    // Clean up the update data by removing undefined values
    const cleanedData = Object.entries(profileData).reduce(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      },
      {} as Record<string, any>
    );

    // Perform the update
    const { data, error } = await supabase
      .from('users')
      .update({
        ...cleanedData,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating company profile:', error);
      throw new Error(`Failed to update company profile: ${error.message}`);
    }

    if (!data) {
      throw new Error('No data returned after update');
    }

    return data as User;
  } catch (error) {
    console.error('Error in updateCompanyProfile:', error);
    throw error instanceof Error
      ? error
      : new Error('Failed to update company profile');
  }
}
