// src/components/Chatbot.tsx
"use client";

import Script from "next/script";

const Chatbot = () => {
  return (
    <>
      {/* Chatbase script */}
      <Script
        strategy="afterInteractive"
        src="https://www.chatbase.co/embed.min.js"
        // domain="www.chatbase.co"
        id="C_HHVaxWM0rL4D6mlGpti"
      />
      
      {/* Container for chatbot - adjust ID according to Chatbase requirements */}
      <div id="chatbase-container" className="fixed bottom-1 border-2 right-4 z-50" />
    </>
  );
};

export default Chatbot;