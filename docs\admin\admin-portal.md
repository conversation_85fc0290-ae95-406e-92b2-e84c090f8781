# Admin Portal Documentation

> **Note:** This documentation has been reorganized into multiple files for better maintainability. Please refer to the [Overview](./overview.md) for the main documentation entry point.

## Table of Contents
1. [Overview](./overview.md)
2. [Dashboard](./dashboard.md)
3. [Users Management](./users-management.md)
4. [Startup Management](./startup/overview.md)
   - [Companies](./startup/companies.md)
   - [Jobs](./startup/jobs.md)
   - [Applications](./startup/applications.md)
5. [Public Firms Management](./public-firms/overview.md)
   - [Companies](./public-firms/companies.md)
   - [Jobs](./public-firms/jobs.md)
   - [Referrals](./public-firms/referrals.md)
6. [Insider Management](./insiders.md)
7. [Capstone Projects](./capstone/overview.md)
   - [Projects](./capstone/projects.md)
   - [Users](./capstone/users.md)
8. [Blog Management](./blogs.md)
9. [Web Crawler](./web-crawler.md)
10. [Account Settings](./account.md)

## Legacy Documentation

> **Note:** The content below is preserved for reference but has been reorganized into separate files. Please refer to the links in the Table of Contents above for the most up-to-date documentation.

## Pages and Routes

### Main Routes

1. **Dashboard** (`/admin`)
   - Overview of platform statistics
   - Quick access to key metrics
   - Summary of recent activities

2. **Users Management** (`/admin/users`)
   - Comprehensive user management interface
   - User search, filtering, and pagination
   - User status and subscription management

3. **Startup Management**
   - **Companies** (`/admin/startup/companies`)
     - Manage startup company profiles
     - Approve or reject company registrations
     - Edit company information
   - **Jobs** (`/admin/startup/jobs`)
     - Review and moderate job postings from startups
     - Approve, reject, or edit job listings
     - Manage job visibility and status
   - **Applications** (`/admin/startup/applications`)
     - Monitor applications to startup positions
     - Track application status and metrics

4. **Public Firms Management**
   - **Companies** (`/admin/publicFirms/companies`)
     - Manage public firm company profiles
     - Add or edit established company information
   - **Jobs** (`/admin/publicFirms/jobs`)
     - Manage job listings from public firms
     - Create and edit job postings
   - **Referrals** (`/admin/publicFirms/referrals`)
     - Track referral requests and status
     - Manage referral assignments to insiders

5. **Insider Management**
   - **Insiders** (`/admin/insiders`)
     - Manage insider profiles and status
     - Track insider performance metrics
   - **Payouts** (`/admin/insiders/payouts`)
     - Review and process insider payouts
     - Manage payout schedules and thresholds

6. **Capstone Projects**
   - **Projects** (`/admin/capstone/projects`)
     - Create and manage capstone projects
     - Track project status and participation
   - **Users** (`/admin/capstone/users`)
     - Manage user participation in capstone projects
     - Track user progress and contributions

7. **Content Management**
   - **Blogs** (`/admin/blogs`)
     - Create, edit, and publish blog content
     - Manage blog categories and tags

8. **Web Crawler** (`/admin/web-crawler`)
   - Manage daily job listings from external sources
   - Configure and monitor web crawling operations
   - Edit and publish crawled job listings

9. **Account Settings** (`/admin/account`)
   - Admin account management
   - Security settings and preferences

### Layout

All admin pages share a common layout defined in `app/(user)/admin/layout.tsx`, which includes:
- Dynamic sidebar with admin-specific navigation
- User navigation bar with profile information
- Access control to ensure only users with the "Admin" role can access the portal

## Components

### Core Components

1. **EditSubscriptionDialog** (`components/admin/edit-subscription-dialog.tsx`)
   - Modal dialog for managing user subscriptions
   - Allows setting subscription type and expiry date
   - Updates both user and subscription records

2. **ScheduledPayoutsTable** (`components/admin/payouts/scheduled-payouts-table.tsx`)
   - Displays scheduled insider payouts
   - Provides actions for processing or canceling payouts
   - Shows payout status and history

3. **CapstoneProjectsList** (`app/(user)/admin/capstone/projects/capstone-projects-list.tsx`)
   - Lists all capstone projects with filtering and sorting
   - Provides actions for editing and managing projects
   - Shows project status and participation metrics

4. **CreateProjectDialog** (`app/(user)/admin/capstone/projects/create-project-dialog.tsx`)
   - Form for creating new capstone projects
   - Validates project details before submission
   - Sets initial project parameters

5. **CompanyForm** (`app/(user)/admin/startup/companies/company-form.tsx`)
   - Form for creating and editing startup company profiles
   - Handles validation and submission
   - Manages company details and status

6. **StartupJobForm** (`app/(user)/admin/startup/jobs/startup-job-form.tsx`)
   - Form for creating and editing startup job listings
   - Validates job details before submission
   - Manages job status and visibility

7. **DailyJobsList** (`app/(user)/admin/web-crawler/daily-jobs-list.tsx`)
   - Displays jobs collected by the web crawler
   - Provides actions for editing and publishing jobs
   - Shows job status and source information

## Server Actions

The Admin Portal functionality is powered by several server actions organized by feature area:

1. **User Management** (`actions/admin/users.ts`)
   - `getUsers`: Fetches users with filtering and pagination
   - `updateUserStatus`: Updates user verification, VIP status, etc.
   - `updateUserSubscription`: Manages user subscription details

2. **Startup Management**
   - **Companies** (`actions/admin/startup/companies.ts`)
     - `getStartupCompanies`: Fetches startup companies with filtering
     - `createStartupCompany`: Creates new startup company profiles
     - `updateStartupCompany`: Updates existing company information
   - **Jobs** (`actions/admin/startup/jobs.ts`)
     - `getStartupJobs`: Retrieves startup job listings
     - `createStartupJob`: Creates new job postings
     - `updateStartupJob`: Updates job details and status
   - **Applications** (`actions/admin/startup/applications.ts`)
     - `getStartupApplications`: Fetches applications with filtering
     - `updateApplicationStatus`: Updates application status

3. **Public Firms Management**
   - **Companies** (`actions/admin/public-firms/companies.ts`)
     - `getPublicFirmCompanies`: Fetches public firm companies
     - `createPublicFirmCompany`: Creates new public firm profiles
     - `updatePublicFirmCompany`: Updates company information
   - **Jobs** (`actions/admin/public-firms/jobs.ts`)
     - `getPublicFirmJobs`: Retrieves public firm job listings
     - `createPublicFirmJob`: Creates new job postings
     - `updatePublicFirmJob`: Updates job details and status
   - **Applications** (`actions/admin/public-firms/applications.ts`)
     - `getPublicFirmApplications`: Fetches applications
     - `updatePublicFirmApplicationStatus`: Updates application status

4. **Insider Management** (`actions/admin/insider.ts`)
   - `getInsiders`: Fetches insider profiles
   - `updateInsiderStatus`: Updates insider verification and status
   - `getInsiderPayouts`: Retrieves payout information
   - `processInsiderPayout`: Processes pending payouts

5. **Capstone Management** (`actions/admin/capstone.ts`)
   - `getCapstoneProjects`: Fetches capstone projects
   - `createCapstoneProject`: Creates new projects
   - `updateCapstoneProject`: Updates project details
   - `getCapstoneUsers`: Retrieves users participating in projects
   - `updateCapstoneUserStatus`: Updates user participation status

6. **Blog Management** (`actions/admin/blog.ts`)
   - `getBlogs`: Fetches blog posts
   - `createBlog`: Creates new blog posts
   - `updateBlog`: Updates existing blog content
   - `deleteBlog`: Removes blog posts

7. **Web Crawler Management** (`actions/admin/web-crawler.ts`)
   - `getDailyJobs`: Fetches jobs collected by the crawler
   - `createDailyJob`: Creates new daily job listings
   - `updateDailyJob`: Updates job details
   - `publishDailyJob`: Publishes jobs to the job market

## Data Models

The Admin Portal uses several data models defined in TypeScript:

1. **User**
   ```typescript
   interface User {
     id: string;
     email: string;
     verified: boolean;
     user_type: string;
     created_at: string;
     vip: boolean;
     first_name: string | null;
     last_name: string | null;
     avatar_url: string | null;
     has_suspended_capstone: boolean;
     subscription_type: string | null;
     subscription_expiry_date: string | null;
   }
   ```

2. **UserFilters**
   ```typescript
   interface UserFilters {
     userType?: string;
     verified?: boolean;
     vip?: boolean;
     searchQuery?: string;
     expiredVipSubscriptions?: boolean;
   }
   ```

3. **StartupCompany**
   ```typescript
   interface StartupCompany {
     id: string;
     name: string;
     description: string | null;
     logo_url: string | null;
     website: string | null;
     location: string | null;
     industry: string | null;
     funding_stage: string | null;
     employee_count: number | null;
     founded_year: number | null;
     verified: boolean;
     created_at: string;
     updated_at: string;
   }
   ```

4. **CapstoneProject**
   ```typescript
   interface CapstoneProject {
     id: string;
     title: string;
     description: string;
     industry: string;
     skills_required: string[];
     duration: string;
     start_date: string;
     max_participants: number;
     current_participants: number;
     status: 'open' | 'in_progress' | 'completed';
     created_at: string;
   }
   ```

5. **InsiderPayout**
   ```typescript
   interface InsiderPayout {
     id: string;
     insider_id: string;
     amount: number;
     status: 'pending' | 'processing' | 'completed' | 'failed';
     payout_method: string;
     stripe_payout_id: string | null;
     description: string;
     metadata: any;
     created_at: string;
     updated_at: string;
   }
   ```

## User Flows

### User Management Flow
1. Admin navigates to the Users page
2. Admin can search for users by name or email
3. Admin can filter users by type, verification status, or VIP status
4. For each user, admin can:
   - Change user type (Admin, Insider, Candidate, Company)
   - Toggle verification status
   - Toggle VIP status
   - Manage subscription details
5. Admin can view and filter expired VIP subscriptions
6. Changes are immediately applied to the user's account

### Job Approval Flow
1. Admin navigates to the relevant jobs page (Startup Jobs or Public Firm Jobs)
2. Admin reviews pending job postings
3. For each job, admin can:
   - Review job details for compliance and quality
   - Approve the job, making it visible in the job market
   - Reject the job with feedback
   - Edit job details before approval
4. Approved jobs appear in the public job market
5. Job creators are notified of the approval or rejection

### Insider Payout Flow
1. Admin navigates to the Insider Payouts page
2. Admin reviews pending payout requests
3. Admin can verify payout details and amounts
4. Admin can approve payouts, triggering the payment process
5. Admin can track payout status and history
6. Insiders are notified of payout status changes

### Capstone Project Management Flow
1. Admin navigates to the Capstone Projects page
2. Admin can create new projects with detailed information
3. Admin can edit existing projects to update details
4. Admin can manage user participation in projects
5. Admin can track project progress and status
6. Users are notified of project updates and status changes

## Features

### Core Features
1. **User Management**
   - Comprehensive user administration
   - Role assignment and permissions
   - Verification and status management
   - Subscription and VIP status control

2. **Content Moderation**
   - Job posting review and approval
   - Company profile verification
   - Blog content management
   - Quality control across the platform

3. **Analytics Dashboard**
   - Platform usage statistics
   - User growth metrics
   - Job market activity tracking
   - Application and conversion rates

4. **Insider Program Management**
   - Insider verification and onboarding
   - Payout processing and tracking
   - Performance monitoring
   - Referral assignment and management

### Advanced Features
1. **Subscription Management**
   - VIP subscription control
   - Expiration date management
   - Subscription type assignment
   - Expired subscription tracking

2. **Capstone Project Administration**
   - Project creation and management
   - Participant assignment and tracking
   - Project status monitoring
   - Resource allocation

3. **Web Crawler Integration**
   - External job source monitoring
   - Automated job collection
   - Job listing curation and editing
   - Publication to the job market

## Integration Points

The Admin Portal integrates with several other parts of the InternUp platform:

1. **User System**
   - Manages user accounts across all roles
   - Controls access permissions and verification
   - Handles subscription status and VIP features

2. **Job Market**
   - Approves and publishes job listings
   - Monitors application activity
   - Controls job visibility and status

3. **Insider Network**
   - Manages insider verification and status
   - Processes insider payouts
   - Assigns referral requests to appropriate insiders

4. **Content Management**
   - Creates and publishes blog content
   - Manages platform documentation
   - Controls marketing materials

5. **Capstone Program**
   - Administers educational projects
   - Manages student participation
   - Tracks project outcomes and success

## Best Practices

When working with the Admin Portal, follow these best practices:

1. **Access Control**
   - Strictly limit admin access to authorized personnel
   - Implement proper role-based permissions
   - Regularly audit admin actions for security

2. **Data Validation**
   - Validate all form inputs before submission
   - Implement server-side validation for critical operations
   - Provide clear error messages for validation failures

3. **User Communication**
   - Notify users of status changes affecting their accounts
   - Provide clear feedback for approval/rejection decisions
   - Maintain professional communication standards

4. **Performance Optimization**
   - Implement pagination for large data sets
   - Use server components where possible
   - Optimize database queries for admin operations

5. **Audit Logging**
   - Log all significant admin actions
   - Maintain detailed records of changes
   - Implement traceability for compliance

## Implementation Details

### User Management Implementation

The user management system is implemented with several key components:

1. **User Table**
   - Displays users with filtering and pagination
   - Uses the Table component from the UI library
   - Implements real-time status updates

2. **User Status Controls**
   - Toggle switches for verification and VIP status
   - Select dropdown for user type changes
   - Immediate application of changes

3. **Subscription Management**
   - Modal dialog for subscription editing
   - Date picker for expiration management
   - Synchronization with subscription records

### Job Approval System

The job approval system includes:

1. **Review Interface**
   - Detailed job information display
   - Status indicators for pending, approved, and rejected jobs
   - Action buttons for approval decisions

2. **Editing Capabilities**
   - Forms for modifying job details
   - Validation of required fields
   - Preview of changes before submission

3. **Notification System**
   - Automated notifications to job creators
   - Status update tracking
   - Feedback mechanism for rejections

### Insider Payout Processing

The insider payout system provides:

1. **Payout Review**
   - Detailed payout request information
   - Verification of amounts and eligibility
   - Approval workflow with checks and balances

2. **Payment Integration**
   - Stripe Connect integration for secure transfers
   - Transaction record maintenance
   - Error handling and reconciliation

3. **Reporting**
   - Payout history and status tracking
   - Financial summaries and reports
   - Compliance documentation

### Capstone Project Management

The capstone project management system includes:

1. **Project Creation**
   - Detailed project specification forms
   - Resource allocation planning
   - Timeline and milestone setting

2. **Participant Management**
   - User assignment to projects
   - Progress tracking and evaluation
   - Communication with participants

3. **Status Monitoring**
   - Project phase tracking
   - Completion metrics
   - Outcome evaluation and reporting
