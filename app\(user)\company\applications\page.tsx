'use client';

import type React from 'react';

import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@/hooks/useUser';
import { useToast } from '@/components/ui/use-toast';
import {
  getCompanyApplications,
  updateApplicationStatus,
  deleteApplication
} from '@/actions/company/applications';
import { ApplicationsTable } from '@/components/company/applications-table';
import { CandidateDetailsDialog } from '@/components/company/candidate-details-dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import type { StartupApplication, PaginatedResponse } from '@/types/startups';
import type { User } from '@/types/user';

export default function CompanyApplicationsPage() {
  const { user } = useUser();
  const { toast } = useToast();
  const [applications, setApplications] = useState<StartupApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string | undefined>(
    undefined
  );
  const [totalApplications, setTotalApplications] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedApplication, setSelectedApplication] =
    useState<StartupApplication | null>(null);
  const [selectedCandidate, setSelectedCandidate] = useState<User | null>(null);
  const [showCandidateDialog, setShowCandidateDialog] = useState(false);

  const fetchApplications = useCallback(
    async (
      page = 1,
      search = searchTerm,
      role = roleFilter,
      status = statusFilter
    ) => {
      if (!user?.id) return;

      setLoading(true);
      try {
        const params: {
          page: number;
          searchTerm: string;
          companyId?: string;
          role?: string;
          status?: string;
        } = {
          page,
          searchTerm: search,
          companyId: user.startup_company || undefined
        };

        if (role) params.role = role;

        if (status && status !== 'all') {
          params.status = status.charAt(0).toUpperCase() + status.slice(1);
        }

        const response = await getCompanyApplications(params);

        setApplications(response.data);
        setTotalApplications(response.count || 0);
        setCurrentPage(response.currentPage);
      } catch (error) {
        console.error('Error fetching applications:', error);
        toast({
          title: 'Error',
          description: 'Failed to load applications',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    },
    [user, statusFilter, roleFilter, searchTerm, toast]
  );

  const handleStatusFilterChange = useCallback(
    (value: string) => {
      const newStatus = value === 'all' ? undefined : value;

      setStatusFilter(newStatus);

      fetchApplications(1, searchTerm, roleFilter, newStatus);
    },
    [fetchApplications, searchTerm, roleFilter]
  );

  useEffect(() => {
    if (user?.id) {
      fetchApplications();
    }
  }, [user, fetchApplications]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchApplications(1, searchTerm, roleFilter, statusFilter);
  };

  const handleRoleFilterChange = (value: string) => {
    setRoleFilter(value === 'all' ? undefined : value);
    fetchApplications(
      1,
      searchTerm,
      value === 'all' ? undefined : value,
      statusFilter
    );
  };

  const handleStatusChange = async (id: string, status: string) => {
    try {
      await updateApplicationStatus(id, status);
      toast({
        title: 'Success',
        description: `Application ${status === 'Accepted' ? 'Accepted' : 'Rejected'} successfully`
      });
      fetchApplications(currentPage, searchTerm, roleFilter, statusFilter);
    } catch (error) {
      console.error('Error updating application status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update application status',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteApplication = async (id: string) => {
    try {
      await deleteApplication(id);
      toast({
        title: 'Success',
        description: 'Application deleted successfully'
      });
      fetchApplications(currentPage, searchTerm, roleFilter, statusFilter);
    } catch (error) {
      console.error('Error deleting application:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete application',
        variant: 'destructive'
      });
    }
  };

  const handleViewCandidate = (
    application: StartupApplication,
    candidate: User
  ) => {
    setSelectedApplication(application);
    setSelectedCandidate(candidate);
    setShowCandidateDialog(true);
  };

  return (
    <div className="container mx-auto">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Applications</h1>
        <p className="text-muted-foreground">
          Manage all your startup applications
        </p>
      </div>

      <form onSubmit={handleSearch} className="mb-6 flex gap-2">
        <Input
          placeholder="Search for a job position's candidates"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-md flex-1"
        />
        <Button type="submit">Search</Button>
      </form>

      <div className="mb-4 flex items-center justify-between gap-2">
        <p className="text-sm">{totalApplications} results</p>
        <div className="flex gap-2">
          <Select
            value={roleFilter || 'all'}
            onValueChange={handleRoleFilterChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="Full Time">Full Time</SelectItem>
              <SelectItem value="Part Time">Part Time</SelectItem>
              <SelectItem value="Contract">Contract</SelectItem>
              <SelectItem value="Internship">Internship</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={statusFilter || 'all'}
            onValueChange={handleStatusFilterChange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="accepted">Accepted</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <ApplicationsTable
        applications={applications}
        loading={loading}
        onStatusChange={handleStatusChange}
        onDelete={handleDeleteApplication}
        onViewCandidate={handleViewCandidate}
        totalApplications={totalApplications}
        currentPage={currentPage}
        onPageChange={(page) =>
          fetchApplications(page, searchTerm, roleFilter, statusFilter)
        }
      />

      {selectedApplication && selectedCandidate && (
        <CandidateDetailsDialog
          open={showCandidateDialog}
          onOpenChange={setShowCandidateDialog}
          application={selectedApplication}
          candidate={selectedCandidate}
        />
      )}
    </div>
  );
}
