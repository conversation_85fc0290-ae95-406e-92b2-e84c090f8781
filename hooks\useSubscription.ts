'use client';

import { useState, useEffect } from 'react';
import { Subscription } from '@/types/subscription';
import {
  getUserSubscriptions
} from '@/actions/subscriptions';

export function useSubscription(userId?: string) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [activeSubscription, setActiveSubscription] =
    useState<Subscription | null>(null);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);

  useEffect(() => {
    // Reset loading state when userId changes
    setLoading(true);
    
    if (!userId) {
      // Clear data and stop loading if userId is undefined
      setSubscriptions([]);
      setActiveSubscription(null);
      setError(null);
      setLoading(false);
      return;
    }

    const fetchSubscriptions = async () => {
      try {
        console.log('Fetching subscriptions for user:', userId);
        setError(null);

        // Fetch all user subscriptions
        const allSubs = await getUserSubscriptions(userId);
        console.log('Subscriptions fetched:', allSubs);
        setSubscriptions(allSubs);
        
        // Find the active subscription from the fetched subscriptions
        const active = allSubs.find(sub => sub.status === 'active');
        setActiveSubscription(active || null);
      } catch (err) {
        console.error('Error fetching subscriptions:', err);
        setError(
          err instanceof Error
            ? err
            : new Error('Failed to fetch subscriptions')
        );
        // Clear any previous data on error
        setSubscriptions([]);
        setActiveSubscription(null);
      } finally {
        setLoading(false);
      }
    };

    // Only fetch if we have a valid userId
    if (userId) {
      fetchSubscriptions();
    }
  }, [userId]);

  return {
    loading,
    error,
    activeSubscription,
    subscriptions,
    hasActiveSubscription: !!activeSubscription
  };
}
