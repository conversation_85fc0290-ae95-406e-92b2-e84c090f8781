"use client"

import type React from "react"
import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import type { DailyJob, DailyJobFormData } from "@/types/crawler"
import { updateDailyJob } from "@/actions/admin/web-crawler"
import { X, FileSpreadsheet, Loader2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { uploadFile } from "@/utils/supabase/storage/client"

interface EditDailyJobDialogProps {
  isOpen: boolean
  onClose: () => void
  onJobUpdated: (job: DailyJob) => void
  job: DailyJob
}

export function EditDailyJobDialog({ isOpen, onClose, onJob<PERSON>pdated, job }: EditDailyJobDialogProps) {
  const [formData, setFormData] = useState<DailyJobFormData>({
    posting_date: job.posting_date
      ? new Date(job.posting_date).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0],
    file_type: job.file_type || "",
    notice: job.notice || "",
    crawler_file: job.crawler_file || "",
  })
  const [isUploading, setIsUploading] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [fileUploadError, setFileUploadError] = useState("")

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    const fileExtension = file.name.split(".").pop()?.toLowerCase()
    if (fileExtension !== "xlsx" && fileExtension !== "xls" && fileExtension !== "csv") {
      setFileUploadError("Only Excel (.xlsx, .xls) or CSV files are allowed")
      return
    }

    setIsUploading(true)
    setFileUploadError("")

    try {
      const { fileUrl, error } = await uploadFile({
        file,
        bucket: "crawler-files",
        folder: `daily-jobs/${new Date().toISOString().split("T")[0]}`,
      })

      if (error) {
        setFileUploadError(error)
      } else {
        setFormData((prev) => ({ ...prev, crawler_file: fileUrl }))
      }
    } catch (error) {
      console.error("File upload error:", error)
      setFileUploadError("Failed to upload file. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const result = await updateDailyJob(job.id, formData)

      if (result.success) {
        // Create an updated job object with the new values
        const updatedJob = {
          ...job,
          posting_date: formData.posting_date ? new Date(formData.posting_date) : null,
          file_type: formData.file_type,
          notice: formData.notice,
          crawler_file: formData.crawler_file,
          modified_date: new Date(),
        }

        onJobUpdated(updatedJob)
      } else {
        console.error("Error updating job:", result.error)
      }
    } catch (error) {
      console.error("Error submitting form:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex justify-between items-center">
            <span>Edit Post</span>
            <button onClick={onClose} className="text-gray-500">
              <X size={18} />
            </button>
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="posting_date">Post Date</Label>
            <Input
              id="posting_date"
              type="date"
              name="posting_date"
              value={formData.posting_date}
              onChange={handleChange}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="file_type">File Type</Label>
            <Select value={formData.file_type} onValueChange={(value) => handleSelectChange("file_type", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select file type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Daily Jobs">Daily Jobs</SelectItem>
                <SelectItem value="Weekly Intern">Weekly Intern</SelectItem>
                <SelectItem value="Smart Search">Smart Search</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notice">Notice</Label>
            <Textarea
              id="notice"
              name="notice"
              value={formData.notice}
              onChange={handleChange}
              placeholder="Type here..."
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="crawler_file">Excel/CSV File</Label>
            {formData.crawler_file ? (
              <div className="flex items-center justify-between p-2 border rounded">
                <a
                  href={formData.crawler_file}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline text-sm truncate max-w-[200px]"
                >
                  {formData.crawler_file.split("/").pop()}
                </a>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setFormData((prev) => ({ ...prev, crawler_file: "" }))}
                >
                  <X size={16} />
                </Button>
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                <label
                  htmlFor="file-upload-edit"
                  className="flex items-center justify-center w-full p-2 border border-dashed rounded cursor-pointer hover:bg-gray-50"
                >
                  {isUploading ? (
                    <div className="flex items-center">
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      <span>Uploading...</span>
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <FileSpreadsheet className="w-4 h-4 mr-2" />
                      <span>Upload Excel/CSV file</span>
                    </div>
                  )}
                </label>
                <input
                  id="file-upload-edit"
                  type="file"
                  className="hidden"
                  onChange={handleFileUpload}
                  accept=".xlsx,.xls,.csv"
                  disabled={isUploading}
                />
                {fileUploadError && <p className="text-red-500 text-xs">{fileUploadError}</p>}
              </div>
            )}
          </div>

          <Button
            type="submit"
            className="w-full bg-gray-800 text-white hover:bg-gray-700"
            disabled={isSubmitting || isUploading}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                <span>Saving...</span>
              </div>
            ) : (
              "Update"
            )}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  )
}

