'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useUser';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Loader2,
  Search,
  DollarSign,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  CreditCard,
  Filter,
  Download,
  Users,
  Wallet,
  ArrowUpDown,
  ChevronDown,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/ui/use-toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import {
  getInsidersWithPayoutInfo,
  createInsiderPayout,
  processAutomaticPayouts,
  processBulkPayouts,
  addToInsiderBalance
} from '@/actions/insider-payouts';
import { InsiderWithPayoutInfo, InsiderBalanceAdjustmentFormData } from '@/types/insider-payouts';
import { formatCurrency } from '@/utils/helpers';

// Form schema for creating a payout
const payoutFormSchema = z.object({
  insider_id: z.string().min(1, 'Insider is required'),
  amount: z.coerce.number().min(1, 'Amount must be greater than 0'),
  payout_method: z.string().min(1, 'Payment method is required'),
  description: z.string().optional()
});

// Form schema for bulk payout
const bulkPayoutFormSchema = z.object({
  payout_method: z.string().min(1, 'Payment method is required'),
  description: z.string().optional()
});

// Form schema for adding to balance
const balanceAdjustmentFormSchema = z.object({
  insider_id: z.string().min(1, 'Insider is required'),
  amount: z.coerce.number().min(1, 'Amount must be greater than 0'),
  description: z.string().optional()
});

export default function AdminInsiderPayoutsPage() {
  const { user, loading: userLoading } = useUser();
  const [isLoading, setIsLoading] = useState(true);
  const [insiders, setInsiders] = useState<InsiderWithPayoutInfo[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isPayoutDialogOpen, setIsPayoutDialogOpen] = useState(false);
  const [isBulkPayoutDialogOpen, setIsBulkPayoutDialogOpen] = useState(false);
  const [selectedInsider, setSelectedInsider] = useState<InsiderWithPayoutInfo | null>(null);
  const [selectedInsiders, setSelectedInsiders] = useState<string[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isProcessingAutomatic, setIsProcessingAutomatic] = useState(false);
  const [isProcessingBulk, setIsProcessingBulk] = useState(false);
  const [isBalanceDialogOpen, setIsBalanceDialogOpen] = useState(false);
  const [isProcessingBalance, setIsProcessingBalance] = useState(false);
  const [sortField, setSortField] = useState('earnings_available');
  const [sortDirection, setSortDirection] = useState('desc');
  const { toast } = useToast();
  const itemsPerPage = 10;

  // Calculate total available earnings
  const totalAvailableEarnings = insiders.reduce((sum, insider) => sum + insider.earnings_available, 0);

  // Calculate total selected earnings
  const selectedInsidersData = insiders.filter(insider => selectedInsiders.includes(insider.id));
  const totalSelectedEarnings = selectedInsidersData.reduce((sum, insider) => sum + insider.earnings_available, 0);

  // Initialize individual payout form
  const payoutForm = useForm<z.infer<typeof payoutFormSchema>>({
    resolver: zodResolver(payoutFormSchema),
    defaultValues: {
      insider_id: '',
      amount: 0,
      payout_method: 'stripe',
      description: 'Referral earnings payout'
    }
  });

  // Initialize bulk payout form
  const bulkPayoutForm = useForm<z.infer<typeof bulkPayoutFormSchema>>({
    resolver: zodResolver(bulkPayoutFormSchema),
    defaultValues: {
      payout_method: 'stripe',
      description: 'Bulk referral earnings payout'
    }
  });

  // Initialize balance adjustment form
  const balanceForm = useForm<z.infer<typeof balanceAdjustmentFormSchema>>({
    resolver: zodResolver(balanceAdjustmentFormSchema),
    defaultValues: {
      insider_id: '',
      amount: 0,
      description: 'Manual balance adjustment'
    }
  });

  // Load insiders with payout info
  useEffect(() => {
    async function loadInsiders() {
      if (userLoading) return;

      try {
        setIsLoading(true);
        const { data, count } = await getInsidersWithPayoutInfo(
          currentPage,
          itemsPerPage,
          searchTerm
        );
        setInsiders(data);
        setTotalCount(count || 0);
      } catch (error) {
        console.error('Error loading insiders:', error);
        toast({
          title: 'Error',
          description: 'Failed to load insiders. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    }

    loadInsiders();
  }, [currentPage, searchTerm, userLoading, toast]);

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1);
    // The effect will reload the data
  };

  // Handle payout dialog open
  const handlePayoutClick = (insider: InsiderWithPayoutInfo) => {
    setSelectedInsider(insider);
    payoutForm.setValue('insider_id', insider.id);
    payoutForm.setValue('amount', insider.earnings_available);
    payoutForm.setValue('payout_method', insider.payment_method || 'stripe');
    setIsPayoutDialogOpen(true);
  };

  // Handle payout form submission
  const onSubmitPayout = async (data: z.infer<typeof payoutFormSchema>) => {
    try {
      setIsProcessing(true);

      // Create payout
      await createInsiderPayout({
        insider_id: data.insider_id,
        amount: data.amount,
        payout_method: data.payout_method,
        description: data.description
      });

      toast({
        title: 'Success',
        description: 'Payout created successfully.',
        variant: 'default'
      });

      // Close dialog and refresh data
      setIsPayoutDialogOpen(false);

      // Reload insiders
      const { data: refreshedData } = await getInsidersWithPayoutInfo(
        currentPage,
        itemsPerPage,
        searchTerm
      );
      setInsiders(refreshedData);
    } catch (error) {
      console.error('Error creating payout:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create payout. Please try again.';

      // Check if this is a payment account setup error
      const isPaymentAccountError = errorMessage.includes('payment account');

      toast({
        title: isPaymentAccountError ? 'Payment Account Required' : 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle bulk payout form submission
  const onSubmitBulkPayout = async (data: z.infer<typeof bulkPayoutFormSchema>) => {
    try {
      setIsProcessingBulk(true);

      // Process bulk payouts using the server action
      const result = await processBulkPayouts(selectedInsiders, {
        payout_method: data.payout_method,
        description: data.description
      });

      // Show success message
      toast({
        title: 'Bulk Payout Processed',
        description: `Successfully processed ${result.success} payouts. ${result.failed > 0 ? `Failed: ${result.failed}` : ''}`,
        variant: result.success > 0 ? 'default' : 'destructive'
      });

      // Close dialog and refresh data
      setIsBulkPayoutDialogOpen(false);
      setSelectedInsiders([]);

      // Reload insiders
      const { data: refreshedData } = await getInsidersWithPayoutInfo(
        currentPage,
        itemsPerPage,
        searchTerm
      );
      setInsiders(refreshedData);
    } catch (error) {
      console.error('Error processing bulk payouts:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to process bulk payouts. Please try again.';

      // Check if this is a payment account setup error
      const isPaymentAccountError = errorMessage.includes('payment account');

      toast({
        title: isPaymentAccountError ? 'Payment Account Required' : 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsProcessingBulk(false);
    }
  };

  // Handle select all insiders
  const handleSelectAll = () => {
    // Get eligible insiders (with earnings, active status, and payment account)
    const eligibleInsiders = insiders.filter(i =>
      i.earnings_available > 0 &&
      i.account_status === 'active' &&
      i.stripe_account_id
    );

    if (selectedInsiders.length === eligibleInsiders.length) {
      // If all eligible insiders are selected, deselect all
      setSelectedInsiders([]);
    } else {
      // Otherwise, select all eligible insiders
      setSelectedInsiders(eligibleInsiders.map(i => i.id));
    }
  };

  // Handle select insider
  const handleSelectInsider = (insiderId: string, checked: boolean) => {
    if (checked) {
      setSelectedInsiders([...selectedInsiders, insiderId]);
    } else {
      setSelectedInsiders(selectedInsiders.filter(id => id !== insiderId));
    }
  };

  // Handle balance adjustment dialog open
  const handleBalanceClick = (insider: InsiderWithPayoutInfo) => {
    setSelectedInsider(insider);
    balanceForm.setValue('insider_id', insider.id);
    balanceForm.setValue('amount', 0);
    setIsBalanceDialogOpen(true);
  };

  // Handle balance adjustment form submission
  const onSubmitBalanceAdjustment = async (data: z.infer<typeof balanceAdjustmentFormSchema>) => {
    try {
      setIsProcessingBalance(true);

      // Add to insider's balance
      const result = await addToInsiderBalance(
        data.insider_id,
        data.amount,
        data.description
      );

      toast({
        title: 'Success',
        description: `Balance adjusted successfully. New balance: ${formatCurrency(result.newBalance / 100)}`,
        variant: 'default'
      });

      // Close dialog and refresh data
      setIsBalanceDialogOpen(false);

      // Reload insiders
      const { data: refreshedData } = await getInsidersWithPayoutInfo(
        currentPage,
        itemsPerPage,
        searchTerm
      );
      setInsiders(refreshedData);
    } catch (error) {
      console.error('Error adjusting balance:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to adjust balance. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessingBalance(false);
    }
  };

  // Handle automatic payouts
  const handleProcessAutomaticPayouts = async () => {
    try {
      setIsProcessingAutomatic(true);

      // Process automatic payouts
      const processedCount = await processAutomaticPayouts();

      toast({
        title: 'Success',
        description: `Processed ${processedCount} automatic payouts.`,
        variant: 'default'
      });

      // Reload insiders
      const { data: refreshedData } = await getInsidersWithPayoutInfo(
        currentPage,
        itemsPerPage,
        searchTerm
      );
      setInsiders(refreshedData);
    } catch (error) {
      console.error('Error processing automatic payouts:', error);
      toast({
        title: 'Error',
        description: 'Failed to process automatic payouts. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessingAutomatic(false);
    }
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Pagination controls
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  if (isLoading || userLoading) {
    return (
      <div className="container mx-auto py-12 flex justify-center items-center">
        <Loader2 className="h-8 w-8 animate-spin text-[#118073]" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Insider Payouts</h1>
        <p className="text-muted-foreground">
          Manage and process payouts for insiders
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-[#118073]/10 to-[#118073]/5 border-[#118073]/20">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 mb-3">
              <Wallet className="h-5 w-5 text-[#118073]" />
              <h3 className="font-semibold text-lg">Total Available Earnings</h3>
            </div>
            <div className="text-3xl font-bold text-[#118073] mb-2">
              {formatCurrency(totalAvailableEarnings / 100)}
            </div>
            <p className="text-sm text-muted-foreground">
              Across {insiders.filter(i => i.earnings_available > 0).length} insiders with available earnings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 mb-3">
              <Users className="h-5 w-5 text-[#118073]" />
              <h3 className="font-semibold text-lg">Active Insiders</h3>
            </div>
            <div className="text-3xl font-bold mb-2">
              {insiders.filter(i => i.account_status === 'active').length}
            </div>
            <p className="text-sm text-muted-foreground">
              Out of {insiders.length} total insiders
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 mb-3">
              <CreditCard className="h-5 w-5 text-[#118073]" />
              <h3 className="font-semibold text-lg">Selected for Payout</h3>
            </div>
            {selectedInsiders.length > 0 ? (
              <>
                <div className="text-3xl font-bold text-[#118073] mb-2">
                  {formatCurrency(totalSelectedEarnings / 100)}
                </div>
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    {selectedInsiders.length} insiders selected
                  </p>
                  <Button
                    size="sm"
                    className="bg-[#118073] hover:bg-[#118073]/90"
                    onClick={() => setIsBulkPayoutDialogOpen(true)}
                    disabled={selectedInsiders.length === 0}
                  >
                    <DollarSign className="h-3 w-3 mr-1" />
                    Pay Selected
                  </Button>
                </div>
              </>
            ) : (
              <>
                <div className="text-xl font-medium mb-2 text-gray-500">
                  No insiders selected
                </div>
                <p className="text-sm text-muted-foreground">
                  Select insiders to process bulk payouts
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex flex-col md:flex-row gap-2 w-full md:w-auto">
          <div className="relative w-full md:w-[300px]">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search by name or email"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9"
            />
          </div>
          <div className="flex gap-2">
            <Button onClick={handleSearch} variant="outline" size="icon">
              <Search className="h-4 w-4" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => setStatusFilter('all')}>
                  All Statuses
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('active')}>
                  Active Only
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setStatusFilter('pending')}>
                  Pending Only
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="flex gap-2 w-full md:w-auto">
          {selectedInsiders.length > 0 && (
            <Button
              variant="outline"
              onClick={() => setSelectedInsiders([])}
              className="text-sm"
            >
              Clear Selection
            </Button>
          )}

        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-[#118073]" />
              <CardTitle>Insiders with Available Earnings</CardTitle>
            </div>
            <Badge variant="outline" className="ml-2">
              {statusFilter === 'all' ? 'All Statuses' :
               statusFilter === 'active' ? 'Active Only' :
               statusFilter === 'pending' ? 'Pending Only' : 'Filtered'}
            </Badge>
          </div>
          <CardDescription className="mt-1.5">
            Select insiders to process individual or bulk payouts
          </CardDescription>
          <Separator className="my-2" />
        </CardHeader>
        <CardContent>
          {insiders.length === 0 ? (
            <div className="text-center py-8 space-y-3">
              <Users className="h-10 w-10 text-gray-300 mx-auto" />
              <div>
                <p className="font-medium text-gray-600">No insiders found</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b text-xs text-muted-foreground">
                    <th className="text-left py-2 px-2 w-10">
                      <Checkbox
                        checked={selectedInsiders.length > 0 &&
                          selectedInsiders.length === insiders.filter(i => i.earnings_available > 0 && i.account_status === 'active').length}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all insiders"
                      />
                    </th>
                    <th className="text-left py-2 px-2 font-medium">
                      <div className="flex items-center gap-1 cursor-pointer" onClick={() => {
                        setSortField('first_name');
                        setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                      }}>
                        Name
                        {sortField === 'first_name' && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                    <th className="text-left py-2 px-2 font-medium">Email</th>
                    <th className="text-left py-2 px-2 font-medium">
                      <div className="flex items-center gap-1 cursor-pointer" onClick={() => {
                        setSortField('earnings_available');
                        setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
                      }}>
                        Available
                        {sortField === 'earnings_available' && (
                          <ArrowUpDown className="h-3 w-3" />
                        )}
                      </div>
                    </th>
                    <th className="text-left py-2 px-2 font-medium">Total Paid</th>
                    <th className="text-left py-2 px-2 font-medium">Payment Method</th>
                    <th className="text-left py-2 px-2 font-medium">Status</th>
                    <th className="text-left py-2 px-2 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {insiders
                    .filter(insider => {
                      if (statusFilter === 'all') return true;
                      return insider.account_status === statusFilter;
                    })
                    .sort((a, b) => {
                      if (sortField === 'first_name') {
                        const nameA = `${a.first_name || ''} ${a.last_name || ''}`;
                        const nameB = `${b.first_name || ''} ${b.last_name || ''}`;
                        return sortDirection === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
                      } else if (sortField === 'earnings_available') {
                        return sortDirection === 'asc' ? a.earnings_available - b.earnings_available : b.earnings_available - a.earnings_available;
                      }
                      return 0;
                    })
                    .map((insider) => (
                    <tr key={insider.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-2">
                        <Checkbox
                          checked={selectedInsiders.includes(insider.id)}
                          onCheckedChange={(checked) => handleSelectInsider(insider.id, !!checked)}
                          disabled={insider.earnings_available <= 0 || insider.account_status !== 'active' || !insider.stripe_account_id}
                          aria-label={`Select ${insider.first_name} ${insider.last_name}`}
                        />
                      </td>
                      <td className="py-3 px-2 font-medium">
                        {insider.first_name} {insider.last_name}
                      </td>
                      <td className="py-3 px-2 text-sm">
                        {insider.user_email}
                      </td>
                      <td className="py-3 px-2 font-medium text-[#118073]">
                        {formatCurrency(insider.earnings_available / 100)}
                      </td>
                      <td className="py-3 px-2 text-sm">
                        {formatCurrency(insider.earnings_paid / 100)}
                      </td>
                      <td className="py-3 px-2 text-sm">
                        {insider.payment_method === 'stripe'
                          ? 'Stripe'
                          : insider.payment_method === 'bank_transfer'
                          ? 'Bank Transfer'
                          : 'Not Set'}
                      </td>
                      <td className="py-3 px-2">
                        <Badge
                          variant={
                            insider.account_status === 'active'
                              ? 'default'
                              : insider.account_status === 'pending'
                              ? 'outline'
                              : 'secondary'
                          }
                          className={
                            insider.account_status === 'active'
                              ? 'bg-green-100 text-green-800 hover:bg-green-100'
                              : insider.account_status === 'pending'
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-100'
                          }
                        >
                          {insider.account_status === 'active'
                            ? 'Active'
                            : insider.account_status === 'pending'
                            ? 'Pending'
                            : insider.account_status === 'restricted'
                            ? 'Restricted'
                            : 'Not Set Up'}
                        </Badge>
                      </td>
                      <td className="py-3 px-2">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handlePayoutClick(insider)}
                              disabled={insider.earnings_available <= 0 || insider.account_status !== 'active' || !insider.stripe_account_id}
                            >
                              <DollarSign className="h-4 w-4 mr-2" />
                              Process Payout
                              {!insider.stripe_account_id && (
                                <span className="ml-2 text-xs text-red-500">(No payment account)</span>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleBalanceClick(insider)}
                            >
                              <Wallet className="h-4 w-4 mr-2" />
                              Add to Balance
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleSelectInsider(insider.id, !selectedInsiders.includes(insider.id))}
                              disabled={insider.earnings_available <= 0 || insider.account_status !== 'active' || !insider.stripe_account_id}
                            >
                              <Checkbox className="h-4 w-4 mr-2" checked={selectedInsiders.includes(insider.id)} />
                              {selectedInsiders.includes(insider.id) ? 'Deselect' : 'Select'}
                              {!insider.stripe_account_id && (
                                <span className="ml-2 text-xs text-red-500">(No payment account)</span>
                              )}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between border-t pt-4">
          <div className="text-sm text-muted-foreground">
            Showing {insiders.filter(insider => {
              if (statusFilter === 'all') return true;
              return insider.account_status === statusFilter;
            }).length} of {totalCount} insiders
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={currentPage >= totalPages}
            >
              Next
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Payout Dialog */}
      <Dialog open={isPayoutDialogOpen} onOpenChange={setIsPayoutDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create Payout</DialogTitle>
            <DialogDescription>
              Create a payout for {selectedInsider?.first_name} {selectedInsider?.last_name}
            </DialogDescription>
          </DialogHeader>
          <Form {...payoutForm}>
            <form
              onSubmit={payoutForm.handleSubmit(onSubmitPayout)}
              className="space-y-6"
            >
              <FormField
                control={payoutForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount ($)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        max={selectedInsider?.earnings_available ? selectedInsider.earnings_available / 100 : 0}
                        step="0.01"
                        onChange={(e) => field.onChange(parseFloat(e.target.value) * 100)}
                        value={field.value / 100}
                      />
                    </FormControl>
                    <FormDescription>
                      Available: {selectedInsider ? formatCurrency(selectedInsider.earnings_available / 100) : '$0.00'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={payoutForm.control}
                name="payout_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="stripe">Stripe</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={payoutForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsPayoutDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isProcessing}
                >
                  {isProcessing && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Create Payout
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Bulk Payout Dialog */}
      <Dialog open={isBulkPayoutDialogOpen} onOpenChange={setIsBulkPayoutDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Process Bulk Payout</DialogTitle>
            <DialogDescription>
              Process payouts for {selectedInsiders.length} selected insiders with a total of {formatCurrency(totalSelectedEarnings / 100)}
            </DialogDescription>
          </DialogHeader>

          <Form {...bulkPayoutForm}>
            <form onSubmit={bulkPayoutForm.handleSubmit(onSubmitBulkPayout)} className="space-y-6">
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-md">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Selected Insiders:</span>
                    <span className="font-medium">{selectedInsiders.length}</span>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium">Total Amount:</span>
                    <span className="font-bold text-[#118073]">{formatCurrency(totalSelectedEarnings / 100)}</span>
                  </div>
                </div>

                <FormField
                  control={bulkPayoutForm.control}
                  name="payout_method"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Payment Method</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="stripe">Stripe</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Method used to process all selected payouts
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={bulkPayoutForm.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Bulk payout for referral earnings"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        This description will be applied to all payouts
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsBulkPayoutDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isProcessingBulk || selectedInsiders.length === 0}
                  className="bg-[#118073] hover:bg-[#118073]/90"
                >
                  {isProcessingBulk && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Process {selectedInsiders.length} Payouts
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Balance Adjustment Dialog */}
      <Dialog open={isBalanceDialogOpen} onOpenChange={setIsBalanceDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add to Balance</DialogTitle>
            <DialogDescription>
              Add to {selectedInsider?.first_name} {selectedInsider?.last_name}'s available earnings
            </DialogDescription>
          </DialogHeader>
          <Form {...balanceForm}>
            <form
              onSubmit={balanceForm.handleSubmit(onSubmitBalanceAdjustment)}
              className="space-y-6"
            >
              <FormField
                control={balanceForm.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount ($)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="1"
                        step="0.01"
                        onChange={(e) => field.onChange(parseFloat(e.target.value) * 100)}
                        value={field.value / 100}
                      />
                    </FormControl>
                    <FormDescription>
                      Current balance: {selectedInsider ? formatCurrency(selectedInsider.earnings_available / 100) : '$0.00'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={balanceForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsBalanceDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isProcessingBalance}
                >
                  {isProcessingBalance && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Add to Balance
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
