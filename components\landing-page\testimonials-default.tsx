import { Card } from '@/components/ui/card';
import Marquee from '../magicui/marquee';

const testimonials = [
  {
    quote:
      'The internal referrals really set Internup apart. It felt like they were genuinely invested in my success, and that made all the difference in securing a job offer.',
    author: '<PERSON><PERSON>',
    role: 'Data Scientist'
  },
  {
    quote:
      "Internup's focus on startups and public companies with a strong commitment to hiring international talent is a real game-changer. I found my job through their referral program and couldn't be happier.",
    author: '<PERSON>',
    role: 'Marketing'
  },
  {
    quote:
      "I found connections to companies that I wouldn't have discovered on my own. Internup made the process smooth and stress-free.",
    author: 'Tongxuan',
    role: 'Software Development Engineer'
  },
  {
    quote:
      'Internup connected me with startups that were open to hiring international talent. The referral opportunities really boosted my chances!',
    author: '<PERSON>',
    role: 'Software Development Engineer'
  }
];

export default function Testimonials() {
  return (
    <section className="py-12 md:py-20 px-4 bg-background">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8 md:mb-12">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-2">
            Hear From Those We've Helped
          </h2>
          <p className="text-sm md:text-base text-muted-foreground px-2">
            Success stories from global talent who landed their dream
            opportunities with Internup
          </p>
        </div>
        <div>
          <Marquee pauseOnHover className="[--duration:20s] w-full">
            <div className="flex space-x-4 md:space-x-8">
              {testimonials.map((testimonial, index) => (
                <Card
                  key={index}
                  className="w-[280px] md:w-[350px] lg:w-[400px] p-4 md:p-6 bg-white shadow-lg"
                >
                  <div className="mb-3 md:mb-4">
                    <svg
                      className="text-primary h-6 w-6 md:h-8 md:w-8"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                    >
                      <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
                    </svg>
                  </div>
                  <p className="text-sm md:text-base lg:text-lg mb-4 md:mb-6">
                    {testimonial.quote}
                  </p>
                  <div className="flex items-center gap-2 md:gap-4">
                    <div className="flex flex-col">
                      <span className="font-bold text-sm md:text-base">
                        {testimonial.author}
                      </span>
                      <span className="text-xs md:text-sm text-muted-foreground">
                        {testimonial.role}
                      </span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </Marquee>
        </div>
      </div>
    </section>
  );
}
