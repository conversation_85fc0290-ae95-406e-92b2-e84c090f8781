'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { createClient } from '@/utils/supabase/client';
import { Loader2, AlertCircle } from 'lucide-react';

type MigrationStatus =
  | 'not_migrated'
  | 'migration_invited'
  | 'verification_pending'
  | 'migrated'
  | 'failed';

export default function VerifyMigrationPage() {
  const supabase = createClient();
  const { toast } = useToast();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function completeMigration() {
      try {
        // Get current user
        const {
          data: { user },
          error: userError
        } = await supabase.auth.getUser();

        if (userError || !user) {
          setError('You must be logged in to complete migration.');
          toast({
            title: 'Not Authenticated',
            description: 'You must be logged in to complete migration.',
            variant: 'destructive'
          });
          router.push('/signin');
          return;
        }

        // Verify this is a migration flow
        const isMigration = user?.user_metadata?.is_migration;
        if (!isMigration) {
          setError('Invalid migration request.');
          toast({
            title: 'Invalid Request',
            description: 'This action is not part of the migration process.',
            variant: 'destructive'
          });
          router.push('/candidate');
          return;
        }

        // Check current migration status
        const { data: userData, error: fetchError } = await supabase
          .from('users')
          .select('id, migration_status')
          .eq('id', user.id)
          .single();

        if (fetchError || !userData) {
          setError('Could not verify migration status.');
          toast({
            title: 'Error Fetching Status',
            description: 'Could not verify migration status.',
            variant: 'destructive'
          });
          router.push('/signin');
          return;
        }

        // Validate correct status
        if (userData.migration_status !== 'verification_pending') {
          // If already migrated, just redirect
          if (userData.migration_status === 'migrated') {
            router.push('/candidate');
            return;
          }

          setError('Invalid migration status.');
          toast({
            title: 'Invalid Status',
            description: 'Your account is not pending verification.',
            variant: 'destructive'
          });
          return;
        }

        // Update migration status to migrated
        const { error: updateError } = await supabase
          .from('users')
          .update({
            migration_status: 'migrated' as MigrationStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        if (updateError) {
          setError('Failed to complete migration.');
          toast({
            title: 'Migration Failed',
            description: 'Please contact support or try again later.',
            variant: 'destructive'
          });
          return;
        }

        // Success! Show toast and redirect
        toast({
          title: 'Migration Complete',
          description: 'Your account has been successfully migrated.',
          variant: 'default'
        });

        // Short delay before redirect for better UX
        setTimeout(() => {
          router.push('/candidate');
        }, 1500);
      } catch (error) {
        console.error('Migration error:', error);
        setError('An unexpected error occurred.');
        toast({
          title: 'Error',
          description: 'An unexpected error occurred during migration.',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    }

    completeMigration();
  }, [router, toast, supabase]);

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-center mb-6">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Migration Error
            </h2>
            <p className="text-sm text-gray-600">{error}</p>
          </div>
          <button
            onClick={() => router.push('/signin')}
            className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors"
          >
            Return to Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        {loading ? (
          <>
            <Loader2 className="animate-spin w-6 h-6 mx-auto mb-4 text-gray-600" />
            <p className="text-sm text-gray-700">Completing migration...</p>
          </>
        ) : (
          <p className="text-sm text-gray-700">Redirecting to dashboard...</p>
        )}
      </div>
    </div>
  );
}
