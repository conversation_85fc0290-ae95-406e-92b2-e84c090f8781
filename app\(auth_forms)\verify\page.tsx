'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { verifyEmail, resendVerificationCode } from '@/utils/bubble/auth';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Meteors } from '@/components/magicui/meteors';
import Particles from '@/components/magicui/particles';
import { useUser } from '@/hooks/useUser';

export default function VerifyPage() {
  const router = useRouter();
  const { toast } = useToast();
  const { user, loading } = useUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [otp, setOtp] = useState('');
  const [userType, setUserType] = useState('');

  // Get user type from URL params if not available in user object
  useEffect(() => {
    if (typeof window !== 'undefined' && !loading) {
      const params = new URLSearchParams(window.location.search);
      const userTypeParam = params.get('userType');

      if (userTypeParam) {
        setUserType(userTypeParam);
      } else if (user?.user_type) {
        setUserType(user.user_type);
      }
    }
  }, [user, loading]);

  const handleVerify = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!user?.email) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          'Unable to verify your account. Please try signing in again.',
        duration: 5000
      });
      setIsSubmitting(false);
      return;
    }

    try {
      const result = await verifyEmail(user.email, otp);

      if (result.type === 'success') {
        toast({
          title: result.title,
          description: result.description,
          duration: 5000
        });

        // Redirect based on user type
        if (userType?.toLowerCase() === 'candidate') {
          router.push('/candidate');
        } else if (userType?.toLowerCase() === 'company') {
          router.push('/onboarding/company');
        } else {
          router.push('/');
        }
      } else {
        toast({
          variant: 'destructive',
          title: result.title,
          description: result.description,
          duration: 5000
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to verify OTP. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOTP = async () => {
    if (!user?.email || !userType) {
      toast({
        variant: 'destructive',
        title: 'Missing Information',
        description:
          'Unable to resend verification code. Please try signing in again.',
        duration: 5000
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await resendVerificationCode(user.email, userType);

      if (result.type === 'success') {
        toast({
          title: 'OTP Resent',
          description: 'A new verification code has been sent to your email.',
          duration: 5000
        });
      } else {
        toast({
          variant: 'destructive',
          title: result.title || 'Failed to Resend',
          description:
            result.description ||
            'Could not send new verification code. Please try again.',
          duration: 5000
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to resend OTP. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!user) {
    router.push('/signin');
    return null;
  }

  return (
    <div className="flex min-h-[100dvh] bg-background">
      {/* Left Section with Illustration */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Verify Your Account</h1>
            <p className="text-xl">
              You're almost there! Please verify your email to access all
              features of InternUp.
            </p>
          </div>
        </div>
        <Meteors number={20} />
      </div>

      {/* Right Section with Form */}
      <div className="flex flex-col flex-1 p-8 sm:p-12 lg:p-16 justify-center relative overflow-hidden">
        <Particles className="absolute inset-0 opacity-20" quantity={100} />
        <div className="w-full max-w-md mx-auto space-y-8 relative z-10">
          <div className="space-y-2 text-center">
            <Link
              href="/"
              className="inline-flex items-center text-sm text-muted-foreground hover:text-primary mb-4"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to home
            </Link>
            <h1 className="text-3xl font-bold">Verify Your Email</h1>
            <p className="text-muted-foreground">
              Please enter the verification code sent to{' '}
              <span className="font-medium">{user?.email}</span>
            </p>
          </div>

          <form className="space-y-6" onSubmit={handleVerify}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="otp">Verification Code</Label>
                <Input
                  id="otp"
                  placeholder="Enter 6-digit code"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  required
                  className="text-center text-lg tracking-widest"
                  maxLength={6}
                />
              </div>
            </div>

            <Button
              type="submit"
              className="w-full bg-[#118073] hover:bg-[#36BA98]"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Verifying...' : 'Verify Account'}
            </Button>
          </form>

          <div className="text-center">
            <Button
              variant="link"
              onClick={handleResendOTP}
              disabled={isSubmitting}
              className="text-sm text-muted-foreground hover:text-primary"
            >
              Didn't receive a code? Resend
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
