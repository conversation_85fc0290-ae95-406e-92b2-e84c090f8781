'use client';

import type React from 'react';

import { useEffect, useState } from 'react';
import { Upload, X, Loader2 } from 'lucide-react';
import { uploadImage, deleteImage } from '@/utils/supabase/storage/client';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';

interface ImageUploadProps {
  value: string;
  onChange: (url: string) => void;
  className?: string;
  bucket?: string;
  folder?: string;
  disabled?: boolean;
}

export function ImageUpload({
  value,
  onChange,
  className,
  bucket = 'startups',
  folder = 'company-logos',
  disabled = false
}: ImageUploadProps) {
  const [isMounted, setIsMounted] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const file = e.target.files?.[0];
    if (!file) {
      console.log('No file selected');
      return;
    }

    console.log(
      'File selected:',
      file.name,
      'Size:',
      file.size,
      'Type:',
      file.type
    );

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      console.log('File too large:', file.size);
      toast({
        title: 'Error',
        description: 'File size must be less than 5MB',
        variant: 'destructive'
      });
      return;
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      console.log('Invalid file type:', file.type);
      toast({
        title: 'Error',
        description: 'Please upload a valid image file (JPEG, PNG, or WebP)',
        variant: 'destructive'
      });
      return;
    }

    setIsUploading(true);

    try {
      console.log(
        'Starting upload process for:',
        file.name,
        'to bucket:',
        bucket,
        'folder:',
        folder
      );
      const { imageUrl, error } = await uploadImage({
        file,
        bucket,
        folder
      });

      if (error) {
        console.error('Error returned from uploadImage:', error);
        throw new Error(error);
      }

      onChange(imageUrl);
      toast({
        title: 'Success',
        description: 'Image uploaded successfully'
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      toast({
        title: 'Upload Failed',
        description:
          error instanceof Error ? error.message : 'Failed to upload image',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = async () => {
    if (disabled) return;

    if (!value) return;

    try {
      setIsUploading(true);
      await deleteImage(value);
      onChange('');
      toast({
        title: 'Success',
        description: 'Image removed successfully'
      });
    } catch (error) {
      console.error('Error deleting image:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove image',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
    }
  };

  if (!isMounted) return null;

  return (
    <div className={`relative ${className}`}>
      <div className="flex h-full w-full flex-col items-center justify-center rounded-md border border-dashed">
        {value ? (
          <div className="relative h-full w-full">
            <img
              src={value || '/placeholder.svg'}
              alt="Uploaded"
              className={`h-full w-full rounded-md object-cover ${isUploading ? 'opacity-50' : ''}`}
            />
            {!isUploading && !disabled && (
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute bottom-0 right-0 bg-background/80"
                onClick={handleRemoveImage}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            {isUploading && (
              <div className="absolute inset-0 flex items-center justify-center bg-background/50">
                <Loader2 className="h-4 w-4 animate-spin" />
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center space-y-2 p-4">
            <div className="rounded-full bg-muted p-2">
              {isUploading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Upload className="h-4 w-4" />
              )}
            </div>
            <div className="text-xs text-muted-foreground">
              {isUploading ? 'Uploading...' : 'Upload Image'}
            </div>
            <div className="text-xs text-muted-foreground">
              (Max 5MB, JPEG/PNG/WebP)
            </div>
            <input
              type="file"
              accept="image/jpeg,image/png,image/webp"
              className="absolute inset-0 cursor-pointer opacity-0"
              onChange={handleUpload}
              disabled={isUploading || disabled}
            />
          </div>
        )}
      </div>
    </div>
  );
}
