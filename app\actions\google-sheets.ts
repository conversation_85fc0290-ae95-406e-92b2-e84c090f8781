'use server';

import { google } from 'googleapis';
import type { JobDetailRow } from '@/types/google-sheets';

// Define a type for the function parameters for clarity
type FetchSheetDataParams = {
  spreadsheetId: string;
  range: string; // e.g., 'Sheet1!A:L'
};

/**
 * Extracts the spreadsheet ID and GID from a Google Sheet URL.
 * @param googleSheetUrl The full URL of the Google Sheet.
 * @returns An object with spreadsheetId and gid, or null for each if not found.
 */
const extractSheetDetailsFromUrl = (
  googleSheetUrl: string
): { spreadsheetId: string | null; gid: string | null } => {
  try {
    const url = new URL(googleSheetUrl);
    const pathParts = url.pathname.split('/');
    const spreadsheetId =
      pathParts.find((part, index, arr) => arr[index - 1] === 'd') || null;

    const gidFromQuery = url.searchParams.get('gid');
    const gidFromHash = url.hash.includes('gid=')
      ? url.hash.split('gid=')[1].split('&')[0]
      : null;
    const gid = gidFromQuery || gidFromHash;

    return { spreadsheetId, gid };
  } catch (error) {
    console.error('Invalid Google Sheet URL format:', googleSheetUrl, error);
    return { spreadsheetId: null, gid: null };
  }
};

const getGoogleAuthCredentials = () => {
  const base64EncodedServiceAccount = process.env.GOOGLE_SERVICE_ACCOUNT_BASE64;

  if (!base64EncodedServiceAccount) {
    console.error(
      'GOOGLE_SERVICE_ACCOUNT_BASE64 is not configured in environment variables.'
    );
    throw new Error(
      'Server configuration error: Missing Google API credentials.'
    );
  }

  try {
    const decodedServiceAccount = Buffer.from(
      base64EncodedServiceAccount,
      'base64'
    ).toString('utf-8');
    const credentials = JSON.parse(decodedServiceAccount);
    return credentials;
  } catch (error) {
    console.error(
      'Error decoding or parsing GOOGLE_SERVICE_ACCOUNT_BASE64:',
      error
    );
    throw new Error(
      'Server configuration error: Invalid Google API credentials format.'
    );
  }
};

export const fetchGoogleSheetData = async ({
  spreadsheetId,
  range
}: FetchSheetDataParams): Promise<JobDetailRow[]> => {
  try {
    const credentials = getGoogleAuthCredentials();

    const auth = new google.auth.GoogleAuth({
      credentials,
      scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
    });

    const sheets = google.sheets({ version: 'v4', auth });

    const response = await sheets.spreadsheets.values.get({
      spreadsheetId,
      range
    });

    const rows = response.data.values;

    if (!rows || rows.length < 2) {
      return [];
    }

    const headerRow = rows[0] as string[];
    const dataRows = rows.slice(1);

    const headerToKeyMap: Record<string, keyof JobDetailRow> = {
      keyword: 'Keyword',
      'job title': 'Job_title',
      job_title: 'Job_title',
      company: 'Company',
      location: 'Location',
      source: 'Source',
      'job post time': 'Job_post_time',
      job_post_time: 'Job_post_time',
      employment: 'Employment',
      qualifications: 'Qualifications',
      'apply links': 'Apply_links',
      apply_links: 'Apply_links',
      authorization: 'Authorization',
      'years of exp': 'Years of Exp',
      years_of_exp: 'Years of Exp',
      'work mode': 'Work Mode',
      work_mode: 'Work Mode'
    };

    const jobDetails: JobDetailRow[] = dataRows
      .map((rowArray: any[]) => {
        const rowObject: Partial<JobDetailRow> = {};
        headerRow.forEach((header, index) => {
          if (
            header &&
            typeof header === 'string' &&
            rowArray[index] !== undefined &&
            rowArray[index] !== null
          ) {
            const normalizedHeader = header.trim().toLowerCase();
            const objectKey = headerToKeyMap[normalizedHeader];

            if (objectKey) {
              rowObject[objectKey] = String(rowArray[index]);
            }
          }
        });
        if (Object.keys(rowObject).length > 0 && rowObject.Job_title) {
          return rowObject as JobDetailRow;
        }
        return null;
      })
      .filter(Boolean) as JobDetailRow[];

    return jobDetails;
  } catch (error) {
    console.error('Error fetching data from Google Sheets API:', error);
    if (error instanceof Error) {
      throw new Error(`Google Sheets API error: ${error.message}`);
    }
    throw new Error(
      'An unknown error occurred while fetching Google Sheet data.'
    );
  }
};

/**
 * Fetches sheet name (title) using GID.
 * This is a helper and might require separate permissions or more robust error handling.
 */
async function getSheetNameFromGid(
  spreadsheetId: string,
  gid: string,
  auth: any // google.auth.GoogleAuth client instance
): Promise<string | null> {
  if (!spreadsheetId || !gid) return null;
  try {
    const sheetsService = google.sheets({ version: 'v4', auth });
    const spreadsheetInfo = await sheetsService.spreadsheets.get({
      spreadsheetId,
      fields: 'sheets(properties(sheetId,title))'
    });

    const foundSheet = spreadsheetInfo.data.sheets?.find(
      (s: {
        properties?: { sheetId?: number | null; title?: string | null } | null;
      }) => s.properties?.sheetId === parseInt(gid, 10)
    );
    return foundSheet?.properties?.title || null;
  } catch (error) {
    console.error(`Error fetching sheet name for GID ${gid}:`, error);
    return null; // Fallback to null if GID to name mapping fails
  }
}

// Optional: A more advanced fetcher that tries to get sheet name by GID
export const fetchGoogleSheetDataByUrl = async (
  googleSheetUrl: string,
  defaultRangeColumns: string = 'A:L', // e.g., A:L for 12 columns
  customSheetName?: string // NEW: Optional parameter to override the sheet name
): Promise<JobDetailRow[]> => {
  const { spreadsheetId, gid } = extractSheetDetailsFromUrl(googleSheetUrl);

  if (!spreadsheetId) {
    throw new Error('Could not extract Spreadsheet ID from URL.');
  }

  const credentials = getGoogleAuthCredentials();
  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: ['https://www.googleapis.com/auth/spreadsheets.readonly']
  });

  let sheetName = 'All Jobs'; // Default sheet name

  // If custom sheet name is provided, use it
  if (customSheetName) {
    sheetName = customSheetName;
  }
  // Otherwise try to detect from GID
  else if (gid) {
    const nameFromGid = await getSheetNameFromGid(spreadsheetId, gid, auth);
    if (nameFromGid) {
      sheetName = nameFromGid;
    } else {
      console.warn(
        `Could not determine sheet name from GID ${gid}, defaulting to '${sheetName}'.`
      );
    }
  }

  const range = `'${sheetName}'!${defaultRangeColumns}`;
  return fetchGoogleSheetData({ spreadsheetId, range });
};
