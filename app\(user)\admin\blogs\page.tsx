"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { PencilIcon, XCircleIcon, Upload, ImageIcon } from "lucide-react"
import { getBlogs, createBlog, updateBlog, deleteBlog, getBlog } from "@/actions/admin/blog"
import { uploadImage, deleteImage } from "@/utils/supabase/storage/client"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import type { Blog, BlogFormData } from "@/types/blog"

export default function BlogsPage() {
  const [blogs, setBlogs] = useState<Blog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [formData, setFormData] = useState<BlogFormData>({
    author: "",
    title: "",
    block1: "",
    block2: "",
    block3: "",
    block4: "",
  })
  const [isEditing, setIsEditing] = useState(false)
  const [currentBlogId, setCurrentBlogId] = useState<string | null>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)

  useEffect(() => {
    fetchBlogs()
  }, [currentPage, searchTerm])

  const fetchBlogs = async () => {
    setIsLoading(true)
    try {
      const { data, totalPages: pages } = await getBlogs({
        page: currentPage,
        searchTerm,
      })
      setBlogs(data || [])
      setTotalPages(pages || 1)
    } catch (error) {
      console.error("Error fetching blogs:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleOpenCreateDialog = () => {
    resetForm()
    setIsEditing(false)
    setCurrentBlogId(null)
    setIsDialogOpen(true)
  }

  const handleOpenEditDialog = async (id: string) => {
    setIsLoading(true)
    try {
      const blog = await getBlog(id)
      setFormData({
        author: blog.author || "",
        title: blog.title || "",
        block1: blog.block1 || "",
        block2: blog.block2 || "",
        block3: blog.block3 || "",
        block4: blog.block4 || "",
      })
      
      // Set image preview if block2 contains an image URL
      if (blog.block2) {
        setImagePreview(blog.block2)
      } else {
        setImagePreview(null)
      }
      
      setIsEditing(true)
      setCurrentBlogId(id)
      setIsDialogOpen(true)
    } catch (error) {
      console.error("Error fetching blog for edit:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImageFile(file)
    
    // Create preview
    const reader = new FileReader()
    reader.onloadend = () => {
      setImagePreview(reader.result as string)
    }
    reader.readAsDataURL(file)
  }

  const handleUploadImage = async () => {
    if (!imageFile) return
    
    setIsUploading(true)
    try {
      // If editing and there's an existing image URL, delete it first
      if (isEditing && formData.block2) {
        await deleteImage(formData.block2)
      }
      
      const { imageUrl, error } = await uploadImage({
        file: imageFile,
        bucket: "blog-images", // Specify your Supabase bucket name
        folder: currentBlogId || formData.title || 'new' // Use blog ID as folder or "new" for new blogs
      })
      
      if (error) {
        throw new Error(error)
      }
      
      setFormData(prev => ({ ...prev, block2: imageUrl }))
      setImageFile(null)
    } catch (error) {
      console.error("Error uploading image:", error)
      alert("Failed to upload image. Please try again.")
    } finally {
      setIsUploading(false)
    }
  }

  const handleSubmitBlog = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Upload image if there's a file selected but not yet uploaded
    if (imageFile) {
      await handleUploadImage()
    }
    
    try {
      if (isEditing && currentBlogId) {
        await updateBlog(currentBlogId, formData)
      } else {
        await createBlog(formData)
      }
      setIsDialogOpen(false)
      resetForm()
      fetchBlogs()
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} blog:`, error)
    }
  }

  const handleDeleteBlog = async (id: string) => {
    if (confirm("Are you sure you want to delete this blog?")) {
      try {
        // Get blog data to check if there's an image to delete
        const blog = await getBlog(id)
        if (blog.block2) {
          await deleteImage(blog.block2)
        }
        
        await deleteBlog(id)
        fetchBlogs()
      } catch (error) {
        console.error("Error deleting blog:", error)
      }
    }
  }

  const resetForm = () => {
    setFormData({
      author: "",
      title: "",
      block1: "",
      block2: "",
      block3: "",
      block4: "",
    })
    setImageFile(null)
    setImagePreview(null)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-2">Blogs</h1>
      <p className="text-gray-600 mb-6">Here are all Blogs</p>

      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-gray-500">{blogs.length > 0 ? blogs.length : 0} results</p>

        <div className="flex gap-4">
          <div className="flex gap-2">
            <Input
              placeholder="Search for a blog"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[400px]"
            />
            <Button onClick={() => fetchBlogs()}>Search</Button>
          </div>
          <Button onClick={handleOpenCreateDialog}>Create</Button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 text-left font-medium text-gray-500">Author</th>
              <th className="p-3 text-left font-medium text-gray-500">Blog title</th>
              <th className="p-3 text-left font-medium text-gray-500">Post Date</th>
              <th className="p-3 text-left font-medium text-gray-500">Nums of Saved</th>
              <th className="p-3 text-left font-medium text-gray-500">Nums of Liked</th>
              <th className="p-3 text-left font-medium text-gray-500">Delete</th>
              <th className="p-3 text-left font-medium text-gray-500">Edit</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={7} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : blogs.length === 0 ? (
              <tr>
                <td colSpan={7} className="p-4 text-center">
                  No blogs found
                </td>
              </tr>
            ) : (
              blogs.map((blog) => (
                <tr key={blog.id} className="border-b">
                  <td className="p-3">{blog.author || "N/A"}</td>
                  <td className="p-3">{blog.title || "N/A"}</td>
                  <td className="p-3">
                    {blog.creation_date
                      ? new Date(blog.creation_date).toLocaleDateString("en-US", {
                          month: "short",
                          day: "2-digit",
                          year: "2-digit",
                        })
                      : "N/A"}
                  </td>
                  <td className="p-3">{blog.saved_user?.length || 0}</td>
                  <td className="p-3">{blog.liked_user?.length || 0}</td>
                  <td className="p-3">
                    <button onClick={() => handleDeleteBlog(blog.id)} className="text-red-500 hover:text-red-700">
                      <XCircleIcon className="h-5 w-5" />
                    </button>
                  </td>
                  <td className="p-3">
                    <button onClick={() => handleOpenEditDialog(blog.id)} className="text-green-500 hover:text-green-700">
                      <PencilIcon className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>{isEditing ? 'Edit Blog' : 'Create A Blog'}</DialogTitle>
          </DialogHeader>
          <ScrollArea className="h-[calc(90vh-120px)]">
            <form onSubmit={handleSubmitBlog} className="space-y-4 px-1">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Author</label>
                  <Input
                    value={formData.author}
                    onChange={(e) => setFormData({ ...formData, author: e.target.value })}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Title</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Block 1</label>
                <Textarea
                  value={formData.block1}
                  onChange={(e) => setFormData({ ...formData, block1: e.target.value })}
                  rows={6}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Block 2 (Image)</label>
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center gap-2">
                    <Input
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      className="flex-1"
                    />
                    <Button 
                      type="button" 
                      onClick={handleUploadImage} 
                      disabled={!imageFile || isUploading}
                      className="whitespace-nowrap"
                    >
                      {isUploading ? 'Uploading...' : 'Upload Image'}
                    </Button>
                  </div>
                  
                  {/* Image preview */}
                  {(imagePreview || formData.block2) && (
                    <div className="relative border rounded-md p-2 mt-2">
                      <div className="text-sm text-gray-500 mb-2">
                        {formData.block2 ? 'Current image:' : 'Preview:'}
                      </div>
                      <div className="flex items-center justify-center bg-gray-100 rounded-md p-2">
                        {imagePreview ? (
                          <img 
                            src={imagePreview} 
                            alt="Preview" 
                            className="max-h-48 object-contain" 
                          />
                        ) : formData.block2 ? (
                          <img 
                            src={formData.block2} 
                            alt="Current image" 
                            className="max-h-48 object-contain" 
                          />
                        ) : (
                          <div className="flex flex-col items-center text-gray-400 py-8">
                            <ImageIcon size={40} />
                            <p>No image selected</p>
                          </div>
                        )}
                      </div>
                      <Input
                        type="text"
                        value={formData.block2}
                        onChange={(e) => setFormData({ ...formData, block2: e.target.value })}
                        placeholder="Image URL (automatically filled when uploaded)"
                        className="mt-2"
                        readOnly
                      />
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Block 3</label>
                <Textarea
                  value={formData.block3}
                  onChange={(e) => setFormData({ ...formData, block3: e.target.value })}
                  rows={6}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Block 4</label>
                <Textarea
                  value={formData.block4}
                  onChange={(e) => setFormData({ ...formData, block4: e.target.value })}
                  rows={6}
                />
              </div>

              <div className="flex justify-end gap-2 py-4">
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button type="submit">{isEditing ? 'Update' : 'Save'}</Button>
              </div>
            </form>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  )
}