import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { stripe } from '@/utils/stripe/config';
import { v4 as uuidv4 } from 'uuid';

export async function POST(req: NextRequest) {
  try {
    console.log('Received request to sync Stripe account');
    // Parse request body
    const body = await req.json();
    const { userId, userEmail, stripeAccountId } = body;

    if (!userId || !userEmail || !stripeAccountId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log(`Syncing Stripe account ${stripeAccountId} for user ${userId}`);
    const supabase = createClient();

    // First, check if there's already a payment account for this Stripe account
    const { data: existingAccount, error: existingAccountError } = await supabase
      .from('insider_payment_accounts')
      .select('*')
      .eq('stripe_account_id', stripeAccountId)
      .maybeSingle();

    if (existingAccountError && existingAccountError.code !== 'PGRST116') {
      console.error('Error checking for existing account:', existingAccountError);
    } else if (existingAccount) {
      console.log(`Found existing payment account for Stripe account ${stripeAccountId}:`, existingAccount);
      return NextResponse.json({ success: true, account: existingAccount });
    }

    // Look for insider by email
    const { data: insider, error: insiderError } = await supabase
      .from('insiders')
      .select('*')
      .eq('user_email', userEmail)
      .maybeSingle();

    if (insiderError && insiderError.code !== 'PGRST116') {
      console.error('Error checking for insider:', insiderError);
    }

    // Generate a new account ID - don't rely on the insider ID for the payment account
    const accountId = uuidv4();
    
    // Get user details for creating insider if needed
    let firstName = 'Insider';
    let lastName = 'User';
    try {
      // Try to get user metadata
      const { data: userData } = await supabase.auth.admin.getUserById(userId);
      if (userData?.user) {
        firstName = userData.user.user_metadata?.firstName || firstName;
        lastName = userData.user.user_metadata?.lastName || lastName;
      }
    } catch (error) {
      console.error('Error getting user data (continuing):', error);
    }

    // If insider doesn't exist, create it
    let insiderId = insider?.id;
    if (!insider) {
      console.log(`Creating new insider record for email: ${userEmail}`);
      
      // Generate a unique ID for the insider
      const newInsiderId = uuidv4();
      
      // Insert the new insider
      const { data: newInsider, error: createError } = await supabase
        .from('insiders')
        .insert({
          id: newInsiderId,
          user_email: userEmail,
          first_name: firstName,
          last_name: lastName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
        
      if (createError) {
        console.error('Error creating insider record:', createError);
        insiderId = null; // Continue without insider
      } else {
        console.log('Successfully created insider record:', newInsider);
        insiderId = newInsider.id;
      }
    } else {
      console.log(`Found existing insider with ID: ${insider.id}`);
    }

    // Get Stripe account details
    const account = await stripe.accounts.retrieve(stripeAccountId);
    console.log(`Retrieved Stripe account: ${account.id}, status: ${account.details_submitted ? 'details submitted' : 'pending'}`);
    
    // Determine account status
    let status = 'pending';
    if (account.details_submitted && account.payouts_enabled) {
      status = 'active';
      console.log(`Account ${account.id} is fully active with payouts enabled`);
    } else if (account.details_submitted) {
      status = 'restricted';
      console.log(`Account ${account.id} has details submitted but payouts not enabled`);
    }

    // Create or update payment account record
    // Use a separate ID and add insider_id as a reference
    console.log(`Creating payment account record with ID: ${accountId}`);
    const { data: paymentAccount, error: upsertError } = await supabase
      .from('insider_payment_accounts')
      .insert({
        id: accountId,                  // Use a unique ID for the account
        insider_id: insiderId,          // Reference to insider if available
        email: userEmail,               // Store email directly in account
        user_id: userId,                // Store user ID for reference
        stripe_account_id: stripeAccountId,
        account_status: status,
        payment_method: 'stripe',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();
      
    if (upsertError) {
      console.error('Error creating payment account record:', upsertError);
      
      // Try a more direct approach without RLS limitations
      try {
        // Use a service role client if available
        const adminResult = await fetch('/api/admin/create-payment-account', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            accountId,
            insiderId,
            userEmail,
            userId,
            stripeAccountId,
            status
          })
        });
        
        if (adminResult.ok) {
          const data = await adminResult.json();
          console.log('Successfully created payment account using admin route:', data);
          return NextResponse.json({ success: true, account: data.account });
        } else {
          const errorData = await adminResult.json();
          throw new Error(`Admin route failed: ${errorData.error}`);
        }
      } catch (adminError) {
        console.error('Both approaches failed:', adminError);
        return NextResponse.json({
          error: `Failed to create payment account: ${upsertError.message}. Admin route also failed.`,
          details: upsertError
        }, { status: 500 });
      }
    }
    
    console.log('Successfully created payment account record:', paymentAccount);

    return NextResponse.json({ success: true, account: paymentAccount });

  } catch (error) {
    console.error('Error syncing Stripe account:', error);
    return NextResponse.json(
      { error: `Failed to sync Stripe account: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}
