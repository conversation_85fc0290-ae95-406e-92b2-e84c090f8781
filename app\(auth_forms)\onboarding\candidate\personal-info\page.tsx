'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { ProgressIndicator } from '../components/progress-indicator';
import { useOnboarding } from '../components/onboarding-provider';
import { updateCandidateProfile } from '@/actions/admin/candidate';
import { useUser } from '@/hooks/useUser';
import { countries } from '@/lib/data/countries';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';

export default function PersonalInfoPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data, updatePersonalInfo, markStepAsCompleted } = useOnboarding();
  const { user, loading } = useUser();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCountries, setFilteredCountries] = useState(countries);

  // Define steps for progress indicator
  const steps = [
    '/onboarding/candidate/personal-info',
    '/onboarding/candidate/job-preferences',
    '/onboarding/candidate/about-you',
    '/onboarding/candidate/complete'
  ];

  // Local form state that syncs with the context
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    preferredName: '',
    email: '',
    phoneNumber: '',
    nationality: ''
  });

  // Load data from context and user data on mount
  useEffect(() => {
    if (!loading && user) {
      setFormData({
        firstName: user.first_name || data.personalInfo.firstName || '',
        lastName: user.last_name || data.personalInfo.lastName || '',
        preferredName:
          user.preferred_name || data.personalInfo.preferredName || '',
        email: user.email || data.personalInfo.email || '',
        phoneNumber: user.phone_number || data.personalInfo.phoneNumber || '',
        nationality: user.nationality || data.personalInfo.nationality || ''
      });
    } else if (!loading) {
      setFormData(data.personalInfo);
    }
  }, [data.personalInfo, user, loading]);

  // Filter countries based on search query
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCountries(countries);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredCountries(
        countries.filter((country) => country.toLowerCase().includes(query))
      );
    }
  }, [searchQuery]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (!user?.id) {
        throw new Error('User ID not found. Please sign in again.');
      }

      // Update the context with form data
      updatePersonalInfo(formData);

      // Store in localStorage for persistence
      localStorage.setItem(
        'onboarding_personal_info',
        JSON.stringify(formData)
      );

      // Mark this step as completed
      markStepAsCompleted('/onboarding/candidate/personal-info');

      // Update profile in the backend
      await updateCandidateProfile(user.id, {
        first_name: formData.firstName,
        last_name: formData.lastName,
        preferred_name: formData.preferredName,
        email: formData.email,
        phone_number: formData.phoneNumber,
        nationality: formData.nationality
      });

      // Show success toast
      toast({
        title: 'Profile Updated',
        description: 'Your personal information has been saved successfully.',
        duration: 3000
      });

      // Navigate to the next step
      router.push('/onboarding/candidate/job-preferences');
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Something went wrong. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-[#36BA98]" />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header with progress indicator */}
      <ProgressIndicator steps={steps} />

      <div className="flex flex-1 p-6 md:p-12">
        {/* Left section with image and text */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center pr-12">
          <div className="flex flex-col items-center gap-8">
            <div className="flex items-center space-x-2">
              <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
                <Image
                  src="/favicon_new.png"
                  alt="logo"
                  width={25}
                  height={25}
                />
              </div>
              <span className="text-lg md:text-xl font-extrabold tracking-tightest">
                InternUp
              </span>
            </div>
            <div className="flex flex-col items-center gap-2 text-center">
              <h2 className="text-xl font-semibold">Connecting</h2>
              <h2 className="text-xl font-semibold">
                International <span className="text-[#36BA98]">Talent</span>
              </h2>
              <h2 className="text-xl font-semibold">
                with <span className="text-[#36BA98]">Opportunities</span>
              </h2>
            </div>
          </div>
        </div>

        {/* Right section with form */}
        <div className="w-full lg:w-1/2">
          <div className="max-w-md mx-auto">
            <h1 className="text-3xl font-bold mb-4">Get Started Today</h1>
            <p className="text-gray-700 mb-6">
              Connect with top companies that value international talent.
              Complete your profile to access tailored opportunities. Personal
              info is needed to apply.
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label
                    htmlFor="firstName"
                    className="block text-sm font-medium"
                  >
                    First Name<span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                    className="w-full rounded-xl"
                  />
                </div>

                <div className="space-y-2">
                  <label
                    htmlFor="lastName"
                    className="block text-sm font-medium"
                  >
                    Last Name<span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                    className="w-full rounded-xl"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="preferredName"
                  className="block text-sm font-medium"
                >
                  Preferred Name
                </label>
                <Input
                  id="preferredName"
                  name="preferredName"
                  value={formData.preferredName}
                  onChange={handleChange}
                  className="w-full rounded-xl"
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-medium">
                  Email<span className="text-red-500">*</span>
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full rounded-xl"
                  disabled={!!user?.email}
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="phoneNumber"
                  className="block text-sm font-medium"
                >
                  Phone Number<span className="text-red-500">*</span>
                </label>
                <Input
                  id="phoneNumber"
                  name="phoneNumber"
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={handleChange}
                  required
                  className="w-full rounded-xl"
                />
              </div>

              <div className="space-y-2">
                <label
                  htmlFor="nationality"
                  className="block text-sm font-medium"
                >
                  Nationality
                </label>
                <Select
                  value={formData.nationality}
                  onValueChange={(value) =>
                    handleSelectChange('nationality', value)
                  }
                >
                  <SelectTrigger id="nationality" className="w-full rounded-xl">
                    <SelectValue placeholder="Select your nationality" />
                  </SelectTrigger>
                  <SelectContent>
                    <div className="px-3 pb-2">
                      <Input
                        placeholder="Search countries..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="my-2"
                      />
                    </div>
                    <ScrollArea className="h-72">
                      {filteredCountries.length > 0 ? (
                        filteredCountries.map((country) => (
                          <SelectItem key={country} value={country}>
                            {country}
                          </SelectItem>
                        ))
                      ) : (
                        <div className="py-2 px-3 text-sm text-gray-500">
                          No countries found
                        </div>
                      )}
                    </ScrollArea>
                  </SelectContent>
                </Select>
              </div>

              <div className="pt-4 flex justify-center">
                <Button
                  type="submit"
                  className="bg-[#36BA98] hover:bg-[#2da888] text-white px-12 py-2 rounded-xl transition-colors"
                  disabled={isSubmitting}
                  aria-disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    'Continue'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
