'use server';

import { createClient } from "@/utils/supabase/client"
import type { User } from "@/types/user"
import { v4 as uuidv4 } from 'uuid';

export interface UserFilters {
  userType?: string;
  verified?: boolean;
  vip?: boolean;
  searchQuery?: string;
  expiredVipSubscriptions?: boolean;
}

export async function getUsers(filters?: UserFilters, page: number = 1, limit: number = 20) {
  const supabase = createClient()

  let query = supabase
    .from('users')
    .select(
      `
      id,
      email,
      verified,
      user_type,
      created_at,
      vip,
      first_name,
      last_name,
      avatar_url,
      has_suspended_capstone,
      subscription_type,
      subscription_expiry_date
    `,
      { count: 'exact' }
    ) // Include count for pagination
    .order('created_at', { ascending: false })
    .range((page - 1) * limit, page * limit - 1); // Apply pagination

  // Apply filters
  if (filters) {
    if (filters.userType && filters.userType !== 'All') {
      query = query.eq('user_type', filters.userType);
    }

    if (filters.verified !== undefined) {
      query = query.eq('verified', filters.verified);
    }

    if (filters.vip !== undefined) {
      query = query.eq('vip', filters.vip);
    }

    if (filters.searchQuery) {
      // Fix the OR query format - the previous format with line breaks and commas was causing SQL parsing errors
      query = query.or(
        `email.ilike.%${filters.searchQuery}%,first_name.ilike.%${filters.searchQuery}%,last_name.ilike.%${filters.searchQuery}%`
      )
    }
    
    // Filter for expired VIP subscriptions
    if (filters.expiredVipSubscriptions) {
      const currentDate = new Date().toISOString();
      query = query
        .lt('subscription_expiry_date', currentDate) // Subscription expired (date in the past)
        .eq('vip', true); // VIP is still active
    }
  }

  const { data, error, count } = await query;

  if (error) {
    console.error('Error fetching users:', error);
    throw new Error('Failed to fetch users');
  }

  return { data: data as User[], count };
}

export async function updateUserStatus(
  userId: string,
  updates: {
    verified?: boolean;
    vip?: boolean;
    has_suspended_capstone?: boolean;
  }
) {
  const supabase = createClient();

  const { error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId);

  if (error) {
    console.error('Error updating user:', error);
    throw new Error('Failed to update user');
  }
}

export async function updateUserSubscription(
  userId: string,
  updates: {
    subscription_type: string | null;
    subscription_expiry_date: string | null;
    vip?: boolean;
  }
) {
  const supabase = createClient();

  try {
    // First fetch the user to get the email
    const { data: userData, error: userFetchError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userFetchError) {
      console.error('Error fetching user:', userFetchError);
      throw new Error('Failed to fetch user');
    }

    const userEmail = userData?.email;

    // 1. Update the user table
    const { error: userError } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId);

    if (userError) {
      console.error('Error updating user subscription:', userError);
      throw new Error('Failed to update user subscription');
    }

    // 2. Update the subscriptions table if subscription info is provided
    if (updates.subscription_type || updates.subscription_expiry_date) {
      // First check if a subscription record exists for this user
      const { data: existingSubscription, error: fetchError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('owner_id', userId)
        .maybeSingle();

      if (fetchError) {
        console.error('Error fetching subscription:', fetchError);
        throw new Error('Failed to fetch subscription record');
      }

      const now = new Date().toISOString();

      // Prepare subscription data updates
      const subscriptionUpdates: Record<string, any> = {
        modified_date: now
      };

      if (updates.subscription_type) {
        subscriptionUpdates.plan = updates.subscription_type;
        // If setting a subscription, mark it as active
        if (updates.subscription_type !== null) {
          subscriptionUpdates.status = 'active';
        } else {
          // If removing subscription, mark it as inactive
          subscriptionUpdates.status = 'inactive';
        }
      }

      if (updates.subscription_expiry_date) {
        subscriptionUpdates.end_date = updates.subscription_expiry_date;
      }

      if (existingSubscription) {
        // Update existing subscription
        const subscriptionUpdateData = { ...subscriptionUpdates };

        // Add owner_email to existing record if it's missing
        if (!existingSubscription.owner_email && userEmail) {
          subscriptionUpdateData.owner_email = userEmail;
        }

        const { error: updateError } = await supabase
          .from('subscriptions')
          .update(subscriptionUpdateData)
          .eq('owner_id', userId);

        if (updateError) {
          console.error('Error updating subscription:', updateError);
          throw new Error('Failed to update subscription record');
        }
      } else if (updates.subscription_type) {
        // Create new subscription record only if subscription_type is provided
        const newSubscription = {
          id: uuidv4(),
          owner_id: userId,
          owner_email: userEmail,
          start_date: now,
          creation_date: now,
          modified_date: now,
          status: 'active',
          plan: updates.subscription_type,
          end_date: updates.subscription_expiry_date || null
        };

        const { error: insertError } = await supabase
          .from('subscriptions')
          .insert(newSubscription);

        if (insertError) {
          console.error('Error creating subscription:', insertError);
          throw new Error('Failed to create subscription record');
        }
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updateUserSubscription:', error);
    throw error;
  }
}
