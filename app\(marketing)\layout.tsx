'use client';

import { marketingConfig } from '@/config/marketing';
import FooterPrimary from '@/components/footer-primary';
import CircularNavigation from '@/components/navigation';
import React from 'react';
import { useUser } from '@/hooks/useUser';

interface MarketingLayoutProps {
  children: React.ReactNode;
}

export default function MarketingLayout({ children }: MarketingLayoutProps) {
  const { user, loading } = useUser();

  // Pass the user_type if available, otherwise pass null
  const userType = user?.user_type || null;

  return (
    <div className="flex min-h-screen flex-col items-center w-full">
      <CircularNavigation
        items={marketingConfig.mainNav}
        user={user ? true : false}
        userType={userType}
      />
      <main className="flex-1 w-full box-border">{children}</main>
      <FooterPrimary />
    </div>
  );
}
