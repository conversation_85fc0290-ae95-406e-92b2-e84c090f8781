'use server';

import { createClient as createServerClient } from '@/utils/supabase/server';
import { StartupApplication } from '@/types/startups';

/**
 * Create a new startup application
 * @param applicationData - The application data to create
 * @returns The created application
 */
export async function createStartupApplication(
  applicationData: Partial<StartupApplication>
): Promise<{ applicationId: string }> {
  const supabase = createServerClient();

  try {
    // Add validation to ensure required fields are present
    if (!applicationData.job) {
      throw new Error('Job ID is required');
    }

    if (!applicationData.candidate && !applicationData.creator) {
      throw new Error('Candidate or creator email is required');
    }

    const { data, error } = await supabase
      .from('startup_applications')
      .insert(applicationData)
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating startup application:', error);

      // Detailed error message
      let errorMessage = 'Failed to create startup application';
      if (error.details) {
        errorMessage += `: ${error.details}`;
      }
      if (error.hint) {
        errorMessage += ` (Hint: ${error.hint})`;
      }

      throw new Error(errorMessage);
    }

    return { applicationId: data.id };
  } catch (error) {
    console.error('Error creating startup application:', error);
    throw error;
  }
}

/**
 * Get a startup application by ID
 * @param jobId - The ID of the job to get the application for
 * @returns The application or null if it doesn't exist
 */
export async function getStartupApplication(
  jobId: string,
  userEmail: string
): Promise<StartupApplication | null> {
  const supabase = createServerClient();

  if (!userEmail) {
    return null;
  }
  const { data, error } = await supabase
    .from('startup_applications')
    .select('*')
    .eq('job', jobId)
    .eq('candidate', userEmail)
    .maybeSingle(); // Use maybeSingle to avoid throwing errors if no rows found

  if (error) {
    console.error('Error fetching startup application:', error);
    return null;
  }

  return data;
}

/*
 * Get all applications for a user
 * @param userEmail - The email of the user to get applications for
 * @returns The applications or an empty array if there are no applications
 */
export async function getApplicationsForUser(
  userEmail: string
): Promise<StartupApplication[]> {
  const supabase = createServerClient();

  const { data, error } = await supabase
    .from('startup_applications')
    .select('*')
    .eq('candidate', userEmail);

  if (error) {
    console.error('Error fetching applications for user:', error);
    throw error;
  }

  return data || [];
}
