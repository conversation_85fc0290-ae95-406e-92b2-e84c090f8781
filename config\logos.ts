// Company logos configuration for the opportunities section

// Simple array of all logos with basic properties
export const allLogos = [
  { src: '/company_logo/google_logo.svg', alt: 'Google' },
  { src: '/company_logo/microsoft_log.svg', alt: 'Microsoft' },
  { src: '/company_logo/amazon_logo.svg', alt: 'Amazon' },
  { src: '/company_logo/tesla_logo.svg', alt: 'Tesla' },
  { src: '/company_logo/paypal_logo.svg', alt: 'PayPal' },
  { src: '/company_logo/adobe_logo.svg', alt: 'Adobe' },
  { src: '/company_logo/wallmart_logo.svg', alt: 'Walmart' },
  { src: '/company_logo/jobbridge_logo.svg', alt: 'JobBridge' },
  { src: '/company_logo/prelim_logo.svg', alt: 'Prelim' },
  { src: '/company_logo/bridge_logo.svg', alt: 'Bridge' },
  { src: '/company_logo/layer_zero_logo.svg', alt: 'Layer Zero' },
  { src: '/company_logo/fedex_logo.svg', alt: 'FedEx' },
  { src: '/company_logo/nabidex_logo.svg', alt: 'Nabidex' },
  { src: '/company_logo/multiply_logo.svg', alt: 'Multiply' },
  { src: '/company_logo/greekpeak_logo.svg', alt: 'Greenpeak' },
];

// Desktop scattered view configuration with positioning data
export const scatteredLogos = [
  // Larger logos with exact positioning coordinates
  {
    src: '/company_logo/google_logo.svg',
    alt: 'Google',
    desktopSize: 180,
    // Using exact coordinates instead of percentages
    top: '10vh',
    left: '50vw', 
    animation: 'animate-float-delay-3',
  },
  {
    src: '/company_logo/microsoft_log.svg',
    alt: 'Microsoft',
    desktopSize: 170,
    top: '15vh',
    left: '20vw',
    animation: 'animate-float-delay-1',
  },
  {
    src: '/company_logo/amazon_logo.svg',
    alt: 'Amazon',
    desktopSize: 190,
    top: '70vh',
    left: '15vw',
    animation: 'animate-float-slow',
  },
  {
    src: '/company_logo/tesla_logo.svg',
    alt: 'Tesla',
    desktopSize: 160,
    top: '30vh',
    left: '80vw',
    animation: 'animate-float-delay-2',
  },
  
  // Medium logos with spacing to prevent overlap
  {
    src: '/company_logo/paypal_logo.svg',
    alt: 'PayPal',
    desktopSize: 140,
    top: '60vh',
    left: '70vw',
    animation: 'animate-float-delay-1',
  },
  {
    src: '/company_logo/adobe_logo.svg',
    alt: 'Adobe',
    desktopSize: 130,
    top: '75vh',
    left: '85vw',
    animation: 'animate-float-delay-3',
  },
  {
    src: '/company_logo/wallmart_logo.svg',
    alt: 'Walmart',
    desktopSize: 150,
    top: '85vh',
    left: '65vw',
    animation: 'animate-float-delay-2',
  },
  {
    src: '/company_logo/jobbridge_logo.svg',
    alt: 'JobBridge',
    desktopSize: 135,
    top: '88vh',
    left: '40vw',
    animation: 'animate-float-slow',
  },
  
  // Smaller logos with positions far from other logos
  {
    src: '/company_logo/prelim_logo.svg',
    alt: 'Prelim',
    desktopSize: 95,
    top: '5vh',
    left: '10vw',
    animation: 'animate-float-delay-2',
  },
  {
    src: '/company_logo/bridge_logo.svg',
    alt: 'Bridge',
    desktopSize: 105,
    top: '8vh',
    left: '90vw',
    animation: 'animate-float-delay-1',
  },
  {
    src: '/company_logo/layer_zero_logo.svg',
    alt: 'Layer Zero',
    desktopSize: 90,
    top: '40vh',
    left: '5vw',
    animation: 'animate-float-delay-3',
  },
  {
    src: '/company_logo/fedex_logo.svg',
    alt: 'FedEx',
    desktopSize: 120,
    top: '65vh',
    left: '8vw',
    animation: 'animate-float-delay-2',
  },
  {
    src: '/company_logo/nabidex_logo.svg',
    alt: 'Nabidex',
    desktopSize: 110,
    top: '78vh',
    left: '25vw',
    animation: 'animate-float-delay-1',
  },
  {
    src: '/company_logo/multiply_logo.svg',
    alt: 'Multiply',
    desktopSize: 105,
    top: '25vh',
    left: '12vw',
    animation: 'animate-float-delay-2',
  },
  {
    src: '/company_logo/greekpeak_logo.svg',
    alt: 'Greenpeak',
    desktopSize: 100,
    top: '92vh',
    left: '20vw',
    animation: 'animate-float-delay-3',
  },
];
