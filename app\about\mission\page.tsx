import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { CircleUser, FileText, Users } from 'lucide-react';

export default function Home() {
  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="container mx-auto py-16 px-4 md:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div className="relative aspect-[5/4] w-full">
            <Image
              src="https://s3-alpha-sig.figma.com/img/a7cc/b541/a848849be4d7d919aa81c5d4945481a4?Expires=1743379200&Key-Pair-Id=APKAQ4GOSFWCW27IBOMQ&Signature=mPcdyOgSsdOH4kvIVnKt8uRM5VRNGzAPm8rLSKDW1svuyXibfeFoKF-CUscbSBS1DUbP5G7nBbbA4~lEsw2wLkDCF-SC6Yj3A19i320pBolYxtWQ-fGxUhv6oBd~uqhjXGMs6QJ0Bkc~z71KKr6qOu4YebzuSEJNMWmCXwDTGpGxRmqZMb5QVWrzKQDWHAHJZKcnDWLHxq6MVi~mvhnWFc9phg8ZeJL2vWtz~lelSb6e0Jbkqv-sDfp7w3g~3ROJVb2-ZklUaO8P~w3wdsL0bWDVPYaCaPK~kHW77~w8WYwjMhZiG25aBwy4shMWm9hDZwmoV8GEMHDPvkNfz739uQ__"
              alt="International students looking at a building"
              fill
              className="rounded-lg object-cover"
              priority
            />
          </div>
          <div className="space-y-6">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 leading-tight">
              We helped thousands of international students launch their
              careers.
            </h1>
            <p className="text-lg text-gray-700">
              Your journey to growth, learning, and success starts here. Through
              hands-on experiences and peer-driven support, we empower
              international students to build skills and unlock opportunities.
            </p>
          </div>
        </div>
      </section>

      {/* Who We Are Section */}
      <section className="bg-[#118073] text-white py-16 px-4 md:px-6 lg:px-8">
        <div className="container mx-auto max-w-4xl space-y-16">
          <div className="space-y-4">
            <h2 className="text-3xl font-bold">Who We Are</h2>
            <p className="text-lg opacity-90">
              InternUp is dedicated to helping international students transition
              from internships to full-time careers in the U.S. By providing
              real-world experiences and fostering a supportive community, we
              empower students to gain the skills, confidence, and connections
              needed to succeed.
            </p>
          </div>

          <div className="space-y-4">
            <h2 className="text-3xl font-bold">Our Mission</h2>
            <p className="text-lg opacity-90">
              Our mission is to provide international students with the tools,
              resources, and support they need to transition from school to the
              global job market with confidence.
            </p>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg p-6 text-gray-900">
              <div className="w-12 h-12 bg-[#118073]/10 rounded-full flex items-center justify-center mb-4">
                <FileText className="h-6 w-6 text-[#118073]" />
              </div>
              <h3 className="text-xl font-bold mb-2">Our Tools</h3>
              <p className="text-gray-700">
                Personalized AI-powered career matching, job filtering, employer
                matching
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 text-gray-900">
              <div className="w-12 h-12 bg-[#118073]/10 rounded-full flex items-center justify-center mb-4">
                <CircleUser className="h-6 w-6 text-[#118073]" />
              </div>
              <h3 className="text-xl font-bold mb-2">Our Resources</h3>
              <p className="text-gray-700">
                Professional industry insights, exclusive referrals, and
                ready-to-use career material templates
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 text-gray-900">
              <div className="w-12 h-12 bg-[#118073]/10 rounded-full flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-[#118073]" />
              </div>
              <h3 className="text-xl font-bold mb-2">Our Support</h3>
              <p className="text-gray-700">
                Peer-driven community mentorship opportunities, simplified job
                search and confidence building
              </p>
            </div>
          </div>

          {/* Values */}
          <div className="space-y-6">
            <h2 className="text-3xl font-bold">Our Values</h2>
            <p className="text-lg opacity-90">
              At InternUp, we are driven by empowerment, collaboration, and
              inclusion. We believe in equipping international students with the
              confidence, skills, and community support needed to thrive in a
              diverse global workforce.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg p-6 text-gray-900">
                <h3 className="text-xl font-bold mb-2">Empowerment</h3>
                <p className="text-gray-700 opacity-90">
                  We equip students with the confidence and skills to achieve
                  their career goals.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 text-gray-900">
                <h3 className="text-xl font-bold mb-2">Collaboration</h3>
                <p className="text-gray-700 opacity-90">
                  We foster a supportive peer-driven community where students
                  learn and grow together.
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 text-gray-900">
                <h3 className="text-xl font-bold mb-2">
                  Diversity and Inclusion
                </h3>
                <p className="text-gray-700 opacity-90">
                  We celebrate diverse perspectives and help create a workforce
                  that truly values differences.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Join Us Section */}
      <section className="py-20 px-4 md:px-6 lg:px-8 relative">
        <div className="container mx-auto max-w-4xl text-center space-y-8 relative">
          <h2 className="text-3xl font-bold">
            Join Us on the Path to Build Careers and Futures
          </h2>

          <div className="text-6xl font-bold text-gray-900">2,000+</div>
          <p className="text-lg text-gray-700 opacity-90">
            students have already joined
          </p>

          <Button className="bg-[#118073] hover:bg-[#118073]/80 text-white">
            Join Our Community
          </Button>
        </div>
      </section>
    </main>
  );
}
