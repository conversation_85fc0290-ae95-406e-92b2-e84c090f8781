/**
 * TypeScript types for Public Firms database schemas
 */

/**
 * Represents a company in the public_firm_companies table
 */
export interface PublicFirmCompany {
  id: string;
  applied?: string[] | null;
  description?: string | null;
  industry?: string | null;
  insider?: string | null;
  live?: string | null;
  location?: string | null;
  logo?: string | null;
  name?: string | null;
  creation_date?: string | null;
  modified_date?: string | null;
  slug?: string | null;
  creator?: string | null;
}

/**
 * Represents a job application in the public_firm_applications table
 */
export interface PublicFirmApplication {
  id: string;
  job_id: string;
  job_title?: string;
  company?: string;
  company_name?: string;
  candidate: string;
  creator?: string;
  status: string;
  creation_date: string;
  applied_via_link?: boolean;
  additional_materials?: string | null;
  cover_note?: string | null;
  decline_reasons?: string | null;
  incomplete?: string | null;
  job_link?: string | null;
  number_of_applications?: string | null;
  referal_feedback?: string | null;
  referral?: string | null;
  referred_by?: string | null;
  resume?: string | null;
  shortlisted?: string | null;
  sponsorship?: string | null;
  yoe_internship?: string | null;
  yoe_job?: string | null;
  modified_date?: string | null;
  slug?: string | null;
}

/**
 * Represents a job posting in the public_firm_jobs table
 */
export interface PublicFirmJob {
  id: string;
  applied_user?: string[] | null;
  company?: string | null;
  description?: string | null;
  expire_date?: string | null;
  favor_applied?: string | null;
  favored?: string[] | null;
  industry?: string | null;
  insider?: string | null;
  job_link?: string | null;
  job_title?: string | null;
  live?: string | null;
  location?: string | null;
  logo?: string | null;
  number_of_openning?: string | null;
  term?: string | null;
  creation_date?: string | null;
  modified_date?: string | null;
  slug?: string | null;
  creator?: string | null;
  company_name?: string | null;
}

/**
 * Input type for creating a new public firm company
 */
export type CreatePublicFirmCompanyInput = Omit<
  PublicFirmCompany,
  'id' | 'applied' | 'creation_date' | 'modified_date'
>;

/**
 * Input type for creating a new public firm job
 */
export type CreatePublicFirmJobInput = Omit<
  PublicFirmJob,
  'id' | 'applied_user' | 'favored' | 'creation_date' | 'modified_date'
>;

/**
 * Input type for creating a new public firm application
 */
export type CreatePublicFirmApplicationInput = Omit<
  PublicFirmApplication,
  'id' | 'creation_date' | 'modified_date'
>;

/**
 * Response type for paginated queries
 */
export interface PaginatedResponse<T> {
  data: T[];
  count: number | null;
  currentPage?: number;
  totalPages?: number;
}

/**
 * Filter options for public firm jobs
 */
export interface PublicFirmJobFilters {
  search?: string;
  location?: string;
  term?: string;
  active?: boolean;
}
