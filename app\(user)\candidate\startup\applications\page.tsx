'use client';

import { useState, useEffect } from 'react';
import { FileEdit, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible';
import { toast } from '@/components/ui/use-toast';
import { StartupApplication } from '@/types/startups';
import { getApplications } from '@/actions/admin/startup/applications';
import { useUser } from '@/hooks/useUser';
export default function StartupApplicationsPage() {
  const [applications, setApplications] = useState<StartupApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date-desc');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const { user } = useUser();
  // Status counts
  const statusCounts = {
    total: applications.length,
    Pending: applications.filter((app) => app.status === 'Pending').length,
    Applying: applications.filter((app) => app.status === 'Applying').length,
    Rejected: applications.filter((app) => app.status === 'Rejected').length,
    Accepted: applications.filter((app) => app.status === 'Accepted').length,
    Applied: applications.filter((app) => app.status === 'Applied').length
  };

  // Fetch applications
  useEffect(() => {
    if (user) {
      fetchApplications();
    }
  }, [currentPage, user]);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const response = await getApplications({
        page: currentPage,
        user: user
      });
      setApplications(response.data);
      setTotalPages(response.totalPages || 1);
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to fetch applications',
        variant: 'destructive'
      });
      setApplications([]);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort applications
  const filteredAndSortedApplications = applications
    .filter((application) => {
      return (
        selectedStatus === 'all' ||
        application.status?.toLowerCase() === selectedStatus.toLowerCase()
      );
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return (
            new Date(b.creation_date || '').getTime() -
            new Date(a.creation_date || '').getTime()
          );
        case 'date-asc':
          return (
            new Date(a.creation_date || '').getTime() -
            new Date(b.creation_date || '').getTime()
          );
        default:
          return 0;
      }
    });

  const handleStatusChange = (value: string) => {
    setSelectedStatus(value);
  };

  const handleSortChange = (value: string) => {
    setSortBy(value);
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Startup Applications
        </h1>
        <p className="text-muted-foreground">
          Here are your current startup applications
        </p>
      </div>

      {/* Status Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-sm font-medium">Total</CardTitle>
          </CardHeader>
          <CardContent className="py-0">
            <div className="text-2xl font-bold">{statusCounts.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
          </CardHeader>
          <CardContent className="py-0">
            <div className="text-2xl font-bold">{statusCounts.Pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-sm font-medium">Applying</CardTitle>
          </CardHeader>
          <CardContent className="py-0">
            <div className="text-2xl font-bold">{statusCounts.Applying}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-sm font-medium">Rejected</CardTitle>
          </CardHeader>
          <CardContent className="py-0">
            <div className="text-2xl font-bold">{statusCounts.Rejected}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="py-3">
            <CardTitle className="text-sm font-medium">Accepted</CardTitle>
          </CardHeader>
          <CardContent className="py-0">
            <div className="text-2xl font-bold">{statusCounts.Accepted}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters - removed search input */}
      <div className="flex justify-end">
        <div className="flex flex-col sm:flex-row gap-2">
          <Select value={selectedStatus} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Applying">Applying</SelectItem>
              <SelectItem value="Rejected">Rejected</SelectItem>
              <SelectItem value="Accepted">Accepted</SelectItem>
              <SelectItem value="Applied">Applied</SelectItem>
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={handleSortChange}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date-desc">Newest First</SelectItem>
              <SelectItem value="date-asc">Oldest First</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {filteredAndSortedApplications.map((application) => (
          <ApplicationCard key={application.id} application={application} />
        ))}

        {filteredAndSortedApplications.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <h3 className="mt-4 text-lg font-semibold">
              No applications found
            </h3>
            <p className="mt-2 text-sm text-muted-foreground max-w-xs">
              {selectedStatus !== 'all'
                ? 'No applications match your current filters. Try adjusting your filter criteria.'
                : "You haven't applied to any startup positions yet."}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

interface ApplicationCardProps {
  application: StartupApplication;
}

function ApplicationCard({ application }: ApplicationCardProps) {
  return (
    <Collapsible className="border rounded-lg">
      <div className="flex items-center justify-between p-4">
        <div className="grid gap-1">
          <div className="font-semibold">{application.job_title}</div>
          <div className="text-sm text-muted-foreground">
            {application.company_name}
          </div>
        </div>
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <StatusBadge status={application.status || 'Pending'} />
          </div>
          <div className="hidden md:block text-sm text-muted-foreground">
            Applied on{' '}
            {new Date(application.creation_date || '').toLocaleDateString()}
          </div>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm">
              <ChevronRight className="h-4 w-4" />
            </Button>
          </CollapsibleTrigger>
        </div>
      </div>

      <CollapsibleContent>
        <div className="px-4 pb-4 pt-0 border-t">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-1">Status</h4>
                  <StatusBadge status={application.status || 'Pending'} />
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-1">Applied On</h4>
                  <p className="text-sm">
                    {new Date(
                      application.creation_date || ''
                    ).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-1">
                Application Materials
              </h4>
              <div className="space-y-2">
                {application.resume && (
                  <div className="flex items-center gap-2">
                    <FileEdit className="h-4 w-4" />
                    <a
                      href={application.resume}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      View Resume
                    </a>
                  </div>
                )}
                {application.additional_materials && (
                  <div className="flex items-center gap-2">
                    <FileEdit className="h-4 w-4" />
                    <a
                      href={application.additional_materials}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      View Additional Materials
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
}

function StatusBadge({ status }: { status: string }) {
  // Helper function to get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Accepted':
        return 'bg-green-500';
      case 'Rejected':
        return 'bg-red-500';
      case 'Applying':
        return 'bg-blue-500';
      case 'Failed Requirement':
        return 'bg-orange-500';
      case 'Pending':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <Badge
      variant="outline"
      className={`${getStatusBadgeColor(status)} text-white`}
    >
      {status}
    </Badge>
  );
}
