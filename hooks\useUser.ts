'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Database } from '@/types/db';

export type PublicUser = Database['public']['Tables']['users']['Row'];

export function useUser() {
  const [user, setUser] = useState<PublicUser | null>(null);
  const [isOAuthUser, setIsOAuthUser] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const supabase = createClient();

    const fetchUser = async () => {
      const {
        data: { user: authUser }
      } = await supabase.auth.getUser();

      if (!authUser?.email) {
        setUser(null);
        setLoading(false);
        setIsOAuthUser(null);
        return;
      }

      setIsOAuthUser(authUser.app_metadata.provider !== 'email');

      const { data: userData, error } = await supabase
        .from('users')
        .select('*')
        .eq('email', authUser.email)
        .single();

      if (error) {
        console.error('Error fetching public user row:', error.message);
        setUser(null);
      } else {
        setUser(userData);
      }

      setLoading(false);
    };

    fetchUser();
  }, []);

  return { user, loading, isOAuthUser };
}
