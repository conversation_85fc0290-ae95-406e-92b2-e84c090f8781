'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { User } from '@/types/user';

export function useUser() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const [isOAuthUser, setIsOAuthUser] = useState<boolean | null>(null);

  useEffect(() => {
    // Set a variable to track if this effect is still valid
    let isMounted = true;

    async function loadUser() {
      if (!isMounted) return;

      // Keep loading state true until we finish
      setLoading(true);

      try {
        const supabase = createClient();

        // Get the authenticated user from Supabase auth
        const {
          data: { user: authUser }
        } = await supabase.auth.getUser();

        if (!isMounted) return;

        if (!authUser?.email) {
          setUser(null);
          setIsOAuthUser(null);
          setInitialized(true);
          return;
        }

        // Determine if this is an OAuth user
        setIsOAuthUser(authUser.app_metadata.provider !== 'email');

        // Fetch user data from our users table
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('email', authUser.email)
          .single();

        if (!isMounted) return;

        if (error) {
          console.error('Error fetching user data:', error.message);
          setUser(null);
        } else {
          console.log('User data:', userData);
          // Explicitly cast to our User type to avoid database schema conflicts
          setUser(userData as User);
        }

        setInitialized(true);
      } catch (error) {
        if (!isMounted) return;

        console.error('Error loading user:', error);
        setUser(null);
        setIsOAuthUser(null);
        setInitialized(true);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    }

    loadUser();

    // Cleanup function to prevent setting state on unmounted component
    return () => {
      isMounted = false;
    };
  }, []);

  return { user, loading, initialized, isOAuthUser };
}
