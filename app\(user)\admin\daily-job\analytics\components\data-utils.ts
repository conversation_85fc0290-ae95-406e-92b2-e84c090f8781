import { formatDate } from '@/lib/utils';
import type { DailyJob } from '@/types/crawler';

export interface ChartDataItem {
  date: string;
  vip: number;
  nonVip: number;
  total: number;
}

export interface UserTypeItem {
  name: string;
  value: number;
  color: string;
}

export interface FileTypeItem {
  name: string;
  value: number;
  color: string;
}

export const prepareDailyDownloadsData = (
  jobs: DailyJob[]
): ChartDataItem[] => {
  // Group by date and count downloads
  const dataMap = new Map<
    string,
    { date: string; vip: number; nonVip: number; total: number }
  >();

  jobs.forEach((job) => {
    const dateStr = formatDate(
      job.posting_date ? new Date(job.posting_date).toISOString() : ''
    );
    const vipCount = job.downloader_vip?.length || 0;
    const nonVipCount = job.downloader_nonvip?.length || 0;

    if (dataMap.has(dateStr)) {
      const existing = dataMap.get(dateStr)!;
      dataMap.set(dateStr, {
        date: dateStr,
        vip: existing.vip + vipCount,
        nonVip: existing.nonVip + nonVipCount,
        total: existing.total + vipCount + nonVipCount
      });
    } else {
      dataMap.set(dateStr, {
        date: dateStr,
        vip: vipCount,
        nonVip: nonVipCount,
        total: vipCount + nonVipCount
      });
    }
  });

  return Array.from(dataMap.values());
};

export const createUserTypeData = (
  vipDownloads: number,
  nonVipDownloads: number
): UserTypeItem[] => {
  return [
    { name: 'VIP Users', value: vipDownloads, color: '#8884d8' },
    { name: 'Non-VIP Users', value: nonVipDownloads, color: '#82ca9d' }
  ];
};

export const prepareFileTypeData = (jobs: DailyJob[]): FileTypeItem[] => {
  const types = new Map<string, number>();

  jobs.forEach((job) => {
    const fileType = job.file_type || 'Daily Jobs';
    const downloadCount =
      (job.downloader_vip?.length || 0) + (job.downloader_nonvip?.length || 0);

    if (types.has(fileType)) {
      types.set(fileType, types.get(fileType)! + downloadCount);
    } else {
      types.set(fileType, downloadCount);
    }
  });

  return Array.from(types.entries()).map(([name, value], index) => ({
    name,
    value,
    color: `hsl(${index * 45}, 70%, 60%)`
  }));
};

export const filterJobsByPeriod = (
  jobs: DailyJob[],
  period: string
): DailyJob[] => {
  if (jobs.length === 0) return [];

  const now = new Date();
  let filtered: DailyJob[] = [];

  switch (period) {
    case '7days': {
      const last7Days = new Date(now);
      last7Days.setDate(now.getDate() - 7);
      filtered = jobs.filter(
        (job) => new Date(job.posting_date || '') >= last7Days
      );
      break;
    }
    case '30days': {
      const last30Days = new Date(now);
      last30Days.setDate(now.getDate() - 30);
      filtered = jobs.filter(
        (job) => new Date(job.posting_date || '') >= last30Days
      );
      break;
    }
    case '90days': {
      const last90Days = new Date(now);
      last90Days.setDate(now.getDate() - 90);
      filtered = jobs.filter(
        (job) => new Date(job.posting_date || '') >= last90Days
      );
      break;
    }
    case 'all':
    default:
      filtered = [...jobs];
      break;
  }

  return filtered.sort(
    (a, b) =>
      new Date(a.posting_date || '').getTime() -
      new Date(b.posting_date || '').getTime()
  );
};

export const calculateStats = (jobs: DailyJob[]) => {
  let totalVipDownloads = 0;
  let totalNonVipDownloads = 0;
  const allUsers = new Set<string>();

  jobs.forEach((job) => {
    const vipUsers = job.downloader_vip || [];
    const nonVipUsers = job.downloader_nonvip || [];

    totalVipDownloads += vipUsers.length;
    totalNonVipDownloads += nonVipUsers.length;

    vipUsers.forEach((user) => allUsers.add(user));
    nonVipUsers.forEach((user) => allUsers.add(user));
  });

  return {
    vipDownloads: totalVipDownloads,
    nonVipDownloads: totalNonVipDownloads,
    totalDownloads: totalVipDownloads + totalNonVipDownloads,
    uniqueUsers: allUsers.size
  };
};
