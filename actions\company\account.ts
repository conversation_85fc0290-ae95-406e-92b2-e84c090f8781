'use server';

import { revalidatePath } from 'next/cache';
import { createClient } from '@/utils/supabase/server';
import type { StartupCompany } from '@/types/startups';

export async function getCompanyDetails(
  userId: string
): Promise<StartupCompany | null> {
  const supabase = createClient();

  // First, get the company ID from the user record
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('startup_company')
    .eq('id', userId)
    .single();

  if (userError || !userData?.startup_company) {
    console.error("Error fetching user's company ID:", userError);
    return null;
  }

  // Then fetch the company details using the company ID
  const { data, error } = await supabase
    .from('startup_companies')
    .select('*')
    .eq('id', userData.startup_company)
    .single();

  if (error) {
    console.error('Error fetching company details:', error);
    return null;
  }

  return data as StartupCompany;
}

export async function updateCompanyDetails(
  companyId: string,
  companyData: Partial<StartupCompany>
): Promise<StartupCompany | null> {
  const supabase = createClient();

  // If name is provided, also update name_lowercase
  if (companyData.name) {
    companyData.name_lowercase = companyData.name.toLowerCase();
  }

  // Add modified timestamp
  companyData.modified_at = new Date().toISOString();

  const { data, error } = await supabase
    .from('startup_companies')
    .update(companyData)
    .eq('id', companyId)
    .select()
    .single();

  if (error) {
    console.error('Error updating company details:', error);
    throw new Error(error.message);
  }

  revalidatePath('/company');
  return data as StartupCompany;
}

export async function createCompanyProfile(
  userId: string,
  companyData: Partial<StartupCompany>
): Promise<{ companyId: string }> {
  const supabase = createClient();

  // If name is provided, also set name_lowercase
  if (companyData.name) {
    companyData.name_lowercase = companyData.name.toLowerCase();
  }

  // Add timestamps
  const now = new Date().toISOString();
  companyData.created_at = now;
  companyData.modified_at = now;

  // Create the company record
  const { data: newCompanyData, error: companyError } = await supabase
    .from('startup_companies')
    .insert(companyData)
    .select()
    .single();

  if (companyError) {
    console.error('Error creating company profile:', companyError);
    throw new Error(companyError.message);
  }

  // Update the user record with the company ID
  const { error: userError } = await supabase
    .from('users')
    .update({ startup_company: newCompanyData.id })
    .eq('id', userId);

  if (userError) {
    console.error('Error linking company to user:', userError);
    throw new Error(userError.message);
  }

  revalidatePath('/company');
  return { companyId: newCompanyData.id };
}
