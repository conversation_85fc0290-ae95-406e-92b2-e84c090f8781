import * as z from 'zod';

// Form Schemas
export const personalDetailsSchema = z.object({
  firstName: z
    .string()
    .min(2, { message: 'First name must be at least 2 characters.' }),
  lastName: z
    .string()
    .min(2, { message: 'Last name must be at least 2 characters.' }),
  preferredName: z.string().optional().nullable(),
  nationality: z
    .string()
    .min(1, { message: 'Please select your nationality.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  phone: z.string().optional().nullable(),
  location: z.string().min(1, { message: 'Please select your location.' }),
  sponsorship: z
    .string()
    .min(1, { message: 'Please select your sponsorship status.' }),
  mbti: z.string().optional().nullable(),
  bio: z.string().max(500, { message: 'B<PERSON> must not exceed 500 characters.' }),
  portfolioLink: z.string().url().optional().nullable(),
  linkedIn: z.string().url({ message: 'Please enter a valid LinkedIn URL.' }),
  openToReverseHiring: z.boolean().default(false)
});

export const workPreferencesSchema = z.object({
  jobTypes: z
    .array(z.string())
    .min(1, { message: 'Please select at least one job type.' }),
  workTypes: z
    .array(z.string())
    .min(1, { message: 'Please select at least one work type.' }),
  activelyLooking: z.boolean(),
  seekingCoaching: z.boolean()
});

export const securitySchema = z
  .object({
    oldPassword: z
      .string()
      .min(8, { message: 'Old password must be at least 8 characters.' }),
    newPassword: z
      .string()
      .min(8, { message: 'New password must be at least 8 characters.' }),
    confirmPassword: z
      .string()
      .min(8, { message: 'Confirm password must be at least 8 characters.' })
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword']
  });

// Options Lists
export const jobTypes = [
  'Accountant',
  'AI Engineer',
  'Back-end Dev',
  'Business Analyst',
  'Business Developer',
  'Business Operation',
  'CAD Engineer',
  'Data Analyst',
  'Database Engineer',
  'Data Scientist',
  'DevOps',
  'EE Engineer',
  'Finance Analyst',
  'Front-end Dev',
  'Full Stack Developer',
  'Graphic Designer',
  'HR Assistant',
  'LLM Engineer',
  'Machine Learning Engineer',
  'Marketer/Researcher',
  'Mechanical Engineer',
  'Operations Manager',
  'Product Designer',
  'Product Manager',
  'Quantitative Research',
  'Robotics Engineer',
  'Software Automation Engineer'
] as const;

export const workTypes = [
  'Full Time',
  'Part Time',
  'Volunteer',
  'OPT/CPT'
] as const;

// US states for location dropdown
export const usStates = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
  { value: 'DC', label: 'District of Columbia' },
  { value: 'outside_us', label: 'Outside US' }
];
