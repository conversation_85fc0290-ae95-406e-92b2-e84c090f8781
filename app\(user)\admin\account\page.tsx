'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ImageUpload } from '@/components/ui/image-upload';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Pencil,
  User,
  MapPin,
  Check,
  Loader2,
  Lock,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { useUser } from '@/hooks/useUser';
import { updateAdminProfile } from '@/actions/admin/account';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import { resetPassword } from '@/utils/bubble/auth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { createClient } from '@/utils/supabase/client';
import { deleteImage } from '@/utils/supabase/storage/client';

const profileSchema = z.object({
  firstName: z
    .string()
    .min(2, { message: 'First name must be at least 2 characters.' }),
  lastName: z
    .string()
    .min(2, { message: 'Last name must be at least 2 characters.' }),
  bio: z.string().max(500, { message: 'Bio must not exceed 500 characters.' }).optional().nullable(),
  location: z.string().min(1, { message: 'Please enter your location.' }).optional().nullable(),
});

const securitySchema = z.object({
  oldPassword: z
    .string()
    .min(8, { message: 'Old password must be at least 8 characters.' }),
  newPassword: z
    .string()
    .min(8, { message: 'New password must be at least 8 characters.' }),
  confirmPassword: z
    .string()
    .min(8, { message: 'Confirm password must be at least 8 characters.' })
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export default function AdminAccountPage() {
  const [activeTab, setActiveTab] = useState('profile');
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localUserData, setLocalUserData] = useState<any>(null);
  const { user } = useUser();
  const router = useRouter();
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [isOAuthUser, setIsOAuthUser] = useState(false);
  const { toast } = useToast();
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetPasswordError, setResetPasswordError] = useState<string | null>(null);
  const [resetPasswordSuccess, setResetPasswordSuccess] = useState(false);

  useEffect(() => {
    async function checkOAuthStatus() {
      if (user) {
        setLocalUserData(user);
        
        // Check if user is logged in via OAuth (Supabase)
        const supabase = createClient();
        const { data: { session } } = await supabase.auth.getSession();
        
        // If there's a valid Supabase session, the user is authenticated via OAuth
        setIsOAuthUser(!!session);
      }
    }
    
    checkOAuthStatus();
  }, [user]);

  const profileForm = useForm<z.infer<typeof profileSchema>>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: user?.first_name || '',
      lastName: user?.last_name || '',
      bio: user?.bio || '',
      location: user?.location || '',
    }
  });

  const securityForm = useForm<z.infer<typeof securitySchema>>({
    resolver: zodResolver(securitySchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  useEffect(() => {
    if (user) {
      profileForm.reset({
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        bio: user.bio || '',
        location: user.location || '',
      });
    }
  }, [user, profileForm]);

  const handleImageUpload = async (url: string) => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'User ID not found. Please try again later.',
        variant: 'destructive'
      });
      return;
    }

    setIsImageUploading(true);

    try {
      // If url is empty, it means the image was removed
      if (!url) {
        // Update the user profile with empty avatar URL
        await updateAdminProfile(user.id, {
          avatar_url: ''
        });

        // Update local state
        setLocalUserData({
          ...localUserData,
          avatar_url: ''
        });

        toast({
          title: 'Success',
          description: 'Profile picture removed successfully',
          variant: 'default'
        });
        
        setIsImageUploading(false);
        return;
      }

      // Store the old avatar URL for later deletion
      const oldAvatarUrl = user.avatar_url;

      // First update the user profile with the new avatar URL
      await updateAdminProfile(user.id, {
        avatar_url: url
      });

      // Update local state
      setLocalUserData({
        ...localUserData,
        avatar_url: url
      });

      // Now that the profile is updated, delete the old avatar if it exists
      if (oldAvatarUrl) {
        try {
          await deleteImage(oldAvatarUrl);
        } catch (deleteError) {
          console.error('Error deleting existing image:', deleteError);
          // Non-critical error, the profile update is already successful
        }
      }

      toast({
        title: 'Success',
        description: 'Profile picture updated successfully',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error updating profile image:', error);

      let errorMessage = 'Failed to update profile picture';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: 'Upload Failed',
        description: errorMessage,
        variant: 'destructive'
      });

      // Revert local state on failure
      setLocalUserData({
        ...localUserData,
        avatar_url: user.avatar_url
      });
    } finally {
      setIsImageUploading(false);
    }
  };

  const onProfileSubmit = async (data: z.infer<typeof profileSchema>) => {
    if (!user) return;

    setIsSubmitting(true);
    try {
      const updatedUser = await updateAdminProfile(user.id, {
        first_name: data.firstName,
        last_name: data.lastName,
        bio: data.bio,
        location: data.location,
      });

      setLocalUserData({
        ...localUserData,
        first_name: data.firstName,
        last_name: data.lastName,
        bio: data.bio,
        location: data.location,
      });

      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
        variant: 'default'
      });

      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to update profile. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onSecuritySubmit = async (data: z.infer<typeof securitySchema>) => {
    if (!user?.email) return;

    setIsSubmitting(true);
    setResetPasswordError(null);
    setResetPasswordSuccess(false);

    try {
      await resetPassword(user.email, data.oldPassword, data.newPassword);
      
      setResetPasswordSuccess(true);
      securityForm.reset({
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      toast({
        title: 'Password updated',
        description: 'Your password has been updated successfully.',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error resetting password:', error);
      setResetPasswordError('Failed to reset password. Please check your old password and try again.');
      
      toast({
        title: 'Error',
        description: 'Failed to update password. Please check your old password and try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto" />
          <p className="mt-2">Loading your profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Account Settings</h1>
      
      <Tabs defaultValue="profile" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="credentials">Credentials</TabsTrigger>
        </TabsList>
        
        {/* Profile Tab */}
        <TabsContent value="profile" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Update your profile information and profile picture
                  </CardDescription>
                </div>
                {!isEditing && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                  >
                    <Pencil className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-8">
                {/* Profile Picture */}
                <div className="flex flex-col items-center space-y-4">
                  <div className="h-24 w-24 relative mx-auto">
                    <ImageUpload
                      value={localUserData?.avatar_url || ''}
                      onChange={handleImageUpload}
                      className="h-24 w-24"
                      bucket="user-avatars"
                      folder={`admin-${user?.id}`}
                      disabled={!isEditing}
                    />
                    {isImageUploading && (
                      <div className="absolute inset-0 bg-background/80 flex items-center justify-center rounded-md">
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span className="text-xs">Uploading...</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Profile Form */}
                <div className="flex-1">
                  <Form {...profileForm}>
                    <form
                      onSubmit={profileForm.handleSubmit(onProfileSubmit)}
                      className="space-y-6"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={profileForm.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="First Name"
                                  {...field}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={profileForm.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Last Name"
                                  {...field}
                                  disabled={!isEditing}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={profileForm.control}
                        name="location"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Location</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  placeholder="Location"
                                  className="pl-10"
                                  value={field.value ?? ''}
                                  onChange={field.onChange}
                                  onBlur={field.onBlur}
                                  disabled={!isEditing}
                                />
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={profileForm.control}
                        name="bio"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>About</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Tell us about yourself"
                                className="resize-none"
                                value={field.value ?? ''}
                                onChange={field.onChange}
                                onBlur={field.onBlur}
                                disabled={!isEditing}
                              />
                            </FormControl>
                            <FormDescription>
                              Brief description for your profile.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      {isEditing && (
                        <div className="flex justify-end space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => {
                              setIsEditing(false);
                              profileForm.reset({
                                firstName: user.first_name || '',
                                lastName: user.last_name || '',
                                bio: user.bio || '',
                                location: user.location || '',
                              });
                            }}
                            disabled={isSubmitting}
                          >
                            Cancel
                          </Button>
                          <Button type="submit" disabled={isSubmitting}>
                            {isSubmitting ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Check className="mr-2 h-4 w-4" />
                                Save Changes
                              </>
                            )}
                          </Button>
                        </div>
                      )}
                    </form>
                  </Form>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Credentials Tab */}
        <TabsContent value="credentials" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Password</CardTitle>
              <CardDescription>
                Change your password
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isOAuthUser ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>OAuth Account</AlertTitle>
                  <AlertDescription>
                    You signed up using a social provider. Password management is handled by your provider.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  {resetPasswordSuccess && (
                    <Alert className="mb-6 bg-green-50 border-green-200">
                      <Check className="h-4 w-4 text-green-600" />
                      <AlertTitle className="text-green-600">Success</AlertTitle>
                      <AlertDescription className="text-green-700">
                        Your password has been updated successfully.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  {resetPasswordError && (
                    <Alert className="mb-6 bg-red-50 border-red-200">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <AlertTitle className="text-red-600">Error</AlertTitle>
                      <AlertDescription className="text-red-700">
                        {resetPasswordError}
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <Form {...securityForm}>
                    <form
                      onSubmit={securityForm.handleSubmit(onSecuritySubmit)}
                      className="space-y-6"
                    >
                      <FormField
                        control={securityForm.control}
                        name="oldPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Current Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type={showOldPassword ? "text" : "password"}
                                  className="pl-10 pr-10"
                                  placeholder="Enter your current password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  className="absolute right-3 top-3 text-muted-foreground"
                                  onClick={() => setShowOldPassword(!showOldPassword)}
                                >
                                  {showOldPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={securityForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>New Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type={showNewPassword ? "text" : "password"}
                                  className="pl-10 pr-10"
                                  placeholder="Enter your new password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  className="absolute right-3 top-3 text-muted-foreground"
                                  onClick={() => setShowNewPassword(!showNewPassword)}
                                >
                                  {showNewPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormDescription>
                              Password must be at least 8 characters.
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={securityForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm New Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type={showConfirmPassword ? "text" : "password"}
                                  className="pl-10 pr-10"
                                  placeholder="Confirm your new password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  className="absolute right-3 top-3 text-muted-foreground"
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                  {showConfirmPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <div className="flex justify-end">
                        <Button type="submit" disabled={isSubmitting}>
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Updating...
                            </>
                          ) : (
                            "Update Password"
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}