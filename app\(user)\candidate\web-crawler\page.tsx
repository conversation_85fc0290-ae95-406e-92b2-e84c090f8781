'use client';

import { useState, useEffect, useMemo } from 'react';
import { format, isWithinInterval, startOfDay, endOfDay } from 'date-fns';
import { useUser } from '@/hooks/useUser';
import { getDailyJobs, trackFileDownload } from '@/actions/admin/web-crawler';
import { getCurrentUserSubscription } from '@/actions/subscriptions';
import type { DailyJob } from '@/types/crawler';
import type { Subscription } from '@/types/subscription';
import type { DateRange as CalendarDateRange } from 'react-day-picker';
import {
  CalendarIcon,
  Download,
  Loader2,
  X,
  ChevronDown,
  Lock,
  UserPlus,
  Crown,
  Sparkles,
  Check,
  FileText,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

interface DateRange {
  from: Date | undefined;
  to: Date | undefined;
}

export default function WebCrawlerPage() {
  const router = useRouter();
  const { user, loading: userLoading } = useUser();
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined
  });
  const [fileType, setFileType] = useState('all');
  const [jobs, setJobs] = useState<DailyJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [displayCount, setDisplayCount] = useState(20);
  const [activeSubscription, setActiveSubscription] =
    useState<Subscription | null>(null);
  const [subscriptionLoading, setSubscriptionLoading] = useState(true);
  const [showSubscriptionDialog, setShowSubscriptionDialog] = useState(false);

  // Simplified access check to match daily-job page
  const isVipUser = useMemo(() => user?.vip || false, [user]);

  useEffect(() => {
    const loadJobs = async () => {
      try {
        const data = await getDailyJobs();
        setJobs(data);
      } catch (error) {
        console.error('Error loading jobs:', error);
        toast({
          title: 'Error',
          description: 'Failed to load jobs',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    loadJobs();
  }, []);

  // Load user's subscription status
  useEffect(() => {
    const checkSubscription = async () => {
      if (!user) {
        setSubscriptionLoading(false);
        return;
      }

      try {
        // VIP users don't need to check subscription - they already have access
        if (user.vip) {
          setActiveSubscription({
            id: `vip-${user.id}`,
            subscription_id: `vip-${user.id}`,
            owner_id: user.id,
            owner_email: user.email || '',
            status: 'active',
            plan: 'vip',
            start_date: new Date().toISOString(),
            end_date: null,
            creation_date: new Date().toISOString(),
            modified_date: new Date().toISOString(),
            cancelable_date: null,
            creator: user.id,
            slug: `vip-${user.id}`
          });
          setSubscriptionLoading(false);
          return;
        }

        const subscription = await getCurrentUserSubscription(user.id);

        // Subscription was returned only if it's active/trialing and end_date is valid
        // So we can simply use it as-is
        setActiveSubscription(subscription);
      } catch (error) {
        // Handle error silently
      } finally {
        setSubscriptionLoading(false);
      }
    };

    checkSubscription();
  }, [user]);

  // Get most recent jobs by type for non-VIP users - matching daily-job logic
  const getMostRecentJobsByType = () => {
    if (!jobs.length) return [];

    // Create a map to store the most recent job of each type
    const mostRecentJobs = new Map();

    // Find the most recent job of each type
    jobs.forEach((job) => {
      const type = job.file_type || 'Daily Jobs';
      if (
        !mostRecentJobs.has(type) ||
        (job.posting_date &&
          mostRecentJobs.get(type).posting_date &&
          new Date(job.posting_date) >
            new Date(mostRecentJobs.get(type).posting_date))
      ) {
        mostRecentJobs.set(type, job);
      }
    });

    // Return only Daily Jobs and Smart Search types
    const result = [];
    if (mostRecentJobs.has('Daily Jobs')) {
      result.push(mostRecentJobs.get('Daily Jobs'));
    }
    if (mostRecentJobs.has('Smart Search')) {
      result.push(mostRecentJobs.get('Smart Search'));
    }

    return result;
  };

  // Check if a job is accessible by current user
  const isJobAccessible = (job: DailyJob) => {
    if (isVipUser) return true;
    if (!user) return false;

    // Non-VIP users can only access the most recent Daily Job and Smart Search
    const recentJobs = getMostRecentJobsByType();
    return recentJobs.some((recentJob) => recentJob.id === job.id);
  };

  // Match the filtering logic from daily-job page
  const getFilteredJobs = () => {
    // For VIP users, show all jobs with filters applied
    if (isVipUser) {
      return jobs.filter((job) => {
        let matchesType = true;
        if (fileType !== 'all') {
          if (fileType === 'daily') {
            matchesType = !job.file_type || job.file_type === 'Daily Jobs';
          } else if (fileType === 'smart') {
            matchesType = job.file_type === 'Smart Search';
          } else if (fileType === 'weekly') {
            matchesType = job.file_type === 'Weekly Referral';
          }
        }

        if (dateRange.from && job.posting_date) {
          const jobDate = startOfDay(new Date(job.posting_date));
          const fromDate = startOfDay(dateRange.from as Date);
          const toDate = dateRange.to
            ? endOfDay(dateRange.to as Date)
            : endOfDay(dateRange.from as Date);

          return (
            matchesType &&
            isWithinInterval(jobDate, { start: fromDate, end: toDate })
          );
        }

        return matchesType;
      });
    }

    // For non-VIP users, only show the most recent Daily Job and Smart Search
    if (user && !isVipUser) {
      const recentJobs = getMostRecentJobsByType();

      // Apply file type filter
      if (fileType !== 'all') {
        if (fileType === 'daily') {
          return recentJobs.filter(
            (job) => !job.file_type || job.file_type === 'Daily Jobs'
          );
        } else if (fileType === 'smart') {
          return recentJobs.filter((job) => job.file_type === 'Smart Search');
        } else {
          return []; // No weekly listings for non-VIP
        }
      }

      // Apply date filter if it exists
      if (dateRange.from) {
        return recentJobs.filter((job) => {
          if (!job.posting_date) return false;

          const jobDate = startOfDay(new Date(job.posting_date));
          const fromDate = startOfDay(dateRange.from as Date);
          const toDate = dateRange.to
            ? endOfDay(dateRange.to as Date)
            : endOfDay(dateRange.from as Date);

          return isWithinInterval(jobDate, { start: fromDate, end: toDate });
        });
      }

      return recentJobs;
    }

    // For non-registered users, show no jobs
    return [];
  };

  const filteredJobs = getFilteredJobs();

  const handleDownload = async (job: DailyJob) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to download files',
        variant: 'destructive'
      });
      return;
    }

    if (!isJobAccessible(job)) {
      setShowSubscriptionDialog(true);
      return;
    }

    const downloadList = user.vip
      ? job.downloader_vip || []
      : job.downloader_nonvip || [];

    if (downloadList.includes(user.id)) {
      toast({
        title: 'Already Downloaded',
        description: 'You have already downloaded this file',
        variant: 'default'
      });
      return;
    }

    if (job.crawler_file) {
      try {
        const result = await trackFileDownload(job.id, user.id, !!user.vip);

        if (result.success) {
          // Update the local job object to reflect the download
          const updatedJobs = jobs.map((j) => {
            if (j.id === job.id) {
              if (user.vip) {
                return {
                  ...j,
                  downloader_vip: [...(j.downloader_vip || []), user.id]
                };
              } else {
                return {
                  ...j,
                  downloader_nonvip: [...(j.downloader_nonvip || []), user.id]
                };
              }
            }
            return j;
          });

          setJobs(updatedJobs);
        }

        window.open(job.crawler_file, '_blank');

        toast({
          title: 'Download Started',
          description: 'Your file download has started in a new tab',
          variant: 'default'
        });
      } catch (error) {
        console.error('Error during download:', error);
        toast({
          title: 'Download Error',
          description: 'There was an error processing your download',
          variant: 'destructive'
        });
      }
    } else {
      toast({
        title: 'File Unavailable',
        description: 'This file is currently unavailable for download',
        variant: 'destructive'
      });
    }
  };

  const formatDateRange = (range: DateRange) => {
    if (!range.from) return 'Pick a date';
    if (!range.to) return format(range.from, 'PPP');
    return `${format(range.from, 'PPP')} - ${format(range.to, 'PPP')}`;
  };

  const handleSubscribe = () => {
    // Navigate to subscription page
    window.location.href = '/membership';
  };

  const viewSample = () => {
    window.open(
      'https://docs.google.com/spreadsheets/d/1UExo3skYgVCbPfM0AQq7_nuCO2b7qHTe/edit?gid=1516982664#gid=1516982664',
      '_blank'
    );
  };

  const redirectToSignup = () => {
    router.push('/signup');
  };

  if (loading || userLoading || subscriptionLoading) {
    return (
      <div className="container mx-auto py-12 flex justify-center items-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Latest Job Postings Tracker
        </h1>
        <p className="text-muted-foreground max-w-3xl">
          InterUp leverages web scraping technology to gather the latest job
          postings in popular fields such as Software Development, Analyst,
          Designer, Finance, Accounting, and Marketing.
          {!isVipUser &&
            ' Upgrade to VIP for unlimited access to all historical job listings.'}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* For all users: Sample view button */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">View Sample</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-2">
              Preview a sample job listings file
            </p>
            <Button variant="outline" className="w-full" onClick={viewSample}>
              View Sample
            </Button>
          </CardContent>
        </Card>

        {/* For VIP users: Status badge */}
        {isVipUser && (
          <Card className="bg-primary/5 border-primary/20 md:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">VIP Status</CardTitle>
              <Crown className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="default" className="bg-primary">
                  Active
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Full access to all job listings
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                You have unlimited access to all {jobs.length} job listings
                including archives
              </p>
            </CardContent>
          </Card>
        )}

        {/* For non-VIP users: Upgrade card */}
        {user && !isVipUser && (
          <Card className="bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200 overflow-hidden relative md:col-span-2">
            <div className="absolute top-0 right-0 p-1.5">
              <span className="inline-flex items-center rounded-sm bg-amber-100 px-1.5 py-0.5 text-xs font-medium text-amber-800 ring-1 ring-inset ring-amber-200">
                Recommended
              </span>
            </div>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-amber-800">
                <Crown className="h-5 w-5 text-amber-500" />
                Upgrade to VIP for Full Access
              </CardTitle>
              <CardDescription className="text-amber-700">
                Get unlimited access to all job listings including historical
                data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-amber-900">
                    Free Account (Current)
                  </h4>
                  <ul className="space-y-1 text-sm text-amber-700">
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Access to newest Daily
                      Jobs only
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Most recent Smart
                      Search only
                    </li>
                    <li className="flex items-center opacity-50">
                      <X className="mr-2 h-4 w-4" /> No access to archives
                    </li>
                    <li className="flex items-center opacity-50">
                      <X className="mr-2 h-4 w-4" /> No Weekly Referral access
                    </li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-amber-900 flex items-center">
                    VIP Account{' '}
                    <span className="ml-2 text-xs bg-amber-200 text-amber-800 px-2 py-0.5 rounded-full">
                      $0.99/mo
                    </span>
                  </h4>
                  <ul className="space-y-1 text-sm text-amber-700">
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Complete access to all
                      job lists
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Full archive access
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Weekly Referral job
                      listings
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Priority support
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row items-center gap-4 sm:justify-between bg-gradient-to-r from-amber-100/50 to-amber-200/50 px-6 py-4">
              <div className="flex items-center">
                <Sparkles className="h-5 w-5 text-amber-500 mr-2" />
                <span className="text-amber-800 text-sm">
                  Unlock all {jobs.length} job listings
                </span>
              </div>
              <Button
                onClick={handleSubscribe}
                className="bg-amber-600 hover:bg-amber-700 w-full sm:w-auto"
              >
                <Crown className="mr-2 h-4 w-4" />
                Upgrade to VIP
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-muted-foreground" />
            Job Listings
          </CardTitle>
          <CardDescription>
            Browse and download daily job listings
            {user && !isVipUser && (
              <span className="block mt-1 text-amber-600 font-medium text-sm">
                Free accounts can only access the most recent Daily Job and
                Smart Search
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !dateRange.from && 'text-muted-foreground',
                      !user && 'opacity-50 cursor-not-allowed'
                    )}
                    disabled={!user}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formatDateRange(dateRange)}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(value: CalendarDateRange | undefined) => {
                      setDateRange({
                        from: value?.from,
                        to: value?.to
                      });
                    }}
                    numberOfMonths={2}
                    className="[&_.rdp-day_focus]:bg-[#118073] [&_.rdp-day_hover]:bg-[#118073]/10"
                  />
                </PopoverContent>
              </Popover>
              {(dateRange.from || dateRange.to) && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={() =>
                    setDateRange({ from: undefined, to: undefined })
                  }
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Clear date range</span>
                </Button>
              )}
            </div>
            <Select
              value={fileType}
              onValueChange={setFileType}
              disabled={!user}
            >
              <SelectTrigger
                className={cn(
                  'w-full md:w-[200px]',
                  !user && 'opacity-50 cursor-not-allowed'
                )}
              >
                <SelectValue placeholder="Select file type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="daily">Daily Jobs</SelectItem>
                <SelectItem value="smart">Smart Search</SelectItem>
                <SelectItem value="weekly">Weekly Referral</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {!user ? (
            <div className="rounded-md border bg-muted/30 p-6 relative">
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-background/80 backdrop-blur-[1px] z-10">
                <div className="text-center max-w-md space-y-4 p-6">
                  <UserPlus className="h-10 w-10 text-primary mx-auto opacity-80" />
                  <h3 className="text-xl font-semibold">
                    Sign up to view jobs
                  </h3>
                  <p className="text-muted-foreground">
                    Create an account to browse our daily curated job listings
                    and access more features
                  </p>
                  <Button className="mt-2" onClick={redirectToSignup}>
                    Sign up now
                  </Button>
                </div>
              </div>
              <Table>
                <TableHeader>
                  <TableRow className="opacity-40">
                    <TableHead>File Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Access</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {jobs.slice(0, 2).map((job) => (
                    <TableRow key={job.id} className="opacity-40">
                      <TableCell>
                        <Badge variant="outline" className="font-normal">
                          {job.file_type || 'Daily Jobs'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {job.posting_date
                          ? format(new Date(job.posting_date), 'PPP')
                          : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-primary">
                          Sample
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" disabled={true}>
                          <Download className="h-4 w-4" />
                          <span className="sr-only">Download file</span>
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>File Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Access</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredJobs.length > 0 ? (
                    <>
                      {filteredJobs.slice(0, displayCount).map((job) => (
                        <TableRow
                          key={job.id}
                          className={cn(
                            'cursor-pointer',
                            isJobAccessible(job)
                              ? 'hover:bg-muted/50'
                              : 'hover:bg-amber-50/30'
                          )}
                        >
                          <TableCell>
                            <Badge
                              variant="outline"
                              className="font-normal border-[#118073] text-[#118073]"
                            >
                              {job.file_type || 'Daily Jobs'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {job.posting_date
                              ? format(new Date(job.posting_date), 'PPP')
                              : 'N/A'}
                          </TableCell>
                          <TableCell>
                            {isVipUser ? (
                              <Badge variant="default" className="bg-primary">
                                VIP
                              </Badge>
                            ) : isJobAccessible(job) ? (
                              <Badge variant="default" className="bg-primary">
                                Free
                              </Badge>
                            ) : (
                              <Badge
                                variant="secondary"
                                className="flex items-center"
                              >
                                <Crown className="h-3 w-3 mr-1 text-amber-500" />
                                VIP Only
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                isJobAccessible(job)
                                  ? handleDownload(job)
                                  : handleSubscribe()
                              }
                              disabled={!job.crawler_file}
                              className="hover:text-[#118073]"
                            >
                              <Download className="h-4 w-4" />
                              <span className="sr-only">Download file</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {filteredJobs.length > displayCount && (
                        <TableRow>
                          <TableCell colSpan={4} className="text-center p-4">
                            <Button
                              variant="outline"
                              className="w-full max-w-sm hover:bg-[#118073] hover:text-white border-[#118073] text-[#118073]"
                              onClick={() =>
                                setDisplayCount((prev) => prev + 20)
                              }
                            >
                              <ChevronDown className="h-4 w-4 mr-2" />
                              View More
                              <span className="ml-2 text-xs text-muted-foreground">
                                ({filteredJobs.length - displayCount} remaining)
                              </span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      )}
                    </>
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={4}
                        className="text-center py-6 text-muted-foreground"
                      >
                        No files found for the selected filters
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Subscription Dialog */}
      <Dialog
        open={showSubscriptionDialog}
        onOpenChange={setShowSubscriptionDialog}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>VIP Access Required</DialogTitle>
            <DialogDescription>
              Only VIP users can access older job listings. Free users can only
              access the most recent listings.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-4">
              Upgrade to VIP to unlock all historical job listings and gain an
              edge in your job search.
            </p>
            <div className="flex flex-col space-y-2">
              <div className="flex items-center">
                <Check className="h-4 w-4 text-[#118073] mr-2" />
                <span>Complete access to all job lists</span>
              </div>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-[#118073] mr-2" />
                <span>Full archive access</span>
              </div>
              <div className="flex items-center">
                <Check className="h-4 w-4 text-[#118073] mr-2" />
                <span>Weekly Referral job listings</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowSubscriptionDialog(false)}
            >
              Close
            </Button>
            <Button
              className="bg-amber-600 hover:bg-amber-700"
              onClick={handleSubscribe}
            >
              Upgrade to VIP
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
