'use client';
import Footer from '@/components/footer-primary';
import CircularNavigation from '@/components/navigation';
import { marketingConfig } from '@/config/marketing';
import { useUser } from '@/hooks/useUser';

export default function MembershipLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const { user } = useUser();
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <CircularNavigation
        items={marketingConfig.mainNav}
        user={user ? true : false}
      />

      {/* Main content */}
      <main className="lg: py-8">{children}</main>
      <Footer />
    </div>
  );
}
