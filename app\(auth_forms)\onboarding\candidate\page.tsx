'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useUser } from '@/hooks/useUser';

export default function OnboardingPage() {
  const router = useRouter();
  const { user, loading } = useUser();

  // Redirect if user already has completed profile
  useEffect(() => {
    if (
      !loading &&
      user &&
      user.first_name &&
      user.last_name &&
      user.user_type?.toLowerCase() === 'candidate'
    ) {
      // Check if user is coming to onboarding specifically (allow manual navigation)
      const isDirectNavigation = document.referrer.includes('/onboarding');

      if (!isDirectNavigation) {
        router.push('/candidate');
      }
    }
  }, [user, loading, router]);

  const handleStartOnboarding = () => {
    router.push('/onboarding/candidate/personal-info');
  };

  return (
    <div className="flex min-h-screen bg-white">
      {/* Left side with brand content */}
      <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center pr-12">
        <div className="flex flex-col items-center gap-8">
          <div className="flex items-center space-x-2">
            <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
              <Image src="/favicon_new.png" alt="logo" width={25} height={25} />
            </div>
            <span className="text-lg md:text-xl font-extrabold tracking-tightest">
              InternUp
            </span>
          </div>
          <div className="flex flex-col items-center gap-2 text-center">
            <h2 className="text-xl font-semibold">Connecting</h2>
            <h2 className="text-xl font-semibold">
              International <span className="text-[#36BA98]">Talent</span>
            </h2>
            <h2 className="text-xl font-semibold">
              with <span className="text-[#36BA98]">Opportunities</span>
            </h2>
          </div>
        </div>
      </div>

      {/* Right side with content */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 flex flex-col justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="lg:hidden mb-8">
            <Image
              src="/internup-logo.svg"
              alt="InternUp Logo"
              width={120}
              height={120}
              className="mx-auto"
            />
          </div>

          <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent mb-4">
            Complete Your Profile
          </h1>
          <p className="text-gray-600 mb-8">
            You're about to start the onboarding process. We'll ask you for some
            information to help connect you with the right opportunities. This
            will only take a few minutes.
          </p>

          <div className="space-y-4">
            <Button
              onClick={handleStartOnboarding}
              className="w-full bg-[#36BA98] hover:bg-[#2da888] text-white py-2 rounded-md"
            >
              Begin Onboarding
            </Button>

            <p className="text-sm text-gray-500">
              Already have an account?{' '}
              <Link href="/signin" className="text-[#36BA98] hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
