# Authentication Flow Documentation

## Overview

This document outlines the authentication flow for the InternUp application, which uses a hybrid authentication system combining Bubble for standard authentication (email/password) and Supabase for OAuth integration. The system is designed to eventually transition all authentication data from Bubble to Supabase.

## Table of Contents

1. [Architecture](#architecture)
2. [Sign Up Process](#sign-up-process)
3. [Sign In Process](#sign-in-process)
4. [OAuth Authentication](#oauth-authentication)
5. [Email Verification](#email-verification)
6. [Forgot Password](#forgot-password)
7. [Authentication Utilities](#authentication-utilities)
8. [Data Flow Between Bubble and Supabase](#data-flow-between-bubble-and-supabase)

## Architecture

The authentication system uses a dual-database approach:

- **Bubble**: Handles traditional email/password authentication, storing user credentials and generating authentication tokens.
- **Supabase**: Manages OAuth authentication (Google, LinkedIn, etc.) and stores additional user data.

This hybrid approach allows for a gradual migration from Bubble to Supabase while maintaining backward compatibility with existing Bubble users.

### Key Components

- **BubbleClient**: A utility class that interfaces with the Bubble API for authentication operations.
- **Supabase Client**: Handles OAuth authentication and user data storage.
- **Authentication Middleware**: Ensures authenticated routes are protected.
- **User Context**: Provides user information throughout the application.

## Sign Up Process

### Email/Password Sign Up

1. User submits the sign-up form with email, password, and user type (Candidate or Company).
2. The application calls `signUp()` function in `utils/bubble/auth.ts`.
3. The function:
   - Registers the user in Bubble using `bubbleClient.signUp()`
   - Stores the Bubble authentication token and user ID
   - Generates a 6-digit verification code
   - Creates a corresponding user record in Supabase with the Bubble user ID
   - Sends a verification email with the code
4. User is prompted to enter the verification code.

### Code Example

```typescript
// From utils/bubble/auth.ts
export async function signUp(formData: FormData) {
  try {
    const email = String(formData.get('email')).trim();
    const password = String(formData.get('password')).trim();
    const user_type = String(formData.get('userType')).trim();

    // First sign up with Bubble
    const bubbleResponse: BubbleAuthResponse = await bubbleClient.signUp(
      email,
      password,
      user_type
    );

    if (bubbleResponse.status === 'success') {
      // Store tokens from Bubble response
      const { token, user_id } = bubbleResponse.response;
      setTokens(token, user_id);

      // Generate verification code and create user in Supabase
      // Send verification email
      // ...
    }
  } catch (error) {
    // Error handling
  }
}
```

## Sign In Process

### Email/Password Sign In

1. User submits the sign-in form with email and password.
2. The application calls `signInWithPassword()` function in `utils/bubble/auth.ts`.
3. The function:
   - Authenticates the user with Bubble using `bubbleClient.signIn()`
   - Stores the Bubble authentication token and user ID
   - Retrieves the user's type from Supabase
   - Redirects the user to the appropriate dashboard based on user type
4. If the user hasn't verified their email, they are prompted to do so.

### Code Example

```typescript
// From utils/bubble/auth.ts
export async function signInWithPassword(formData: FormData) {
  try {
    const email = String(formData.get('email')).trim();
    const password = String(formData.get('password')).trim();

    // Authenticate with Bubble
    const response = await bubbleClient.signIn(email, password);

    if (response.status === 'success') {
      // Store tokens
      const { token, user_id } = response.response;
      setTokens(token, user_id);

      // Get user type from Supabase
      // Determine redirect path
      // ...
    }
  } catch (error) {
    // Error handling
  }
}
```

## OAuth Authentication

### OAuth Sign Up/Sign In

1. User clicks on an OAuth provider button (Google, LinkedIn, etc.).
2. The application calls either `signUpWithOAuth()` or `signInWithOAuth()` function.
3. The function:
   - Stores the selected user type in localStorage
   - Initiates the OAuth flow with Supabase
   - Redirects to the provider's authentication page
4. After successful authentication, the provider redirects back to the application's callback URL.
5. The callback handler (`auth-callback-handler.tsx`):
   - Exchanges the code for a session
   - Checks if the user exists in Supabase
   - For new users, creates a Bubble account with a random password
   - Updates or creates the user record in Supabase
   - Sets authentication tokens
   - Redirects to the appropriate dashboard

### Code Example

```typescript
// From utils/bubble/auth.ts
export async function signUpWithOAuth(provider: string, userType: string) {
  try {
    const supabase = createClient();
    const redirectURL = new URL(
      'api/auth/callback',
      window.location.origin
    ).toString();

    // Store user type in localStorage before redirect
    localStorage.setItem('oauth_user_type', userType);

    // Initiate OAuth flow with Supabase
    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider as Provider,
      options: {
        redirectTo: redirectURL,
        queryParams: {
          user_type: userType,
          flow: 'signup'
        }
        // ...
      }
    });

    // Error handling
  } catch (error) {
    // Error handling
  }
}
```

## Email Verification

1. After sign-up, a 6-digit verification code is sent to the user's email.
2. User enters the code in the verification form.
3. The application calls `verifyEmail()` function in `utils/bubble/auth.ts`.
4. The function:
   - Retrieves the user's verification code from Supabase
   - Validates the code and checks if it's expired
   - Updates the user's verification status in Supabase
   - For company users, creates a company entity if needed
   - Redirects to the appropriate dashboard

### Code Example

```typescript
// From utils/bubble/auth.ts
export async function verifyEmail(email: string, otp: string) {
  try {
    // Get user data from Supabase to check verification code
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select(
        'verification_code, verification_expiration, user_type, startup_company'
      )
      .eq('email', email)
      .single();

    // Validate verification code
    // Update verification status
    // Handle company creation if needed
    // ...
  } catch (error) {
    // Error handling
  }
}
```

## Forgot Password

1. User enters their email in the forgot password form.
2. The application calls `forgotPassword()` function in `utils/bubble/auth.ts`.
3. The function:
   - Calls the Bubble API to initiate a password reset
   - Bubble sends a password reset email to the user
4. User receives an email with instructions to reset their password.
5. After resetting the password, the user can sign in with the new credentials.

### Code Example

```typescript
// From utils/bubble/auth.ts
export async function forgotPassword(email: string) {
  try {
    // Call the Bubble API to initiate password reset
    const response = await bubbleClient.forgotPassword(email);

    return {
      success: true,
      data: response,
      message:
        'Password reset email sent successfully. Please check your inbox.'
    };
  } catch (error) {
    // Error handling
  }
}
```

## Authentication Utilities

### Token Management

- **setTokens()**: Stores Bubble authentication tokens in localStorage.
- **getTokens()**: Retrieves Bubble authentication tokens from localStorage.

### User Context

The application provides a `useUser()` hook that:

- Fetches the current user's data
- Provides loading and authentication states
- Makes user data available throughout the application

## Data Flow Between Bubble and Supabase

### User Creation

1. When a user signs up with email/password:

   - User is created in Bubble first
   - A corresponding record is created in Supabase with the Bubble user ID

2. When a user signs up with OAuth:
   - User is authenticated through Supabase
   - A corresponding account is created in Bubble with a random password

### Authentication State

- Bubble authentication state is managed through tokens stored in localStorage
- Supabase authentication state is managed through cookies
- The application checks both systems to determine if a user is authenticated

### Future Migration Path

The current hybrid system is designed to facilitate a gradual migration from Bubble to Supabase:

1. All new users are created in both systems
2. Authentication can happen through either system
3. As the migration progresses, more authentication functionality will be moved to Supabase
4. Eventually, all authentication will be handled by Supabase, with Bubble being phased out

## Security Considerations

- OAuth users are automatically verified (no email verification required)
- Email/password users must verify their email before accessing protected routes
- Authentication tokens are stored securely in localStorage and cookies
- Password reset is handled through secure email links
- Random strong passwords are generated for OAuth users in Bubble

---

This documentation provides an overview of the authentication flow. For detailed implementation, refer to the source code in the following files:

- `utils/bubble/auth.ts`: Main authentication functions
- `utils/bubble/client.ts`: Bubble API client
- `app/(auth_forms)/signup/page.tsx`: Sign-up form
- `app/(auth_forms)/signin/page.tsx`: Sign-in form
- `app/(auth_forms)/verify/page.tsx`: Email verification form
- `app/(auth_forms)/forgot-password/page.tsx`: Forgot password form
- `app/auth/callback/auth-callback-handler.tsx`: OAuth callback handler
