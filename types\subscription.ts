export type SubscriptionStatus =
  | 'active'
  | 'canceled'
  | 'past_due'
  | 'unpaid'
  | 'incomplete'
  | 'incomplete_expired'
  | 'trialing';

export interface Subscription {
  id: string;
  creator: string;
  owner_id: string;
  owner_email: string;
  plan: string;
  status: SubscriptionStatus;
  start_date: string;
  end_date: string | null;
  cancelable_date: string | null;
  subscription_id: string;
  creation_date: string;
  modified_date: string;
  slug: string;
  bubble_id?: string;
}

export interface CreateSubscriptionInput {
  owner_id: string;
  owner_email: string;
  plan: string;
  status: SubscriptionStatus;
  start_date: string;
  end_date?: string | null;
  cancelable_date?: string | null;
  subscription_id: string;
  slug: string;
  bubble_id?: string;
}

export interface UpdateSubscriptionInput {
  id: string;
  owner_id: string;
  owner_email: string;
  status?: SubscriptionStatus;
  end_date?: string | null;
  cancelable_date?: string | null;
  plan?: string;
}
