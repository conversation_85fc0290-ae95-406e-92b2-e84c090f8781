'use client';

import * as React from 'react';
import { Search, Loader2, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ReferralDetailView } from '@/components/insider-portal/ReferralDetailView';
import { useUser } from '@/hooks/useUser';
import {
  getInsiderReferralStats,
  getInsiderReferrals,
  updateReferralStatus,
  markReferralAsReviewed
} from '@/actions/job-market/publicFirm-referrals';
import { getInsider } from '@/actions/admin/insider';
import { toast } from '@/components/ui/use-toast';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { InsiderReferralResponse } from '@/types/referral';
import { Insider } from '@/types/insider';
import { bubbleClient } from '@/utils/bubble/client';

export default function ReferralRequestsPage() {
  const [loading, setLoading] = React.useState(true);
  const [referrals, setReferrals] = React.useState<InsiderReferralResponse[]>(
    []
  );
  const [searchQuery, setSearchQuery] = React.useState('');
  const [statusFilter, setStatusFilter] = React.useState<string>('all');
  const [stats, setStats] = React.useState({
    pending: 0,
    reviewed: 0,
    accepted: 0,
    rejected: 0,
    total: 0
  });
  const { user } = useUser();
  const [selectedReferral, setSelectedReferral] =
    React.useState<InsiderReferralResponse | null>(null);
  const [notes, setNotes] = React.useState('');
  const [actionDialogOpen, setActionDialogOpen] = React.useState(false);
  const [currentAction, setCurrentAction] = React.useState<
    'Reviewed' | 'Accepted' | 'Rejected'
  >('Accepted');
  const [reviewDialogOpen, setReviewDialogOpen] = React.useState(false);

  // First, fetch the insider data using user ID
  const [insiderData, setInsiderData] = React.useState<Insider | null>(null);

  React.useEffect(() => {
    async function fetchInsiderData() {
      try {
        if (!user?.id) {
          setLoading(false);
          return;
        }

        // Fetch insider data using the getInsider function
        const insider = await getInsider(user.id);

        if (insider && insider.id) {
          setInsiderData(insider);
        } else {
          console.error('No insider profile found for this user');
          toast({
            title: 'Warning',
            description: 'Could not find insider profile for your account',
            variant: 'destructive'
          });
          setLoading(false);
        }
      } catch (error) {
        console.error('Error fetching insider profile:', error);
        toast({
          title: 'Error',
          description: 'Failed to load insider profile',
          variant: 'destructive'
        });
        setLoading(false);
      }
    }

    fetchInsiderData();
  }, [user]);

  // Fetch referral data using insider ID
  React.useEffect(() => {
    async function fetchReferrals() {
      try {
        if (!insiderData?.id) {
          return;
        }

        // Fetch stats using insider ID
        const statsData = await getInsiderReferralStats(insiderData.id);
        setStats(statsData);

        // Fetch referrals using insider ID
        const referralsData = await getInsiderReferrals(
          insiderData.id,
          statusFilter !== 'all' ? statusFilter : undefined
        );
        setReferrals(referralsData);
      } catch (error) {
        console.error('Error fetching referrals:', error);
        toast({
          title: 'Error',
          description: 'Failed to load referral requests',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    }

    if (insiderData?.id) {
      fetchReferrals();
    }
  }, [insiderData, statusFilter]);

  // Filter referrals based on search query
  const filteredReferrals = React.useMemo(() => {
    return referrals.filter((referral) => {
      return (
        referral.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()) ||
        referral.candidateName
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        referral.companyName.toLowerCase().includes(searchQuery.toLowerCase())
      );
    });
  }, [referrals, searchQuery]);

  const handleStatusChange = async (
    action: 'Reviewed' | 'Accepted' | 'Rejected'
  ) => {
    if (!selectedReferral || !insiderData) return;

    if (action === 'Reviewed') {
      setReviewDialogOpen(false);
    } else {
      setActionDialogOpen(false);
    }

    try {
      if (action === 'Reviewed') {
        // Mark as reviewed
        await markReferralAsReviewed(
          selectedReferral.id,
          insiderData.id,
          notes
        );

        // Update local state
        setReferrals((prev) =>
          prev.map((ref) =>
            ref.id === selectedReferral.id
              ? { ...ref, status: 'reviewed' }
              : ref
          )
        );

        // Update stats
        setStats((prev) => ({
          ...prev,
          pending: prev.pending - 1,
          reviewed: prev.reviewed + 1
        }));

        toast({
          title: 'Success',
          description:
            'Referral marked as reviewed successfully. You earned $1 for reviewing. You can now accept or reject this referral.'
        });
      } else {
        // Accept or reject
        const status = action === 'Accepted' ? 'accepted' : 'rejected';
        await updateReferralStatus(selectedReferral.id, status, notes);

        // Update local state
        setReferrals((prev) =>
          prev.map((ref) =>
            ref.id === selectedReferral.id ? { ...ref, status } : ref
          )
        );

        // Update stats based on previous status
        if (selectedReferral.status === 'assigned') {
          setStats((prev) => ({
            ...prev,
            pending: prev.pending - 1,
            [action === 'Accepted' ? 'accepted' : 'rejected']:
              prev[action === 'Accepted' ? 'accepted' : 'rejected'] + 1
          }));
        } else if (selectedReferral.status === 'reviewed') {
          setStats((prev) => ({
            ...prev,
            reviewed: prev.reviewed - 1,
            [action === 'Accepted' ? 'accepted' : 'rejected']:
              prev[action === 'Accepted' ? 'accepted' : 'rejected'] + 1
          }));

          // Show additional message for accepted referrals after review
          if (action === 'Accepted') {
            toast({
              title: 'Bonus Earned',
              description:
                'You earned an additional $2 for accepting this referral after review.'
            });
          }
        }

        toast({
          title: 'Success',
          description: `Referral ${action === 'Accepted' ? 'accepted' : 'rejected'} successfully`
        });

        // --- BEGIN ADDED EMAIL LOGIC ---
        if (selectedReferral.candidateEmail) {
          const emailSubject = `Update on your referral request for ${selectedReferral.jobTitle} at ${selectedReferral.companyName}`;
          let emailBody = '';

          if (action === 'Accepted') {
            emailBody = `Dear ${selectedReferral.candidateName},

We are pleased to inform you that your referral request for the ${selectedReferral.jobTitle} position at ${selectedReferral.companyName} has been accepted by our insider.

${notes ? `Insider notes: ${notes}\n\n` : ''}We will proceed with submitting your application. You will be notified of further steps.

Best regards,
InternUp Team`;
          } else if (action === 'Rejected') {
            emailBody = `Dear ${selectedReferral.candidateName},

We regret to inform you that your referral request for the ${selectedReferral.jobTitle} position at ${selectedReferral.companyName} has been declined by our insider.

${notes ? `Reason provided: ${notes}\n\n` : 'Unfortunately, no specific reason was provided.\n\n'}We encourage you to explore other opportunities on InternUp.

Best regards,
InternUp Team`;
          }

          if (emailBody) {
            try {
              await bubbleClient.sendEmail(
                selectedReferral.candidateEmail,
                emailSubject,
                emailBody
              );
            } catch (emailError) {
              console.error('Failed to send email notification:', emailError);
              // Optionally, add a toast notification for email failure
              toast({
                title: 'Email Warning',
                description:
                  'Failed to send notification email to the candidate.',
                variant: 'destructive' // Or 'warning'
              });
            }
          }
        } else {
          console.warn(
            'Cannot send email notification: Candidate email is missing for referral ID:',
            selectedReferral.id
          );
        }
        // --- END ADDED EMAIL LOGIC ---
      }

      setNotes('');
      setSelectedReferral(null);
    } catch (error) {
      console.error(`Error processing referral action (${action}):`, error);
      toast({
        title: 'Error',
        description: `Failed to process referral action`,
        variant: 'destructive'
      });
    }
  };

  const openActionDialog = (
    referral: InsiderReferralResponse,
    action: 'Reviewed' | 'Accepted' | 'Rejected'
  ) => {
    setSelectedReferral(referral);
    setCurrentAction(action);

    if (action === 'Reviewed') {
      setReviewDialogOpen(true);
    } else {
      setActionDialogOpen(true);
    }
  };

  const handleReviewFromDetail = (referral: InsiderReferralResponse) => {
    openActionDialog(referral, 'Reviewed');
  };

  const handleApproveFromDetail = (referral: InsiderReferralResponse) => {
    openActionDialog(referral, 'Accepted');
  };

  const handleDeclineFromDetail = (referral: InsiderReferralResponse) => {
    openActionDialog(referral, 'Rejected');
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-[#118073]" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Referral Requests</h1>
        <p className="text-muted-foreground">
          Review and process candidate referral requests
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-5">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Referrals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Pending
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pending}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Reviewed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.reviewed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Accepted
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.accepted}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Rejected
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rejected}</div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Referral Requests</CardTitle>
          <CardDescription>
            Review applications first before accepting or rejecting referral
            requests.
            <br />
            <strong>Note:</strong> You must click the 'Review' button on a
            pending request first. Only after reviewing can you choose to
            'Accept' or 'Reject' it.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search requests..."
                className="pl-9"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Tabs
              defaultValue="all"
              className="w-full md:w-auto"
              onValueChange={(value) => setStatusFilter(value)}
              value={statusFilter}
            >
              <TabsList className="grid w-full grid-cols-5 md:w-auto bg-[#118073]/10">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="assigned">Pending</TabsTrigger>
                <TabsTrigger value="reviewed">Reviewed</TabsTrigger>
                <TabsTrigger value="accepted">Accepted</TabsTrigger>
                <TabsTrigger value="rejected">Rejected</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Results Count */}
          <div className="text-sm text-muted-foreground mb-4">
            {filteredReferrals.length} results
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Position</TableHead>
                  <TableHead>Candidate</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredReferrals.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={6}
                      className="text-center py-6 text-muted-foreground"
                    >
                      No referral requests found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredReferrals.map((referral) => (
                    <TableRow key={referral.id}>
                      <TableCell className="font-medium max-w-[250px]">
                        <div className="truncate">{referral.jobTitle}</div>
                      </TableCell>
                      <TableCell>{referral.candidateName}</TableCell>
                      <TableCell>{referral.companyName}</TableCell>
                      <TableCell>{referral.date}</TableCell>
                      <TableCell>
                        {referral.status === 'assigned' && (
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700"
                          >
                            Pending
                          </Badge>
                        )}
                        {referral.status === 'reviewed' && (
                          <Badge
                            variant="outline"
                            className="bg-blue-50 text-blue-700"
                          >
                            Reviewed
                          </Badge>
                        )}
                        {referral.status === 'accepted' && (
                          <Badge
                            variant="outline"
                            className="bg-emerald-50 text-emerald-700"
                          >
                            Accepted
                          </Badge>
                        )}
                        {referral.status === 'rejected' && (
                          <Badge
                            variant="outline"
                            className="bg-rose-50 text-rose-700"
                          >
                            Rejected
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() =>
                              window.open(referral.candidateResume, '_blank')
                            }
                          >
                            <ExternalLink className="h-3 w-3" />
                            Resume
                          </Button>

                          <ReferralDetailView
                            referral={referral}
                            canTakeAction={true}
                            onReview={
                              referral.status === 'assigned'
                                ? () => handleReviewFromDetail(referral)
                                : undefined
                            }
                            onApprove={
                              referral.status === 'reviewed'
                                ? () => handleApproveFromDetail(referral)
                                : undefined
                            }
                            onDecline={
                              referral.status === 'reviewed'
                                ? () => handleDeclineFromDetail(referral)
                                : undefined
                            }
                          />

                          {referral.status === 'assigned' && (
                            <>
                              <Button
                                variant="default"
                                size="sm"
                                className="bg-blue-600 hover:bg-blue-700"
                                onClick={() =>
                                  openActionDialog(referral, 'Reviewed')
                                }
                              >
                                Review
                              </Button>
                            </>
                          )}

                          {referral.status === 'reviewed' && (
                            <>
                              <Button
                                variant="default"
                                size="sm"
                                className="bg-[#118073] hover:bg-[#118073]/90"
                                onClick={() =>
                                  openActionDialog(referral, 'Accepted')
                                }
                              >
                                Accept
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() =>
                                  openActionDialog(referral, 'Rejected')
                                }
                              >
                                Reject
                              </Button>
                            </>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Action Dialog for Accept/Reject with Notes */}
      <Dialog open={actionDialogOpen} onOpenChange={setActionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {currentAction === 'Accepted'
                ? 'Accept Referral'
                : 'Reject Referral'}
            </DialogTitle>
            <DialogDescription>
              {currentAction === 'Accepted'
                ? 'Add any notes before accepting this referral.'
                : 'Please provide a reason for rejecting this referral.'}
            </DialogDescription>
          </DialogHeader>

          {selectedReferral && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="font-medium">Candidate:</div>
                <div>{selectedReferral.candidateName}</div>
                <div className="font-medium">Position:</div>
                <div>{selectedReferral.jobTitle}</div>
                <div className="font-medium">Company:</div>
                <div>{selectedReferral.companyName}</div>
                <div className="font-medium">Date:</div>
                <div>{selectedReferral.date}</div>
              </div>

              <Textarea
                placeholder={
                  currentAction === 'Accepted'
                    ? 'Add any notes about this referral (optional)'
                    : 'Please provide a reason for rejecting'
                }
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
              />
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setActionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant={currentAction === 'Accepted' ? 'default' : 'destructive'}
              className={
                currentAction === 'Accepted'
                  ? 'bg-[#118073] hover:bg-[#118073]/90'
                  : ''
              }
              onClick={() => handleStatusChange(currentAction)}
            >
              {currentAction === 'Accepted'
                ? 'Accept Referral'
                : 'Reject Referral'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Review Referral</DialogTitle>
            <DialogDescription>
              Mark this referral as reviewed. You'll earn $1 for reviewing.
            </DialogDescription>
          </DialogHeader>

          {selectedReferral && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div className="font-medium">Candidate:</div>
                <div>{selectedReferral.candidateName}</div>
                <div className="font-medium">Position:</div>
                <div>{selectedReferral.jobTitle}</div>
                <div className="font-medium">Company:</div>
                <div>{selectedReferral.companyName}</div>
                <div className="font-medium">Date:</div>
                <div>{selectedReferral.date}</div>
              </div>

              <Textarea
                placeholder="Add your review notes (optional)"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={4}
              />

              <div className="bg-blue-50 p-3 rounded-md text-sm text-blue-800">
                <p>
                  After reviewing, you can still accept or reject this referral.
                  If you accept after reviewing, you'll earn an additional $2.
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setReviewDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="default"
              className="bg-blue-600 hover:bg-blue-700"
              onClick={() => handleStatusChange('Reviewed')}
            >
              Mark as Reviewed
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
