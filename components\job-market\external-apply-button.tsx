'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { createStartupApplication } from '@/actions/job-market/startup-applications';
import { createPublicFirmApplication } from '@/actions/job-market/publicFirm-applications';
import { useToast } from '@/components/ui/use-toast';
import { v4 as uuidv4 } from 'uuid';
interface ExternalApplyButtonProps {
  jobId: string;
  jobTitle: string;
  jobLink: string;
  company?: string;
  companyName?: string;
  user: any; // Replace with your user type
  hasApplied: boolean;
  onApplicationSuccess: (application: any) => void;
  fullWidth?: boolean;
  jobType?: 'startup' | 'publicFirm'; // Add job type to determine which action to use
}

export const ExternalApplyButton = ({
  jobId,
  jobTitle,
  jobLink,
  company,
  companyName,
  user,
  hasApplied,
  onApplicationSuccess,
  fullWidth = false,
  jobType = 'startup' // Default to startup for backward compatibility
}: ExternalApplyButtonProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click event
    if (hasApplied) return;

    // For publicFirm jobs, directly open the external link and silently record the application
    if (jobType === 'publicFirm') {
      window.open(jobLink, '_blank', 'noopener,noreferrer');

      // Silently record the application if the user is logged in
      if (user?.email) {
        recordPublicFirmApplication();
      }

      return;
    }

    // For startup jobs, show the tracking dialog
    setIsDialogOpen(true);
  };

  const handleConfirm = async () => {
    if (!user?.email) {
      toast({
        title: 'Authentication Required',
        description: 'Please sign in to apply for this job',
        variant: 'destructive'
      });
      setIsDialogOpen(false);
      return;
    }

    setIsSubmitting(true);

    try {
      let result;

      if (jobType === 'publicFirm') {
        // Public firm application
        const applicationData = {
          id: uuidv4(),
          job_id: jobId,
          job_title: jobTitle,
          company: company,
          company_name: companyName,
          candidate: user.email,
          creator: user.email,
          status: 'Applied',
          creation_date: new Date().toISOString()
        };

        result = await createPublicFirmApplication(applicationData);

        // Pass the application data back to the parent component
        if (result.applicationId) {
          onApplicationSuccess({
            ...applicationData,
            id: result.applicationId
          });
        }
      } else {
        // Startup application (original behavior)
        const applicationData = {
          id: uuidv4(),
          job: jobId,
          job_title: jobTitle,
          company: company,
          company_name: companyName,
          candidate: user.email,
          creator: user.email,
          status: 'Applied',
          creation_date: new Date().toISOString()
        };

        result = await createStartupApplication(applicationData);

        // Pass the application data back to the parent component
        if (result.applicationId) {
          onApplicationSuccess({
            ...applicationData,
            id: result.applicationId
          });
        }
      }

      toast({
        title: 'Application Recorded',
        description: `Your application to ${companyName} has been recorded.`
      });

      // Open the external link in a new tab
      window.open(jobLink, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('Error recording application:', error);
      toast({
        title: 'Error',
        description: 'Failed to record your application. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
      setIsDialogOpen(false);
    }
  };

  const handleRedirectOnly = () => {
    setIsDialogOpen(false);
    window.open(jobLink, '_blank', 'noopener,noreferrer');
  };

  // Function to record publicFirm application silently
  const recordPublicFirmApplication = async () => {
    try {
      const applicationData = {
        id: uuidv4(),
        job_id: jobId,
        job_title: jobTitle,
        company: company,
        company_name: companyName,
        candidate: user.email,
        creator: user.email,
        status: 'Applied',
        creation_date: new Date().toISOString()
      };

      const result = await createPublicFirmApplication(applicationData);

      if (result.applicationId) {
        onApplicationSuccess({
          ...applicationData,
          id: result.applicationId
        });
      }
    } catch (error) {
      console.error('Error silently recording application:', error);
      // We don't show errors to the user in this case to avoid disrupting the flow
    }
  };

  return (
    <>
      <Button
        variant="outline"
        className={`${fullWidth ? 'w-full' : ''} ${
          hasApplied
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-300'
            : 'border-[#36BA98] text-[#36BA98] hover:bg-[#36BA98] hover:text-white'
        }`}
        onClick={handleClick}
        disabled={hasApplied}
      >
        <ExternalLink className="mr-2 h-4 w-4" />
        {hasApplied
          ? 'Already Applied'
          : jobType === 'publicFirm'
            ? `Open ${companyName || 'External'} Link`
            : `Apply on ${companyName || 'Company'}`}
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>External Application</DialogTitle>
            <DialogDescription>
              You are about to be redirected to {companyName || "the company's"}{' '}
              external application page. Would you like us to track your
              application?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={handleRedirectOnly}>
              Just Redirect Me
            </Button>
            <Button
              className="bg-[#36BA98] hover:bg-[#2da885] text-white"
              onClick={handleConfirm}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Recording...' : 'Track & Continue'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
