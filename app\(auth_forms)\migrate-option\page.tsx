'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useRouter, useSearchParams } from 'next/navigation';
import { Mail, Loader2, ArrowLeft, Chrome, Linkedin } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { sendMigrationMagicLink } from '@/utils/auth/migration';
import { getURL } from '@/utils/helpers';
import type { Provider } from '@supabase/supabase-js';
import Link from 'next/link';
import Image from 'next/image';
import { Separator } from '@/components/ui/separator';

export default function MigrationOption() {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);

  // OAuth providers configuration (copied from signin page)
  const oAuthProviders = [
    {
      name: 'google',
      displayName: 'Google',
      icon: <Chrome className="mr-2 h-4 w-4" />
    },
    {
      name: 'linkedin',
      displayName: 'LinkedIn',
      icon: <Linkedin className="mr-2 h-4 w-4" />
    }
  ] as const;
  
  // Handle email magic link reset
  const handleEmailReset = async () => {
    if (!email) {
      toast({
        title: 'Email Address Required',
        description: 'Please go back to the sign-in page and enter your email address.',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Send magic link email
      const result = await sendMigrationMagicLink(email);

      if (result.error) {
        throw new Error(result.error);
      }

      setIsEmailSent(true);
      toast({
        title: 'Email Sent',
        description: 'Check your inbox for a login link to reset your password.',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error sending magic link:', error);
      toast({
        title: 'Email Sending Failed',
        description: 'Please try again or contact <NAME_EMAIL>',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle OAuth signin (copied from signin page)
  const handleOAuthSubmit = async (provider: Provider) => {
    setIsSubmitting(true);

    try {
      const supabase = createClient();
      const redirectURL = getURL('/auth/callback');

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: { redirectTo: redirectURL }
      });

      if (error) throw error;

      toast({
        title: 'Redirecting...',
        description: `Signing in with ${provider}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('OAuth sign-in error:', error);
      toast({
        title: 'OAuth Sign-in Failed',
        description: 'Please try again or use another sign-in method.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (isSubmitting) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <Loader2 className="animate-spin w-6 h-6 mx-auto mb-4 text-primary" />
          <p className="text-sm text-muted-foreground">Processing your request...</p>
        </div>
      </div>
    );
  }
  
  // Email sent success state
  if (isEmailSent) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <div className="bg-green-50 text-green-700 p-4 rounded-full inline-block mb-4">
            <Mail className="w-8 h-8" />
          </div>
          <h1 className="text-2xl font-bold mb-2">Check Your Email</h1>
          <p className="text-muted-foreground mb-6">
            We've sent a password reset link to <strong>{email}</strong>.
            Click the link in the email to reset your password.
          </p>

          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Can't find the email? Check your spam folder or request a new link.
            </p>

            <div className="flex flex-col space-y-3">
              <Button
                variant="outline"
                className="w-full"
                onClick={handleEmailReset}
              >
                Resend Reset Link
              </Button>
              
              <Link href="/signin" className="inline-block w-full">
                <Button variant="ghost" className="w-full">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Sign In
                </Button>
              </Link>
            </div>
          </div>
          
          <p className="text-sm text-muted-foreground mt-8">
            Having trouble? Contact{' '}
            <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-background">
      {/* Left Column */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Reset Your Password</h1>
            <p className="text-xl">
              We found your account in our system. Choose how you'd like to reset your password and regain access to your account.
            </p>
          </div>
        </div>
        <div className="relative z-10 text-sm opacity-80">
          © {new Date().getFullYear()} InternUp. All rights reserved.
        </div>
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20" />
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40" />
      </div>

      {/* Right Column */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto flex items-center justify-center">
        <div className="w-full max-w-md">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold">Reset Password</h2>
            <Link href="/signin" className="text-sm text-primary flex items-center">
              <ArrowLeft className="mr-1 h-4 w-4" /> Back to Login
            </Link>
          </div>

          <div className="mb-6 bg-blue-50 p-4 rounded-md border border-blue-100">
            <p className="text-sm">
              We found your account for <strong>{email}</strong> in our records. Choose how you'd like to reset your password.
            </p>
          </div>

          <div className="space-y-6">
            {/* OAuth Options */}
            <div className="space-y-4">
              <h3 className="font-medium">Sign in with Social Account</h3>
              <p className="text-sm text-muted-foreground">
                Use your social account to sign in quickly and securely.
              </p>

              <div className="flex gap-3">
                {oAuthProviders.map((provider) => (
                  <Button
                    key={provider.name}
                    variant="outline"
                    className="w-full border-muted-foreground/20 hover:bg-[#e6f7ef] hover:text-[#118073] transition-colors"
                    onClick={() => handleOAuthSubmit(provider.name as Provider)}
                    disabled={isSubmitting}
                  >
                    {provider.icon}
                    {provider.displayName}
                  </Button>
                ))}
              </div>
            </div>

            <div className="relative">
              <Separator className="bg-muted-foreground/20" />
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or reset via email
                </span>
              </div>
            </div>

            {/* Email Reset Option */}
            <div className="space-y-4">
              <h3 className="font-medium">Reset Password via Email</h3>
              <p className="text-sm text-muted-foreground">
                We'll send you a secure link to reset your password.
              </p>

              <Button
                className="w-full bg-[#118073] hover:bg-[#0d6a5a]"
                onClick={handleEmailReset}
                disabled={isSubmitting}
              >
                <Mail className="mr-2 h-4 w-4" />
                Send Reset Link
              </Button>
            </div>

            <div className="pt-4">
              <p className="text-sm text-muted-foreground mt-4">
                Having trouble? Contact{' '}
                <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                  <EMAIL>
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
