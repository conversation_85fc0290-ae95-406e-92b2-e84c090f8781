'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import {
  getPublicFirmJobs,
  getPublicFirmJobsFilterOptions
} from '@/actions/job-market/publicFirms-jobs';
import { getApplicationsForUser } from '@/actions/job-market/publicFirm-applications';
import type {
  PublicFirmJob,
  PaginatedResponse,
  PublicFirmJobFilters
} from '@/types/public-firms';
import JobCard from '@/components/job-market/job-card';
import JobFilters from '@/components/job-market/job-filters';
import Pagination from '@/components/job-market/pagination';
import JobListSkeleton from '@/components/job-market/job-list-skeleton';
import { useUser } from '@/hooks/useUser';

interface PublicFirmJobsListProps {
  currentPage: number;
  searchParams: {
    page?: string;
    search?: string;
    location?: string;
    term?: string;
    active?: string;
  };
}

export default function PublicFirmJobsList({
  currentPage,
  searchParams
}: PublicFirmJobsListProps) {
  const { user, loading: userLoading } = useUser();
  const { toast } = useToast();

  const [jobs, setJobs] = useState<PublicFirmJob[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [filterOptions, setFilterOptions] = useState({
    locations: [] as string[],
    terms: [] as string[]
  });
  const [appliedJobs, setAppliedJobs] = useState<Set<string>>(new Set());

  const ITEMS_PER_PAGE = 20;
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  useEffect(() => {
    fetchJobs();
    fetchFilterOptions();
  }, [currentPage, searchParams]);

  useEffect(() => {
    if (user?.email) {
      fetchAppliedJobs();
    }
  }, [user]);

  const fetchAppliedJobs = async () => {
    if (!user?.email) return;

    try {
      const applications = await getApplicationsForUser(user.email);
      const appliedJobIds = new Set(
        applications
          .map((app) => app.job_id!)
          .filter((job) => job !== null && job !== undefined)
      );
      setAppliedJobs(appliedJobIds);
    } catch (error) {
      console.error('Error fetching applied jobs:', error);
    }
  };

  const fetchJobs = async () => {
    setLoading(true);
    try {
      const filters: PublicFirmJobFilters = {
        search: searchParams.search || '',
        location: searchParams.location || '',
        term: searchParams.term || '',
        active: searchParams.active === undefined ? true : searchParams.active !== 'false'
      };
      const response: PaginatedResponse<PublicFirmJob> =

        await getPublicFirmJobs({
          page: currentPage,
          limit: ITEMS_PER_PAGE,
          ...filters
        });

      setJobs(response.data);
      setTotalCount(response.count || 0);
    } catch (error) {
      console.error('Error fetching jobs:', error);
      toast({
        title: 'Error',
        description: 'Failed to load jobs. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchFilterOptions = async () => {
    try {
      const options = await getPublicFirmJobsFilterOptions();
      setFilterOptions({
        locations: options.locations,
        terms: options.terms
      });
    } catch (error) {
      console.error('Error fetching filter options:', error);
      // Fallback to static values if API fails
      setFilterOptions({
        locations: ['Remote', 'New York', 'San Francisco', 'London', 'Berlin'],
        terms: ['Full-time', 'Part-time', 'Contract', 'Internship']
      });
    }
  };

  if (loading || userLoading) {
    return <JobListSkeleton count={5} />;
  }

  return (
    <>
      <Card className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-semibold text-gray-900">
            Available Positions
          </h2>
          <span className="text-gray-600 font-medium">
            {totalCount} jobs found
          </span>
        </div>

        <JobFilters
          showSearch={true}
          showActiveToggle={true}
          searchPlaceholder="Search jobs..."
          filterOptions={{
            locations: filterOptions.locations,
            terms: filterOptions.terms
          }}
          initialValues={{
            search: searchParams.search || '',
            location: searchParams.location || '',
            term: searchParams.term || '',
            active: searchParams.active === undefined ? true : searchParams.active !== 'false'
          }}
          baseUrl="/public-firms"
        />
      </Card>

      {jobs.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <p className="text-gray-600">No jobs found matching your criteria.</p>
        </div>
      ) : (
        <>
          <div className="space-y-6">
            {jobs.map((job) => (
              <JobCard
                key={job.id}
                id={job.id}
                title={job.job_title || ''}
                companyName={job.company || ''}
                companyLogo={job.logo || null}
                location={job.location || null}
                term={job.term || null}
                createdAt={job.creation_date || new Date().toISOString()}
                applyLink={job.job_link || `/public-firms/${job.id}/apply`}
                requestReferralLink={`/public-firms/${job.id}/referral`}
                showBookmark={false}
                hideEasyApply={true}
                hideViewDetails={true}
                hasApplied={appliedJobs.has(job.id)}
                jobType="publicFirm"
              />
            ))}
          </div>

          {totalCount > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              baseUrl="/public-firms"
              filters={{
                search: searchParams.search || undefined,
                location: searchParams.location || undefined,
                term: searchParams.term || undefined,
                active: searchParams.active || undefined
              }}
            />
          )}
        </>
      )}
    </>
  );
}
