'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useUser';
import { useSubscription } from '@/hooks/useSubscription';
import { SubscriptionDetails } from '@/components/membership/subscription-details';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Loader2, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { createStripePortal } from '@/utils/stripe/server';
import { useToast } from '@/components/ui/use-toast';
import { getActiveAndTrailingSubscription } from '@/actions/subscriptions';
import { Subscription } from '@/types/subscription';
import { PricingCard } from '@/components/membership/pricing-card';
import { ComparisonTable } from '@/components/membership/comparison-table';
import { SubscriptionFaq } from '@/components/membership/subscription-faq';

// Define a type for the possible return values from createStripePortal
type StripePortalResult = string | { errorRedirect?: string };

interface PricingPlan {
  name: string;
  price: string;
  period: 'monthly' | 'annual' | 'semi-annual' | 'one-time';
  description: string;
  features: readonly string[];
  ctaText: string;
  popular?: boolean;
}

// Define pricing plans with names that match the frontend_name in SUBSCRIPTION_PLANS
const pricingPlans: readonly PricingPlan[] = [
  {
    name: 'Essential Access Pass', // Matches frontend_name in SUBSCRIPTION_PLANS
    price: '$14.99',
    period: 'semi-annual',
    description: 'For students and new grads',
    features: [
      'Job alerts from 100+ platforms',
      'Expert career tips',
      'Direct apply—90% faster',
      'Basic job filters'
    ],
    ctaText: 'Subscribe'
  },
  {
    name: 'Pro Monthly Pass', // Matches frontend_name in SUBSCRIPTION_PLANS
    price: '$39.99',
    period: 'monthly',
    description: 'For ambitious professionals',
    features: [
      'Application guidance',
      'Priority referrals',
      'Internship & OPT support',
      'All Daily Access features'
    ],
    ctaText: 'Subscribe'
  },
  {
    name: 'Elite Semi-Annual Pass', // Matches frontend_name in SUBSCRIPTION_PLANS
    price: '$129.99',
    period: 'semi-annual',
    description: 'For growth-focused professionals',
    features: [
      'FAANG and startup referrals',
      'Weekly curated job lists',
      'AI-powered career tools',
      'Daily jobs from 100+ platforms'
    ],
    ctaText: 'Subscribe'
  },
  {
    name: 'Ultimate Annual Pass', // Matches frontend_name in SUBSCRIPTION_PLANS
    price: '$199.99',
    period: 'annual',
    description: 'For committed career builders',
    features: [
      'All Monthly Pass features',
      'Boost LinkedIn connections',
      'Real-world capstone projects',
      'Exclusive startup jobs'
    ],
    ctaText: 'Subscribe'
  }
] as const;

export default function MembershipPage() {
  const {
    user,
    loading: userLoading,
    initialized: userInitialized
  } = useUser();
  const { subscriptions, loading: subscriptionLoading } = useSubscription(
    userInitialized ? user?.id : undefined
  );
  const [activeSubscription, setActiveSubscription] =
    useState<Subscription | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    const fetchActiveSubscription = async () => {
      // Only proceed if user is fully initialized, loaded and not null
      if (!userInitialized || userLoading || !user?.id) {
        return;
      }

      try {
        const active = await getActiveAndTrailingSubscription(user.id);
        if (active) {
          setActiveSubscription(active);
        }
      } catch (error) {
        console.error('Error fetching active subscription:', error);
      }
    };

    fetchActiveSubscription();
  }, [user?.id, userLoading, userInitialized]);

  const handleManageSubscription = async () => {
    // Check if still loading or if user is not logged in
    if (userLoading) {
      toast({
        title: 'Loading',
        description: 'Please wait while we load your account information.',
        variant: 'default'
      });
      return;
    }

    if (!user?.id || !user?.email) {
      toast({
        title: 'Error',
        description: 'You must be logged in to manage your subscription.',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      const result: StripePortalResult = await createStripePortal(
        window.location.pathname,
        user.id,
        user.email
      );

      // Handle the result which can be a string URL or an object with errorRedirect
      if (typeof result === 'string') {
        // If it's a string URL, navigate to the Stripe portal
        window.location.href = result;
      } else if (result && typeof result === 'object') {
        const errorRedirectUrl = (result as { errorRedirect?: string })
          .errorRedirect;
        if (errorRedirectUrl) {
          // Check if we're already on the error redirect page to avoid an infinite redirect loop
          if (errorRedirectUrl.includes(window.location.pathname)) {
            // We're already on the error page, just show a toast
            toast({
              title: 'Subscription Management',
              description:
                'No active subscriptions found to manage. Please subscribe to a plan first.',
              variant: 'default'
            });
          } else {
            // Navigate to the error page
            window.location.href = errorRedirectUrl;
          }
        } else {
          throw new Error('Invalid response from subscription portal');
        }
      } else {
        throw new Error('Invalid response from subscription portal');
      }
    } catch (error) {
      console.error('Subscription management error:', error);
      toast({
        title: 'Error',
        description: 'Could not process your request. Please try again later.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Make sure we're checking both user and subscription loading states
  // Only consider the page loaded when user initialization is complete
  const isLoaded = userInitialized && !userLoading && !subscriptionLoading;

  return (
    <div className="container max-w-5xl py-8 mx-auto">
      <div className="flex items-center mb-8">
        <Link
          href="/membership"
          className="flex items-center text-primary hover:underline mr-4"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Back to Plans
        </Link>
        <h1 className="text-3xl font-bold">My Membership</h1>
      </div>

      {!isLoaded ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      ) : userInitialized && !userLoading && !user ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="mb-4">
              Please sign in to view your subscription details.
            </p>
            <Button onClick={() => router.push('/signin')}>Sign In</Button>
          </CardContent>
        </Card>
      ) : userInitialized && !userLoading && activeSubscription ? (
        <SubscriptionDetails
          subscription={activeSubscription}
          userId={user.id}
        />
      ) : userInitialized &&
        !userLoading &&
        subscriptions &&
        subscriptions.length > 0 ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold">Past Subscriptions</h2>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                You don't have an active subscription, but you have had
                subscriptions in the past.
              </p>
              <Button onClick={handleManageSubscription} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  'Manage Subscriptions'
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      ) : userInitialized &&
        !userLoading &&
        !activeSubscription &&
        !subscriptions?.length ? (
        <div className="space-y-10">
          <Card>
            <CardContent className="py-8 text-center">
              <h2 className="text-2xl font-bold mb-4">
                No Active Subscription
              </h2>
              <p className="mb-6">
                You don't have any active subscriptions. Unlock premium features
                with one of our membership plans below.
              </p>
            </CardContent>
          </Card>

          <section className="py-10">
            <div className="max-w-full mx-auto">
              <h2 className="mb-6 text-2xl font-bold text-center">
                Choose The Perfect Plan
              </h2>
              <p className="max-w-2xl mx-auto text-gray-600 text-center mb-8">
                Explore our membership options and find the one that fits your
                career goals. From job alerts to expert guidance, we've got a
                plan tailored for you.
              </p>
              {/* Changed to 3 columns for wider cards */}
              <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 max-w-7xl mx-auto">
                {pricingPlans.map((plan) => (
                  <PricingCard
                    key={plan.name}
                    {...plan}
                    user={user}
                    loading={userLoading}
                  />
                ))}
              </div>
            </div>
          </section>

          <section className="py-10 bg-gray-50 -mx-8 px-8">
            <div className="max-w-full mx-auto">
              <div className="max-w-3xl mx-auto text-center mb-10">
                <h2 className="mb-4 text-2xl font-bold">
                  See What Each Plan Offers
                </h2>
                <p className="text-gray-600">
                  A closer look at the benefits included in every membership.
                </p>
              </div>
              <ComparisonTable
                plans={pricingPlans}
                user={user}
                loading={userLoading}
              />
            </div>
          </section>

          <SubscriptionFaq />
        </div>
      ) : (
        <div className="space-y-6">
          <Card>
            <CardContent className="py-8 text-center">
              <h2 className="text-xl font-semibold mb-4">
                No Active Subscription
              </h2>
              <p className="mb-6">
                You don't have any active subscriptions. Explore our membership
                plans to unlock premium features.
              </p>
              <Button
                onClick={() => router.push('/membership')}
                className="bg-primary hover:bg-primary/90"
              >
                View Membership Plans
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
