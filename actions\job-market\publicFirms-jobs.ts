'use server';

import { createClient } from '@/utils/supabase/server';
import type { PublicFirmJob, PaginatedResponse } from '@/types/public-firms';

interface GetPublicFirmJobsParams {
  page?: number;
  limit?: number;
  search?: string;
  location?: string;
  term?: string;
  active?: boolean;
}

export async function getPublicFirmJobs({
  page = 1,
  limit = 20,
  search = '',
  location = '',
  term = '',
  active = true
}: GetPublicFirmJobsParams): Promise<PaginatedResponse<PublicFirmJob>> {
  const supabase = createClient();
  const offset = (page - 1) * limit;

  // Create a query that joins public_firm_jobs with public_firm_companies to get logos
  let query = supabase.from('public_firm_jobs').select(
    `
      *,
      public_firm_companies!inner(logo)
    `,
    { count: 'exact' }
  );

  // Apply filters
  if (search) {
    query = query.or(
      `job_title.ilike.%${search}%,description.ilike.%${search}%`
    );
  }

  if (location) {
    query = query.eq('location', location);
  }

  if (term) {
    query = query.eq('term', term);
  }

  if (active) {
    query = query.eq('live', 'yes');
  }

  // Apply pagination
  query = query
    .range(offset, offset + limit - 1)
    .order('creation_date', { ascending: false });

  const { data, error, count } = await query;

  if (error) {
    console.error('Error fetching public firm jobs:', error);
    throw new Error('Failed to fetch public firm jobs');
  }

  // Process the data to extract the company logo from the nested public_firm_companies
  const processedData = data?.map((item) => {
    const processedItem = { ...item };

    // Extract logo from public_firm_companies if available
    if (item.public_firm_companies && item.public_firm_companies.logo) {
      processedItem.logo = item.public_firm_companies.logo;
    }

    // Remove the nested public_firm_companies object
    delete processedItem.public_firm_companies;

    return processedItem;
  });

  return {
    data: processedData as PublicFirmJob[],
    count: count
  };
}

export async function getPublicFirmJobById(
  id: string
): Promise<PublicFirmJob | null> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('public_firm_jobs')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching public firm job:', error);
    return null;
  }

  return data as PublicFirmJob;
}

/**
 * Get filter options for public firm jobs
 * This fetches unique terms and locations for the filter dropdowns
 */
export async function getPublicFirmJobsFilterOptions(): Promise<{
  locations: string[];
  terms: string[];
}> {
  const supabase = createClient();

  // Fetch unique locations
  const { data: locations, error: locationsError } = await supabase
    .from('public_firm_jobs')
    .select('location')
    .not('location', 'is', null)
    .order('location')
    .limit(100);

  if (locationsError) {
    console.error('Error fetching location options:', locationsError);
    throw new Error('Failed to fetch location options');
  }

  // Fetch unique terms
  const { data: terms, error: termsError } = await supabase
    .from('public_firm_jobs')
    .select('term')
    .not('term', 'is', null)
    .order('term')
    .limit(100);

  if (termsError) {
    console.error('Error fetching term options:', termsError);
    throw new Error('Failed to fetch term options');
  }

  // Extract unique values and filter out null/undefined
  const uniqueLocations = Array.from(
    new Set(locations.map((item) => item.location))
  )
    .filter(Boolean)
    .sort() as string[];

  const uniqueTerms = Array.from(new Set(terms.map((item) => item.term)))
    .filter(Boolean)
    .sort() as string[];

  return {
    locations: uniqueLocations,
    terms: uniqueTerms
  };
}
