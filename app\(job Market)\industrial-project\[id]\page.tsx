import { getCapstoneProject } from '@/actions/admin/capstone';
import { Badge } from '@/components/ui/badge';
import { Clock, Link as LinkIcon, ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { ApplyProjectButton } from '../components/apply-project-button';

interface CapstoneDetailPageProps {
  params: {
    id: string;
  };
}

export const dynamic = 'force-dynamic';
export const revalidate = 0;

// Format BBCode-style tags to HTML
const FormattedDescription = ({
  description
}: {
  description?: string | null;
}) => {
  if (!description) return <p>No description available.</p>;

  // Step 1: Replace all heading tags
  let html = description
    .replace(
      /\[h3\](.*?)\[\/h3\]/g,
      '<h3 class="text-xl font-semibold text-primary mt-8 mb-3">$1</h3>'
    )
    .replace(
      /\[h4\](.*?)\[\/h4\]/g,
      '<h4 class="text-lg font-medium text-gray-800 mt-6 mb-2">$1</h4>'
    );

  // Step 2: Split into paragraphs and process them
  const processedHtml = html
    .split('\n')
    .map((line) => line.trim())
    .filter((line) => line.length > 0)
    .map((line) => {
      // If it's already a heading, don't wrap it in p tags
      if (line.startsWith('<h3') || line.startsWith('<h4')) {
        return line;
      }
      // Otherwise, wrap in paragraph tags
      return `<p class="text-gray-700 mb-4">${line}</p>`;
    })
    .join('');

  return (
    <div
      dangerouslySetInnerHTML={{ __html: processedHtml }}
      className="leading-relaxed"
    />
  );
};

// Get plain text version for metadata
const getPlainTextDescription = (text?: string | null) => {
  if (!text) return '';
  return text.replace(/\[\/?(h3|h4)\]/g, '');
};

export async function generateMetadata({
  params
}: CapstoneDetailPageProps): Promise<Metadata> {
  const { id } = params;
  const project = await getCapstoneProject(id);

  if (!project) {
    return {
      title: 'Project Not Found | InternUp',
      description: 'The requested capstone project could not be found.'
    };
  }

  const plainDescription = getPlainTextDescription(project.project_description);

  return {
    title: `${project.project_title} | Capstone Project | InternUp`,
    description: plainDescription
      ? plainDescription.slice(0, 160) +
        (plainDescription.length > 160 ? '...' : '')
      : 'View details about this capstone project on InternUp.'
  };
}

export default async function CapstoneDetailPage({
  params
}: CapstoneDetailPageProps) {
  const { id } = params;
  const project = await getCapstoneProject(id);

  if (!project) {
    return notFound();
  }

  // Format date for display
  const formatDate = (dateString?: Date | null) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <Link
          href="/industrial-project"
          className="inline-flex items-center mb-4 text-sm text-primary hover:text-primary/80 hover:underline"
        >
          <ChevronLeft className="mr-1 h-4 w-4" />
          Back to all projects
        </Link>

        <div className="bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-5 border-b border-gray-100">
            <div className="flex items-center justify-between flex-wrap gap-3">
              <h1 className="text-3xl font-bold text-gray-900">
                {project.project_title}
              </h1>
              {project.type && (
                <Badge className="bg-primary/10 text-primary hover:bg-primary/20 px-3 py-1">
                  {project.type}
                </Badge>
              )}
            </div>

            <div className="mt-3 flex flex-wrap items-center gap-6 text-sm text-gray-500">
              {project.project_no && <div>Project #{project.project_no}</div>}

              {project.creation_date && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  Posted on {formatDate(project.creation_date)}
                </div>
              )}

              {project.duration_days && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {project.duration_days} days duration
                </div>
              )}
            </div>
          </div>

          <div className="p-5">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="md:col-span-3 space-y-4">
                <div>
                  <h2 className="text-2xl font-semibold text-primary mb-3">
                    Project Description
                  </h2>
                  <div className="prose prose-compact max-w-none text-gray-700">
                    <FormattedDescription
                      description={project.project_description}
                    />
                  </div>
                </div>

                {project.mentors && (
                  <div className="border-t border-gray-100 pt-4 mt-4">
                    <h2 className="text-xl font-semibold text-primary mb-2">
                      Mentors
                    </h2>
                    <p className="text-gray-700">{project.mentors}</p>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <h3 className="font-medium text-gray-900 mb-2">
                    Industry Roles
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {project.industry_jobs?.map((job, index) => (
                      <Badge
                        key={index}
                        className="bg-gray-100 text-gray-800 hover:bg-gray-200"
                      >
                        {job.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>

                {project.firm_url && (
                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <h3 className="font-medium text-gray-900 mb-2">
                      Company Website
                    </h3>
                    <a
                      href={
                        project.firm_url.startsWith('http')
                          ? project.firm_url
                          : `https://${project.firm_url}`
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-primary hover:text-primary/80 hover:underline"
                    >
                      <LinkIcon className="mr-1 h-4 w-4" />
                      Visit Website
                    </a>
                  </div>
                )}

                <div
                  id="apply"
                  className="bg-primary/5 rounded-lg p-4 border border-primary/20"
                >
                  <h3 className="font-medium text-primary mb-2">
                    Apply for this Project
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Interested in working on this capstone project? Click the
                    button below to submit your application.
                  </p>
                  <ApplyProjectButton
                    projectId={id}
                    projectTitle={project.project_title || ''}
                    fullWidth
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
