'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Chrome, Linkedin, ArrowLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { createClient } from '@/utils/supabase/client';
import type { Provider } from '@supabase/supabase-js';
import { getURL } from '@/utils/helpers';
import Particles from '@/components/magicui/particles';
import { checkUserNeedsMigration } from '@/utils/auth/migration';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';

// Validation schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required')
});

type FormValues = z.infer<typeof formSchema>;

export default function SignIn() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: { email: '', password: '' }
  });

  const onSubmit = async (values: FormValues) => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    const supabase = createClient();

    try {
      // Supabase sign in with password
      const { data, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password
      });

      // ✅ Success: redirect user
      if (data.session) {
        const {data:userData} = await supabase.from('users').select('user_type').eq('email', values.email).single();
        const userType = userData?.user_type?.toLowerCase() || 'candidate';

        const pathMap: Record<string, string> = {
          candidate: '/candidate',
          company: '/company',
          insider: '/insider',
          admin: '/admin'
        };

        router.push(pathMap[userType] || '/dashboard');
        router.refresh();
        return;
      }

      // ❌ Handle authentication errors
      if (error) {
        console.log('Sign-in error:', error);
        
        // Check if user needs migration using the admin API
        // This will reliably check both auth system and profiles table
        const migrationResult = await checkUserNeedsMigration(values.email);
        console.log('Migration check result:', migrationResult);
        
        // Case 2: User exists in profiles table but not in auth (needs password reset)
        if (migrationResult.needsMigration) {
          toast({
            title: 'Password Reset Required',
            description: 'Please choose how you\'d like to reset your password.',
            variant: 'default'
          });

          // Redirect to password reset options page
          router.push(`/migrate-option?email=${encodeURIComponent(values.email)}`);
          return;
        }
        // Case 3: User is not in auth or users table (direct to signup)
        else if (!migrationResult.existsInAuth && !migrationResult.existsInProfiles) {
          toast({
            title: 'Account Not Found',
            description: 'No account found with this email. Please sign up instead.',
            variant: 'destructive'
          });
          
          // Redirect to signup page
          router.push('/signup');
          return;
        }
        // Case 4: User is in auth but wrong credentials (normal error)
        else if (migrationResult.existsInAuth) {
          toast({
            title: 'Incorrect Password',
            description: 'The email or password you entered is incorrect.',
            variant: 'destructive'
          });
          return;
        }
      }

      // 🔴 Other unexpected auth errors
      throw error;
    } catch (error) {
      console.error('Sign in error:', error);
      toast({
        title: 'Authentication Failed',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOAuthSubmit = async (provider: Provider) => {
    setIsSubmitting(true);

    try {
      const supabase = createClient();
      const redirectURL = getURL('/auth/callback');

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: { redirectTo: redirectURL }
      });

      if (error) throw error;

      toast({
        title: 'Redirecting...',
        description: `Signing in with ${provider}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('OAuth sign-in error:', error);
      toast({
        title: 'OAuth Sign-in Failed',
        description: 'Please try again or use another sign-in method.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const oAuthProviders = [
    {
      name: 'google',
      displayName: 'Google',
      icon: <Chrome className="mr-2 h-4 w-4" />
    },
    {
      name: 'linkedin',
      displayName: 'LinkedIn',
      icon: <Linkedin className="mr-2 h-4 w-4" />
    }
  ] as const;

  return (
    <div className="flex min-h-[100dvh] bg-background">
      {/* Left Section */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Welcome Back</h1>
            <p className="text-xl">
              Sign in to your account to continue your journey with InternUp —
              where opportunities meet talent.
            </p>
          </div>
        </div>
        <div className="relative z-10 text-sm opacity-80">
          © {new Date().getFullYear()} InternUp. All rights reserved.
        </div>
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20" />
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40" />
        <Particles
          className="absolute inset-0"
          quantity={300}
          size={1}
          ease={1}
          refresh
        />
      </div>

      {/* Right Section */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto">
        <div className="flex items-center justify-between mb-8">
          <Link
            href="/"
            className="rounded-md p-2 transition-colors hover:bg-muted"
            prefetch={false}
          >
            <ArrowLeft className="h-5 w-5" aria-hidden="true" />
            <span className="sr-only">Back</span>
          </Link>
        </div>

        <div className="max-w-md mx-auto">
          <div className="space-y-6 text-center mb-8">
            <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent">
              Welcome Back
            </h1>
            <p className="text-muted-foreground">
              Sign in to your account to continue
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          autoComplete="email"
                          disabled={isSubmitting}
                          className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="••••••••"
                          type="password"
                          autoComplete="current-password"
                          disabled={isSubmitting}
                          className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end">
                <Link
                  href="/forgot-password"
                  className="text-sm font-medium text-[#118073] hover:text-[#16a38a] hover:underline transition-colors"
                  prefetch={false}
                >
                  Forgot your password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full bg-[#118073] hover:bg-[#16a38a] text-white transition-colors"
                size="lg"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Signing in...' : 'Sign in'}
              </Button>
            </form>
          </Form>

          <div className="relative my-8">
            <Separator className="bg-muted-foreground/20" />
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>

          <div className="mt-6 flex gap-3">
            {oAuthProviders.map((provider) => (
              <Button
                key={provider.name}
                variant="outline"
                className="w-full border-muted-foreground/20 hover:bg-[#e6f7ef] hover:text-[#118073] transition-colors"
                onClick={() => handleOAuthSubmit(provider.name as Provider)}
                disabled={isSubmitting}
              >
                {provider.icon}
                {provider.displayName}
              </Button>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/signup"
              className="text-[#118073] hover:text-[#16a38a] text-sm font-medium hover:underline transition-colors"
              prefetch={false}
            >
              Don't have an account?{' '}
              <span className="font-semibold">Sign up</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
