import { Metadata } from 'next';
import { PropsWithChildren, Suspense } from 'react';
import { getURL } from '@/utils/helpers';
import { siteConfig } from '@/config/site';
import { cn } from '@/lib/utils';
import { Toaster } from '@/components/ui/toaster';
// import { TailwindIndicator } from '@/components/tailwind-indicator';
import { ThemeProvider } from '@/components/theme-provider';
import { DM_Sans as FontSans } from 'next/font/google';
// import localFont from 'next/font/local';
import type { Viewport } from 'next';
import { RootProvider } from 'fumadocs-ui/provider';
// import { TRPCReactProvider } from '@/trpc/react';
import { GoogleAnalytics } from '@next/third-parties/google';

const fontSans = FontSans({
  subsets: ['latin'],
  variable: '--font-sans'
});

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' }
  ]
};

import '@/styles/globals.css';
import Chatbot from '@/components/chatbot';

// Font files can be colocated inside of `pages`
// const fontHeading = localFont({
//   // src: "../assets/fonts/NotoSansMono-VariableFont_wdth,wght.ttf",
//   src: '../assets/fonts/CalSans-SemiBold.woff2',
//   variable: '--font-heading'
// });

export const metadata: Metadata = {
  metadataBase: new URL(getURL()),
  title: {
    default: 'InternUp - Find Your Dream Internship and Job',
    template: '%s | InternUp'
  },
  description:
    'Discover exciting internship and job opportunities at InternUp. We care and help international students to find quality internships and jobs in their dream companies.',
  keywords: [
    'internship',
    'jobs',
    'international students',
    'career',
    'employment',
    'work experience',
    'student opportunities',
    'internship platform',
    'job search',
    'career development',
    'student jobs',
    'entry level positions',
    'internship opportunities',
    'job opportunities',
    'internship platform',
    'job search',
    'career development',
    'student jobs'
  ],
  authors: [{ name: 'InternUp Team' }],
  creator: 'InternUp',
  publisher: 'InternUp',
  formatDetection: {
    email: false,
    address: false,
    telephone: false
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: siteConfig.url,
    title: siteConfig.name,
    description: siteConfig.description,
    siteName: siteConfig.name
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon_new.png',
    apple: '/favicon_new.png'
  },
  manifest: `${siteConfig.url}/site.webmanifest`
};

export default async function RootLayout({ children }: PropsWithChildren) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={cn(
          'min-h-screen bg-background font-sans antialiased',
          fontSans.variable
        )}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
        >
          <RootProvider>{children}</RootProvider>
          <GoogleAnalytics
            gaId={process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS_ID!}
          />
          <Toaster />
          {/* <TailwindIndicator /> */}
        </ThemeProvider>
        <Chatbot />
      </body>
    </html>
  );
}
