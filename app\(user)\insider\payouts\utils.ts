'use server';

import { createClient } from '@/utils/supabase/server';
import { InsiderPaymentAccount } from '@/types/insider-payouts';

/**
 * Custom function to get an insider's payment account that properly handles the relationship
 * between insiders and payment accounts
 */
export async function getPaymentAccountForUser(
  userIdOrEmail: string,
  isEmail: boolean = false
): Promise<InsiderPaymentAccount | null> {
  console.log(`Getting payment account for ${isEmail ? 'email' : 'user ID'}: ${userIdOrEmail}`);
  const supabase = createClient();

  try {
    if (isEmail) {
      // First get the insider record using the email
      console.log(`Looking up insider with email: ${userIdOrEmail}`);
      const { data: insider, error: insiderError } = await supabase
        .from('insiders')
        .select('id')
        .eq('user_email', userIdOrEmail)
        .maybeSingle();

      if (insiderError && insiderError.code !== 'PGRST116') {
        console.error('Error finding insider by email:', insiderError);
        return null;
      }

      if (!insider) {
        console.log(`No insider found with email: ${userIdOrEmail}`);
        return null;
      }

      const insiderId = insider.id;
      console.log(`Found insider with ID: ${insiderId}`);

      // Now look for payment accounts with this insider_id
      const { data: accountByInsiderId, error: insiderIdError } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('insider_id', insiderId)
        .maybeSingle();

      if (accountByInsiderId) {
        console.log('Found payment account by insider_id:', accountByInsiderId);
        return accountByInsiderId as InsiderPaymentAccount;
      }

      // If not found by insider_id, try with id field
      const { data: accountById, error: idError } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('id', insiderId)
        .maybeSingle();

      if (accountById) {
        console.log('Found payment account by id:', accountById);
        return accountById as InsiderPaymentAccount;
      }

      // If still not found, try with email field directly
      const { data: accountByEmail, error: emailError } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('email', userIdOrEmail)
        .maybeSingle();

      if (accountByEmail) {
        console.log('Found payment account by email field:', accountByEmail);
        return accountByEmail as InsiderPaymentAccount;
      }

      console.log(`No payment account found for insider with ID: ${insiderId}`);
      return null;
    } else {
      // Try to find by user_id field first
      const { data: accountByUserId, error: userIdError } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('user_id', userIdOrEmail)
        .maybeSingle();

      if (accountByUserId) {
        console.log('Found payment account by user_id:', accountByUserId);
        return accountByUserId as InsiderPaymentAccount;
      }

      // If not found, try with id field
      const { data: accountById, error: idError } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('id', userIdOrEmail)
        .maybeSingle();

      if (accountById) {
        console.log('Found payment account by id:', accountById);
        return accountById as InsiderPaymentAccount;
      }

      // If still not found, try to find the insider by user ID
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('email')
        .eq('id', userIdOrEmail)
        .maybeSingle();

      if (user?.email) {
        // Now try to find the insider by email
        const { data: insider, error: insiderError } = await supabase
          .from('insiders')
          .select('id')
          .eq('user_email', user.email)
          .maybeSingle();

        if (insider?.id) {
          // Try to find the payment account by insider_id
          const { data: accountByInsiderId, error: insiderIdError } = await supabase
            .from('insider_payment_accounts')
            .select('*')
            .eq('insider_id', insider.id)
            .maybeSingle();

          if (accountByInsiderId) {
            console.log('Found payment account via user->email->insider->insider_id path:', accountByInsiderId);
            return accountByInsiderId as InsiderPaymentAccount;
          }

          // Last try with id field
          const { data: accountByInsiderAsId, error: insiderAsIdError } = await supabase
            .from('insider_payment_accounts')
            .select('*')
            .eq('id', insider.id)
            .maybeSingle();

          if (accountByInsiderAsId) {
            console.log('Found payment account via user->email->insider->id path:', accountByInsiderAsId);
            return accountByInsiderAsId as InsiderPaymentAccount;
          }
        }
      }

      console.log(`No payment account found for user ID: ${userIdOrEmail}`);
      return null;
    }
  } catch (error) {
    console.error('Error in getPaymentAccountForUser:', error);
    return null;
  }
}
