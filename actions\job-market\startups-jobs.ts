'use server';

import { createClient } from '@/utils/supabase/server';
import type {
  StartupJob,
  PaginatedResponse,
  StartupJobFormData
} from '@/types/startups';
import { revalidatePath } from 'next/cache';

interface GetStartupJobsParams {
  page?: number;
  limit?: number;
  search?: string;
  location?: string;
  term?: string;
  sponsorship?: string;
  active?: boolean;
}

export async function getStartupJobs({
  page = 1,
  limit = 20,
  search = '',
  location = '',
  term = '',
  sponsorship = '',
  active = true
}: GetStartupJobsParams): Promise<PaginatedResponse<StartupJob>> {
  const supabase = createClient();
  const offset = (page - 1) * limit;

  // Create a query that joins startup_jobs with startup_companies to get logos
  let query = supabase.from('startup_jobs').select(
    `
      *,
      startup_companies!inner(id, name, logo_url)
    `,
    { count: 'exact' }
  );

  // Apply filters
  if (search) {
    query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
  }

  if (location) {
    query = query.eq('location', location);
  }

  if (term) {
    query = query.eq('term', term);
  }

  if (sponsorship) {
    query = query.eq('sponsorship', sponsorship);
  }

  if (active) {
    query = query.eq('live', true);
  }

  // Apply pagination
  query = query
    .range(offset, offset + limit - 1)
    .order('created_at', { ascending: false });

  const { data, error, count } = await query;

  if (error) {
    console.error('Error fetching startup jobs:', error);
    throw new Error('Failed to fetch startup jobs');
  }

  // Process the data to extract the company information from the nested startup_companies
  const processedData = data?.map((item) => {
    const processedItem = { ...item };

    // Extract company info from startup_companies if available
    if (item.startup_companies) {
      processedItem.startup_company = {
        id: item.startup_companies.id,
        name: item.startup_companies.name,
        logo_url: item.startup_companies.logo_url
      };
    }

    // Remove the nested startup_companies object
    delete processedItem.startup_companies;

    return processedItem;
  });

  return {
    data: processedData as StartupJob[],
    count: count || 0,
    currentPage: page,
    totalPages: count ? Math.ceil(count / limit) : 0
  };
}

export async function getStartupJobById(
  id: string
): Promise<StartupJob | null> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_jobs')
    .select(
      `
      *,
      startup_companies!inner(id, name, logo_url)
    `
    )
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching startup job:', error);
    return null;
  }

  // Process the data to extract company information
  if (data) {
    const processedJob = { ...data };

    if (data.startup_companies) {
      processedJob.startup_company = {
        id: data.startup_companies.id,
        name: data.startup_companies.name,
        logo_url: data.startup_companies.logo_url
      };
      delete processedJob.startup_companies;
    }

    return processedJob as StartupJob;
  }

  return null;
}

/**
 * Get filter options for startup jobs
 * This fetches unique terms, locations, and sponsorship options for the filter dropdowns
 */
export async function getStartupJobsFilterOptions(): Promise<{
  locations: string[];
  terms: string[];
  sponsorships: string[];
  workTypes: string[];
}> {
  const supabase = createClient();

  // Fetch unique locations
  const { data: locations, error: locationsError } = await supabase
    .from('startup_jobs')
    .select('location')
    .not('location', 'is', null)
    .order('location')
    .limit(100);

  if (locationsError) {
    console.error('Error fetching location options:', locationsError);
    throw new Error('Failed to fetch location options');
  }

  // Fetch unique terms
  const { data: terms, error: termsError } = await supabase
    .from('startup_jobs')
    .select('term')
    .not('term', 'is', null)
    .order('term')
    .limit(100);

  if (termsError) {
    console.error('Error fetching term options:', termsError);
    throw new Error('Failed to fetch term options');
  }

  // Fetch unique sponsorship options
  const { data: sponsorships, error: sponsorshipsError } = await supabase
    .from('startup_jobs')
    .select('sponsorship')
    .not('sponsorship', 'is', null)
    .order('sponsorship')
    .limit(100);

  if (sponsorshipsError) {
    console.error('Error fetching sponsorship options:', sponsorshipsError);
    throw new Error('Failed to fetch sponsorship options');
  }

  // Fetch unique work types
  const { data: workTypes, error: workTypesError } = await supabase
    .from('startup_jobs')
    .select('work_type')
    .not('work_type', 'is', null)
    .order('work_type')
    .limit(100);

  if (workTypesError) {
    console.error('Error fetching work type options:', workTypesError);
    throw new Error('Failed to fetch work type options');
  }

  // Extract unique values and filter out null/undefined
  const uniqueLocations = Array.from(
    new Set(locations.map((item) => item.location))
  )
    .filter(Boolean)
    .sort() as string[];

  const uniqueTerms = Array.from(new Set(terms.map((item) => item.term)))
    .filter(Boolean)
    .sort() as string[];

  const uniqueSponsorships = Array.from(
    new Set(sponsorships.map((item) => item.sponsorship))
  )
    .filter(Boolean)
    .sort() as string[];

  const uniqueWorkTypes = Array.from(
    new Set(workTypes.map((item) => item.work_type))
  )
    .filter(Boolean)
    .sort() as string[];

  return {
    locations: uniqueLocations,
    terms: uniqueTerms,
    sponsorships: uniqueSponsorships,
    workTypes: uniqueWorkTypes
  };
}

/**
 * Create a new startup job
 */
export async function createStartupJob(
  jobData: StartupJobFormData
): Promise<StartupJob> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_jobs')
    .insert({
      ...jobData,
      created_at: new Date().toISOString(),
      modified_at: new Date().toISOString()
    })
    .select();

  if (error) {
    console.error('Error creating startup job:', error);
    throw new Error('Failed to create startup job');
  }

  revalidatePath('/startups');
  revalidatePath('/admin/startup/jobs');

  return data[0] as StartupJob;
}

/**
 * Update an existing startup job
 */
export async function updateStartupJob(
  id: string,
  jobData: Partial<StartupJobFormData>
): Promise<StartupJob> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_jobs')
    .update({
      ...jobData,
      modified_at: new Date().toISOString()
    })
    .eq('id', id)
    .select();

  if (error) {
    console.error('Error updating startup job:', error);
    throw new Error('Failed to update startup job');
  }

  revalidatePath('/startups');
  revalidatePath('/admin/startup/jobs');

  return data[0] as StartupJob;
}

/**
 * Delete a startup job
 */
export async function deleteStartupJob(
  id: string
): Promise<{ success: boolean }> {
  const supabase = createClient();

  const { error } = await supabase.from('startup_jobs').delete().eq('id', id);

  if (error) {
    console.error('Error deleting startup job:', error);
    throw new Error('Failed to delete startup job');
  }

  revalidatePath('/startups');
  revalidatePath('/admin/startup/jobs');

  return { success: true };
}

/**
 * Get all available startup companies for the job creation form
 */
export async function getStartupCompanies() {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('startup_companies')
    .select('id, name, logo_url')
    .order('name');

  if (error) {
    console.error('Error fetching startup companies:', error);
    throw new Error('Failed to fetch startup companies');
  }

  return data;
}
