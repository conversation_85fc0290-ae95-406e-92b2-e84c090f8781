'use client';

import { useEffect, useState } from 'react';
import {
  getStartupJobById,
  getSimilarJobs
} from '@/actions/job-market/startups';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Building2,
  Briefcase,
  MapPin,
  LinkedinIcon,
  Mail,
  ExternalLink,
  DollarSign,
  Calendar,
  Info
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Loader from '@/components/loader';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import * as z from 'zod';
import { StartupApplication } from '@/types/startups';
import { getApplicationsForUser } from '@/actions/job-market/startup-applications';
import { useUser } from '@/hooks/useUser';
import { EasyApplyForm } from '@/components/job-market/easy-apply-form';
import { ExternalApplyButton } from '@/components/job-market/external-apply-button';

function getTimeAgo(dateString: string) {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) return '1 day ago';
  if (diffDays < 7) return `${diffDays} days ago`;
  if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
}

// Define the form schema with validation
const easyApplyFormSchema = z
  .object({
    resume: z.string().optional(),
    useExistingResume: z.boolean().default(true),
    coverNote: z.string().optional(),
    yoe_internship: z.string().optional(),
    yoe_job: z.string().optional()
  })
  .refine(
    (data) => {
      // Validate that either useExistingResume is true OR resume is provided
      return (
        data.useExistingResume === true ||
        (data.resume && data.resume.trim() !== '')
      );
    },
    {
      message: 'Please provide a resume link or use your existing resume',
      path: ['resume']
    }
  );

type EasyApplyFormValues = z.infer<typeof easyApplyFormSchema>;

function JobDetailsContent({ id }: { id: string }) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [job, setJob] = useState<any>(null);
  const [similarJobs, setSimilarJobs] = useState<any[]>([]);
  const [isEasyApplyOpen, setIsEasyApplyOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);
  const [applicationData, setApplicationData] =
    useState<Partial<StartupApplication> | null>(null);
  const [isViewApplicationOpen, setIsViewApplicationOpen] = useState(false);
  const { user, loading: userLoading } = useUser();

  // First useEffect: Load job and similar jobs data
  useEffect(() => {
    const fetchJobData = async () => {
      try {
        setIsLoading(true);
        const [jobData, similarJobsData] = await Promise.all([
          getStartupJobById(id),
          getSimilarJobs(id, 3)
        ]);
        setJob(jobData);
        setSimilarJobs(similarJobsData);
      } catch (err) {
        setError(
          err instanceof Error ? err : new Error('Failed to fetch job data')
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchJobData();
  }, [id]);

  // Check application status when user data is loaded
  useEffect(() => {
    const checkApplicationStatus = async () => {
      if (!user?.email) return;

      try {
        // Fetch all user applications
        const applications = await getApplicationsForUser(user.email);

        // Find if user has already applied for this job
        const existingApplication = applications.find((app) => app.job === id);

        if (existingApplication) {
          setApplicationData(existingApplication);
          setHasApplied(true);
        }
      } catch (error) {
        console.error('Error checking application status:', error);
      }
    };

    if (!userLoading && user && job) {
      checkApplicationStatus();
    }
  }, [id, job, user, userLoading]);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Alert variant="destructive" className="max-w-lg">
          <AlertDescription className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            We encountered an error while loading the job details. Please try
            again later or contact support if the problem persists.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center">
        <Loader text="Loading job details..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-12 max-w-5xl">
        <Link
          href="/candidate/startup"
          className="inline-flex items-center text-sm text-gray-600 hover:text-[#36BA98] mb-8 transition-colors"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Jobs
        </Link>

        <div className="grid gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-8">
            <Card className="p-8 shadow-lg bg-gradient-to-br from-[#36BA98] to-[#2da885]">
              <div className="flex items-start gap-6">
                <div className="w-24 h-24 rounded-xl bg-white shadow-sm relative overflow-hidden flex-shrink-0 border">
                  {job.startup_company?.logo_url ? (
                    <Image
                      src={
                        job.startup_company.logo_url.startsWith('http')
                          ? job.startup_company.logo_url
                          : `https:${job.startup_company.logo_url}`
                      }
                      alt={`${job.startup_company.name} logo`}
                      width={96}
                      height={96}
                      sizes="(max-width: 768px) 96px, 96px"
                      className="object-contain p-2 w-full h-full"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-3xl font-bold bg-white text-[#36BA98]">
                      {job.startup_company?.name?.[0] || '?'}
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Badge
                    variant="secondary"
                    className="bg-white text-[#36BA98] font-medium"
                  >
                    Posted{' '}
                    {getTimeAgo(job.created_at || new Date().toISOString())}
                  </Badge>
                  <h1 className="text-3xl font-bold text-white">{job.title}</h1>
                  <p className="text-xl text-white opacity-90">
                    {job.startup_company?.name}
                  </p>
                </div>
              </div>
            </Card>

            <Card className="p-8 shadow-md bg-white">
              <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                {job.startup_company?.industry && (
                  <div className="flex items-center gap-3">
                    <Building2 className="h-6 w-6 text-[#36BA98]" />
                    <div>
                      <p className="text-sm text-gray-500">Industry</p>
                      <p className="font-medium text-gray-900">
                        {job.startup_company.industry}
                      </p>
                    </div>
                  </div>
                )}
                {job.work_type && (
                  <div className="flex items-center gap-3">
                    <Briefcase className="h-6 w-6 text-[#36BA98]" />
                    <div>
                      <p className="text-sm text-gray-500">Type</p>
                      <p className="font-medium text-gray-900">
                        {job.work_type}
                      </p>
                    </div>
                  </div>
                )}
                {job.location && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-6 w-6 text-[#36BA98]" />
                    <div>
                      <p className="text-sm text-gray-500">Location</p>
                      <p className="font-medium text-gray-900">
                        {job.location}
                      </p>
                    </div>
                  </div>
                )}
                {job.salary && (
                  <div className="flex items-center gap-3">
                    <DollarSign className="h-6 w-6 text-[#36BA98]" />
                    <div>
                      <p className="text-sm text-gray-500">Salary</p>
                      <p className="font-medium text-gray-900">{job.salary}</p>
                    </div>
                  </div>
                )}
                {job.work_mode && (
                  <div className="flex items-center gap-3">
                    <Calendar className="h-6 w-6 text-[#36BA98]" />
                    <div>
                      <p className="text-sm text-gray-500">Work Mode</p>
                      <p className="font-medium text-gray-900">
                        {job.work_mode}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </Card>

            <Card className="p-8 shadow-md">
              <div className="space-y-8">
                <div className="flex flex-wrap gap-2">
                  {job.sponsorship && (
                    <Badge
                      variant="secondary"
                      className="bg-purple-100 text-purple-700"
                    >
                      {job.sponsorship}
                    </Badge>
                  )}
                  {job.linkedin_required && (
                    <Badge
                      variant="secondary"
                      className="bg-blue-100 text-blue-700"
                    >
                      LinkedIn Endorsement Available
                    </Badge>
                  )}
                  {job.other_compensation && (
                    <Badge
                      variant="secondary"
                      className="bg-green-100 text-green-700"
                    >
                      Additional Compensation Available
                    </Badge>
                  )}
                </div>

                <div className="space-y-4">
                  <h2 className="text-2xl font-semibold text-gray-900">
                    About the Role
                  </h2>

                  <div className="prose max-w-none text-gray-600 text-sm leading-relaxed font-sans">
                    {job.description &&
                      job.description
                        .split('\n')
                        .map((paragraph: string, index: number) => (
                          <p key={index} className="mb-4">
                            {paragraph}
                          </p>
                        ))}
                  </div>
                </div>

                {job.required_skills?.length > 0 && (
                  <div className="space-y-4">
                    <h2 className="text-2xl font-semibold text-gray-900">
                      Required Skills
                    </h2>
                    <ul className="list-disc pl-6 space-y-2 text-gray-600">
                      {job.required_skills.map(
                        (skill: string, index: number) => (
                          <li key={index}>{skill}</li>
                        )
                      )}
                    </ul>
                  </div>
                )}

                {job.duties && (
                  <div className="space-y-4">
                    <h2 className="text-2xl font-semibold text-gray-900">
                      Responsibilities
                    </h2>
                    <ul className="list-disc pl-6 space-y-2 text-gray-600">
                      {job.duties.map((duty: string, index: number) => (
                        <li key={index}>{duty}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </Card>
          </div>

          <div className="space-y-8">
            <Card className="p-6 shadow-md">
              <h2 className="text-xl font-semibold mb-4 text-gray-900">
                How to Apply
              </h2>
              <div className="space-y-4">
                {/* Easy Apply Button */}
                {/* <Button
                  className={`w-full ${
                    hasApplied
                      ? 'bg-gray-400 hover:bg-gray-500'
                      : 'bg-[#36BA98] hover:bg-[#2da885]'
                  } text-white`}
                  onClick={() => setIsEasyApplyOpen(true)}
                  disabled={userLoading || isLoading}
                >
                  {hasApplied ? 'Already Applied' : 'Easy Apply'}
                </Button> */}

                {/* Application status indicator if user has applied */}
                {hasApplied && (
                  <div className="rounded-md bg-gray-50 p-3 border border-gray-200">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Badge
                          variant="outline"
                          className="bg-blue-100 text-blue-700"
                        >
                          {applicationData?.status || 'Pending'}
                        </Badge>
                      </div>
                      <div className="ml-3 flex-1 md:flex md:justify-between">
                        <p className="text-sm text-gray-700">
                          You applied for this position on{' '}
                          {applicationData?.creation_date
                            ? new Date(
                                applicationData.creation_date
                              ).toLocaleDateString()
                            : 'recently'}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {job.job_link && (
                  <ExternalApplyButton
                    jobId={id}
                    jobTitle={job.title}
                    jobLink={job.job_link}
                    company={job.startup_company?.id}
                    companyName={job.startup_company?.name}
                    user={user}
                    hasApplied={hasApplied}
                    onApplicationSuccess={(application) => {
                      setApplicationData(application);
                      setHasApplied(true);
                    }}
                    fullWidth={true}
                  />
                )}

                {job.startup_company?.company_linkedin && (
                  <Button
                    variant="outline"
                    className="w-full border-[#36BA98] text-[#36BA98] hover:bg-[#36BA98] hover:text-white"
                  >
                    <Link
                      href={job.startup_company.company_linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center"
                    >
                      <LinkedinIcon className="mr-2 h-4 w-4" />
                      View {job.startup_company.name} on LinkedIn
                    </Link>
                  </Button>
                )}

                {job.startup_company?.company_website && (
                  <Button
                    variant="outline"
                    className="w-full border-[#36BA98] text-[#36BA98] hover:bg-[#36BA98] hover:text-white"
                  >
                    <Link
                      href={job.startup_company.company_website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center"
                    >
                      <Mail className="mr-2 h-4 w-4" />
                      Company Website
                    </Link>
                  </Button>
                )}
              </div>
            </Card>

            {job.startup_company && (
              <Card className="p-6 shadow-md bg-gradient-to-br from-gray-50 to-white">
                <h2 className="text-xl font-semibold mb-4 text-gray-900">
                  About the Company
                </h2>
                <div className="space-y-4">
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {job.startup_company.description}
                  </p>

                  <Separator className="my-4" />

                  <div className="space-y-3">
                    {job.startup_company.legally_incorporated && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Company Status</span>
                        <Badge
                          variant="secondary"
                          className="bg-green-100 text-green-700"
                        >
                          Incorporated
                        </Badge>
                      </div>
                    )}

                    {job.startup_company.funding && (
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Funding Status</span>
                        <Badge
                          variant="secondary"
                          className="bg-purple-100 text-purple-700"
                        >
                          {job.startup_company.funding}
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            )}

            {similarJobs.length > 0 && (
              <Card className="p-6 shadow-md">
                <h2 className="text-xl font-semibold mb-4 text-gray-900">
                  Similar Opportunities
                </h2>
                <div className="space-y-4">
                  {similarJobs.map((similarJob) => (
                    <Link
                      key={similarJob.id}
                      href={`/candidate/startup/${similarJob.id}`}
                    >
                      <div className="group hover:bg-gray-50 p-3 rounded-lg transition-all duration-200">
                        <div className="flex items-start gap-3">
                          <div className="w-12 h-12 rounded-lg bg-white relative overflow-hidden flex-shrink-0 border">
                            {similarJob.startup_company?.logo_url ? (
                              <Image
                                src={
                                  similarJob.startup_company.logo_url.startsWith(
                                    'http'
                                  )
                                    ? similarJob.startup_company.logo_url
                                    : `https:${similarJob.startup_company.logo_url}`
                                }
                                alt={similarJob.startup_company.name || ''}
                                width={48}
                                height={48}
                                sizes="(max-width: 768px) 48px, 48px"
                                className="object-contain p-2 w-full h-full"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center text-lg font-bold bg-gradient-to-br from-[#36BA98] to-[#2da885] text-white">
                                {similarJob.startup_company?.name?.[0] || '?'}
                              </div>
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900 group-hover:text-[#36BA98] transition-colors">
                              {similarJob.title}
                            </h3>
                            <p className="text-sm text-gray-500">
                              {similarJob.startup_company?.name}
                            </p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge
                                variant="secondary"
                                className="text-xs bg-gray-100 text-gray-600"
                              >
                                {similarJob.location}
                              </Badge>
                              {similarJob.salary && (
                                <Badge
                                  variant="secondary"
                                  className="text-xs bg-gray-100 text-gray-600"
                                >
                                  {similarJob.salary}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </Card>
            )}
          </div>
        </div>
      </div>

      {/* Easy Apply Dialog */}
      <Dialog open={isEasyApplyOpen} onOpenChange={setIsEasyApplyOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Easy Apply to {job?.title}</DialogTitle>
            <DialogDescription>
              Fill out this form to apply for the position at{' '}
              {job?.startup_company?.name}.
            </DialogDescription>
          </DialogHeader>

          <EasyApplyForm
            user={user}
            jobId={id}
            jobTitle={job?.title}
            companyId={job?.startup_company?.id}
            companyName={job?.startup_company?.name}
            hasApplied={hasApplied}
            applicationData={applicationData}
            onSubmitSuccess={(application) => {
              setApplicationData(application);
              setHasApplied(true);
            }}
            onClose={() => setIsEasyApplyOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* View Application Dialog */}
      {hasApplied && (
        <Dialog
          open={isViewApplicationOpen}
          onOpenChange={setIsViewApplicationOpen}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Your Application</DialogTitle>
              <DialogDescription>
                You applied for {job?.title} on{' '}
                {applicationData?.creation_date
                  ? new Date(applicationData.creation_date).toLocaleDateString()
                  : 'recently'}
                .
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4 mt-4">
              <div>
                <h4 className="text-sm font-medium">Status</h4>
                <p className="mt-1">{applicationData?.status || 'Pending'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium">Resume</h4>
                <p className="mt-1">
                  <a
                    href={applicationData?.resume || '#'}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    View Resume
                  </a>
                </p>
              </div>
              {/* Display other application details as needed */}
            </div>

            <DialogFooter>
              <Button onClick={() => setIsViewApplicationOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

const ApplyButton = ({
  jobLink,
  companyName
}: {
  jobLink: string;
  companyName?: string;
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleApplyClick = () => {
    setIsDialogOpen(true);
  };

  const handleConfirm = () => {
    setIsDialogOpen(false);
    window.open(jobLink, '_blank', 'noopener,noreferrer');
  };

  return (
    <>
      <Button
        variant="outline"
        className="w-full border-[#36BA98] text-[#36BA98] hover:bg-[#36BA98] hover:text-white"
        onClick={handleApplyClick}
      >
        <ExternalLink className="mr-2 h-4 w-4" />
        Apply on {companyName}
      </Button>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>External Link Notice</DialogTitle>
            <DialogDescription>
              You are about to be redirected to {companyName || "the company's"}{' '}
              external application page. Are you sure you want to continue?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              className="bg-[#36BA98] hover:bg-[#2da885] text-white"
              onClick={handleConfirm}
            >
              Continue to Application
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default function JobDetailsPage({ params }: { params: { id: string } }) {
  return <JobDetailsContent id={params.id} />;
}
