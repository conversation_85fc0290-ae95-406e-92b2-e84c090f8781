# Email Verification Flow

## Overview

This document details the email verification process in the InternUp application. Email verification is a critical security measure that ensures users provide valid email addresses and helps prevent spam accounts.

## Table of Contents

1. [Verification Process Overview](#verification-process-overview)
2. [Verification Code Generation](#verification-code-generation)
3. [Email Delivery](#email-delivery)
4. [Verification Code Entry](#verification-code-entry)
5. [Verification Code Validation](#verification-code-validation)
6. [Resending Verification Codes](#resending-verification-codes)
7. [Verification Status Management](#verification-status-management)
8. [OAuth Users Verification](#oauth-users-verification)
9. [Implementation Details](#implementation-details)

## Verification Process Overview

The email verification flow follows these steps:

1. User signs up with email and password
2. System generates a 6-digit verification code
3. Code is stored in the Supabase database with an expiration time
4. Verification email is sent to the user
5. User enters the code in the verification form
6. System validates the code and updates the user's verification status
7. User is redirected to the appropriate dashboard

## Verification Code Generation

### Process

When a user signs up, the system generates a random 6-digit verification code:

```typescript
// Generate a 6-digit OTP code
const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

// Set expiration time (30 minutes from now)
const verificationExpiration = new Date();
verificationExpiration.setMinutes(verificationExpiration.getMinutes() + 30);
```

### Storage

The verification code and its expiration time are stored in the Supabase database:

```typescript
const { error: supabaseError } = await supabase.from('users').insert({
  id: bubbleResponse.response.user_id,
  email: email,
  user_type: user_type,
  startup_company: startup_company,
  verified: false,
  verification_code: verificationCode,
  verification_expiration: verificationExpiration.toISOString()
});
```

## Email Delivery

### Email Content

The verification email content is customized based on the user type:

```typescript
let subject, body;

if (user_type.toLowerCase() === 'candidate') {
  subject = 'Verify Your InternUp Candidate Account';
  body = `Welcome to InternUp! Your verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
} else if (user_type.toLowerCase() === 'company') {
  subject = 'Verify Your InternUp Company Account';
  body = `Thank you for registering your company with InternUp! Your verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
} else {
  subject = 'Verify Your InternUp Account';
  body = `Welcome to InternUp! Your verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
}
```

### Sending Method

The verification email is sent using the Bubble email service:

```typescript
await bubbleClient.sendEmail(email, subject, body);
```

## Verification Code Entry

### User Interface

After signup, users are presented with a verification form where they can enter the code they received:

```tsx
// From app/(auth_forms)/verify/page.tsx
<form onSubmit={handleSubmit} className="space-y-4">
  <div className="space-y-2">
    <Label htmlFor="otp" className="text-sm font-medium">Verification Code</Label>
    <Input
      id="otp"
      type="text"
      placeholder="Enter 6-digit code"
      value={otp}
      onChange={(e) => setOtp(e.target.value)}
      required
      className="border-[#36BA98]/30 focus:border-[#36BA98] focus:ring-[#36BA98]"
    />
  </div>
  <Button
    type="submit"
    className="w-full bg-[#118073] hover:bg-[#36BA98] text-white transition-all duration-300"
    disabled={isSubmitting}
  >
    {isSubmitting ? 'Verifying...' : 'Verify Email'}
  </Button>
</form>
```

### Form Submission

When the user submits the verification form, the `verifyEmail` function is called:

```typescript
const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  setIsSubmitting(true);

  try {
    const result = await verifyEmail(email, otp);

    if (result.type === 'success') {
      toast({
        title: result.title,
        description: result.description,
      });

      // Redirect to appropriate page
      router.push(result.redirectPath || '/dashboard');
    } else {
      toast({
        variant: "destructive",
        title: result.title,
        description: result.description,
      });
    }
  } catch (error) {
    toast({
      variant: "destructive",
      title: "Error",
      description: "An unexpected error occurred. Please try again.",
    });
  } finally {
    setIsSubmitting(false);
  }
};
```

## Verification Code Validation

### Retrieval and Validation

The `verifyEmail` function retrieves the stored verification code and validates it:

```typescript
export async function verifyEmail(email: string, otp: string) {
  try {
    // Get user data from Supabase to check verification code
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('verification_code, verification_expiration, user_type, startup_company')
      .eq('email', email)
      .single();

    if (error) throw new Error('Error fetching user data');

    // Check if verification code exists and matches
    if (!data || !data.verification_code) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'No verification code found for this email'
      };
    }

    // Check if verification code matches
    if (data.verification_code !== otp) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'Invalid verification code'
      };
    }

    // Check if verification code has expired
    const expirationTime = new Date(data.verification_expiration);
    if (expirationTime < new Date()) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'Verification code has expired. Please request a new one.'
      };
    }

    // Update verification status
    // ...
  } catch (error) {
    // Error handling
  }
}
```

### Status Update

After successful validation, the user's verification status is updated:

```typescript
// Update verification status in Supabase
const { error: updateError } = await supabase
  .from('users')
  .update({
    verified: true,
    // Clear the verification code and expiration after successful verification
    verification_code: null,
    verification_expiration: null
  })
  .eq('email', email);
```

## Resending Verification Codes

### Process

Users can request a new verification code if:
- They didn't receive the original email
- The original code expired
- They made a mistake entering the code

```typescript
export async function resendVerificationCode(email: string) {
  try {
    // Check if user exists
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select('user_type')
      .eq('email', email)
      .single();

    if (error) throw new Error('User not found');

    // Generate a new 6-digit OTP code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Set new expiration time (30 minutes from now)
    const verificationExpiration = new Date();
    verificationExpiration.setMinutes(verificationExpiration.getMinutes() + 30);

    // Update the user record with the new OTP and expiration
    const { error: updateError } = await supabase
      .from('users')
      .update({
        verification_code: verificationCode,
        verification_expiration: verificationExpiration.toISOString()
      })
      .eq('email', email);

    // Prepare and send email
    // ...
  } catch (error) {
    // Error handling
  }
}
```

### User Interface

The verification page includes a "Resend Code" button:

```tsx
<Button
  type="button"
  variant="link"
  onClick={handleResendCode}
  className="text-[#36BA98] hover:text-[#118073] transition-all duration-300"
  disabled={isResending}
>
  {isResending ? 'Sending...' : 'Resend Code'}
</Button>
```

## Verification Status Management

### Checking Verification Status

The application checks the user's verification status before allowing access to protected routes:

```typescript
// Example of verification check in a protected route
export async function getUser(supabase) {
  try {
    // Get user from Supabase auth
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return null;

    // Get additional user data from the users table
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) throw error;

    // Check if user is verified
    if (!data.verified) {
      // Handle unverified user
      // ...
    }

    return data;
  } catch (error) {
    console.error('Error in getUser:', error);
    return null;
  }
}
```

### Verification Indicators

The UI indicates verification status to users:

- Unverified users see a banner prompting them to verify their email
- Verified users have full access to the application

## OAuth Users Verification

### Automatic Verification

Users who sign up with OAuth providers are automatically verified:

```typescript
// From auth-callback-handler.tsx
const { error: upsertError } = await supabase
  .from('users')
  .upsert({
    id: bubbleUserId,
    email: email,
    user_type: existingUser?.user_type || finalUserType,
    avatar_url: data.user.user_metadata?.avatar_url || '',
    verified: true, // OAuth users are pre-verified
    updated_at: new Date().toISOString(),
  }, { onConflict: 'id' });
```

### Rationale

OAuth users are automatically verified because:
1. The OAuth provider has already verified their email address
2. The user has proven ownership of the account by authenticating with the provider
3. This provides a smoother user experience for OAuth users

## Implementation Details

### Database Schema

The users table includes the following verification-related fields:

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users,
  email TEXT UNIQUE NOT NULL,
  verified BOOLEAN DEFAULT FALSE,
  verification_code TEXT,
  verification_expiration TIMESTAMP WITH TIME ZONE,
  -- other fields
);
```

### Security Considerations

- Verification codes expire after 30 minutes
- Codes are cleared from the database after successful verification
- Rate limiting is applied to verification attempts
- Email addresses are validated for format before sending verification emails

---

This documentation provides a detailed explanation of the email verification flow. For implementation details, refer to the source code in the following files:

- `utils/bubble/auth.ts`: Verification functions
- `app/(auth_forms)/verify/page.tsx`: Verification form
- `app/(auth_forms)/signup/page.tsx`: Sign-up form with OTP dialog
