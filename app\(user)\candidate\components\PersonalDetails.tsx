'use client';

import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { personalDetailsSchema, usStates } from '../constants';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ImageUpload } from '@/components/ui/image-upload';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Pencil,
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  FileText,
  Linkedin,
  Check,
  Loader2,
  Upload
} from 'lucide-react';
import { Globe } from './Globe';
import { CandidateUpdateData } from '@/types/user';
import { updateCandidateProfile } from '@/actions/admin/candidate';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { uploadFile } from '@/utils/supabase/storage/client';

interface PersonalDetailsProps {
  user: any;
  localUserData: any;
  setLocalUserData: (data: any) => void;
}

export function PersonalDetails({
  user,
  localUserData,
  setLocalUserData
}: PersonalDetailsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isImageUploading, setIsImageUploading] = useState(false);
  const [uploadingResume, setUploadingResume] = useState(false);
  const [selectedFileName, setSelectedFileName] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const router = useRouter();

  useEffect(() => {
    if (localUserData?.resume_url) {
      setPreviewUrl(localUserData.resume_url);
    }
  }, [localUserData?.resume_url]);

  const personalDetailsForm = useForm<z.infer<typeof personalDetailsSchema>>({
    resolver: zodResolver(personalDetailsSchema),
    defaultValues: {
      firstName: user?.first_name || '',
      lastName: user?.last_name || '',
      preferredName: user?.preferred_name || '',
      nationality: user?.nationality || '',
      email: user?.email || '',
      phone: user?.phone_number || '',
      location: user?.location || '',
      sponsorship: user?.need_sponsorship || '',
      mbti: user?.mbti || '',
      bio: user?.bio || '',
      portfolioLink: user?.portfolio || '',
      linkedIn: user?.linkedin_url || '',
      openToReverseHiring: user?.is_open_for_reverse_hiring || false
    }
  });

  const handleCancel = () => {
    if (user) {
      personalDetailsForm.reset();
      setIsEditing(false);
    }
  };

  // Add a useEffect to reset form values when isEditing changes
  useEffect(() => {
    if (isEditing && user) {
      personalDetailsForm.reset({
        firstName: user?.first_name || '',
        lastName: user?.last_name || '',
        preferredName: user?.preferred_name || '',
        nationality: user?.nationality || '',
        email: user?.email || '',
        phone: user?.phone_number || '',
        location: user?.location || '',
        sponsorship: user?.need_sponsorship || '',
        mbti: user?.mbti || '',
        bio: user?.bio || '',
        portfolioLink: user?.portfolio || '',
        linkedIn: user?.linkedin_url || '',
        openToReverseHiring: user?.is_open_for_reverse_hiring || false
      });
    }
  }, [isEditing, user, personalDetailsForm]);

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (file.type !== 'application/pdf') {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a PDF file',
        variant: 'destructive'
      });
      return;
    }

    // Validate file size (7MB max)
    if (file.size > 7 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Resume file must be less than 7MB',
        variant: 'destructive'
      });
      return;
    }

    setSelectedFileName(file.name);
    setUploadingResume(true);

    try {
      // Use the uploadFile utility to upload the resume
      const result = await uploadFile({
        file: file,
        bucket: 'candidate-resumes',
        folder: `profile_user_${user.id || user.email}`
      });

      if (result.error) {
        throw new Error(result.error);
      }

      // Update user profile with resume URL
      await updateCandidateProfile(user.id, {
        resume_url: result.fileUrl
      });

      // Update local user data
      updateLocalUserData({
        resume_url: result.fileUrl
      });

      toast({
        title: 'Resume uploaded',
        description: 'Your resume has been uploaded successfully',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error uploading resume:', error);
      toast({
        title: 'Upload failed',
        description: 'Failed to upload resume. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setUploadingResume(false);
    }
  };

  async function onSubmit(values: z.infer<typeof personalDetailsSchema>) {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'User ID not found',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const updateData: CandidateUpdateData = {
        first_name: values.firstName,
        last_name: values.lastName,
        email: values.email,
        preferred_name: values.preferredName,
        nationality: values.nationality,
        phone_number: values.phone,
        location: values.location,
        need_sponsorship: values.sponsorship,
        mbti: values.mbti,
        bio: values.bio,
        portfolio: values.portfolioLink,
        linkedin_url: values.linkedIn,
        is_open_for_reverse_hiring: values.openToReverseHiring
      };

      await updateCandidateProfile(user.id, updateData);

      updateLocalUserData(updateData);

      toast({
        title: 'Success',
        description: 'Personal details updated successfully'
      });

      setIsEditing(false);
      router.refresh();
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update profile',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  async function uploadProfileImage(imageUrl: string) {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'User ID not found. Please try again later.',
        variant: 'destructive'
      });
      return;
    }

    setIsImageUploading(true);

    try {
      // Create update data object - empty string is valid for removing an image
      const updateData: CandidateUpdateData = {
        avatar_url: imageUrl
      };

      await updateCandidateProfile(user.id, updateData);
      updateLocalUserData({ avatar_url: imageUrl });

      toast({
        title: 'Success',
        description: imageUrl
          ? 'Profile picture updated successfully'
          : 'Profile picture removed successfully',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error updating profile image:', error);

      let errorMessage = imageUrl
        ? 'Failed to update profile picture'
        : 'Failed to remove profile picture';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast({
        title: 'Operation Failed',
        description: errorMessage,
        variant: 'destructive'
      });

      // Revert local state on failure
      updateLocalUserData({ avatar_url: localUserData?.avatar_url });
    } finally {
      setIsImageUploading(false);
    }
  }

  function updateLocalUserData(newData: any) {
    setLocalUserData((prev: any) => ({
      ...prev,
      ...newData
    }));
  }

  const openResumeInNewTab = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank');
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Personal Details</CardTitle>
            <CardDescription>
              Personal information is required to apply for positions or
              referrals.
            </CardDescription>
          </div>
          {!isEditing && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <Form {...personalDetailsForm}>
            <form
              onSubmit={personalDetailsForm.handleSubmit(onSubmit)}
              className="space-y-6"
            >
              <div className="flex items-center space-x-4">
                <div className="h-24 w-24 relative">
                  <ImageUpload
                    value={localUserData?.avatar_url || ''}
                    onChange={uploadProfileImage}
                    className="h-24 w-24"
                    bucket="user-avatars"
                    folder={`user-${user?.id}`}
                  />
                  {isImageUploading && (
                    <div className="absolute inset-0 bg-background/80 flex items-center justify-center rounded-md">
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="text-xs">Uploading...</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={personalDetailsForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={personalDetailsForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your last name"
                          value={field.value || ''}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={personalDetailsForm.control}
                  name="preferredName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter your preferred name"
                          value={field.value || ''}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={personalDetailsForm.control}
                  name="nationality"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nationality *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your nationality" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="us">United States</SelectItem>
                          <SelectItem value="cn">China</SelectItem>
                          <SelectItem value="in">India</SelectItem>
                          {/* Add more countries */}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={personalDetailsForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email *</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter your email"
                          value={field.value || ''}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={personalDetailsForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          type="tel"
                          placeholder="Enter your phone number"
                          value={field.value || ''}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <FormField
                  control={personalDetailsForm.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select your location" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {usStates.map((state) => (
                            <SelectItem key={state.value} value={state.value}>
                              {state.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={personalDetailsForm.control}
                  name="sponsorship"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sponsorship *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select sponsorship status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="citizen">US Citizen</SelectItem>
                          <SelectItem value="green-card">Green Card</SelectItem>
                          <SelectItem value="h1b">
                            H1B Sponsor Likely
                          </SelectItem>
                          <SelectItem value="opt">OPT/CPT Available</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={personalDetailsForm.control}
                name="bio"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bio *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Write a brief introduction about yourself..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Your bio will be displayed on recruiter's dashboard
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={personalDetailsForm.control}
                    name="portfolioLink"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Portfolio Link</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://your-portfolio.com"
                            value={field.value || ''}
                            onChange={field.onChange}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-sm flex items-center gap-1">
                      Resume
                    </FormLabel>
                    {localUserData?.resume_url && (
                      <Badge
                        variant="outline"
                        className="bg-green-50 text-green-700"
                      >
                        Resume Available
                      </Badge>
                    )}
                  </div>

                  {localUserData?.resume_url && (
                    <div className="p-3 border rounded-md bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 flex-shrink-0 bg-blue-100 rounded-md flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-blue-700"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </div>
                        <div className="flex-1 overflow-hidden">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {localUserData.resume_url.split('/').pop() ||
                              'Your Resume'}
                          </p>
                          <p className="text-xs text-gray-500">
                            Current resume
                          </p>
                        </div>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="text-blue-600"
                            >
                              Preview
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-4xl h-[80vh]">
                            <DialogHeader>
                              <DialogTitle>Resume Preview</DialogTitle>
                            </DialogHeader>
                            <div className="relative w-full h-full min-h-[60vh] border rounded">
                              <iframe
                                src={localUserData.resume_url}
                                className="absolute inset-0 w-full h-full"
                                title="Resume Preview"
                              />
                            </div>
                            <div className="flex justify-end">
                              <Button
                                type="button"
                                onClick={openResumeInNewTab}
                                className="bg-[#118073] hover:bg-[#118073]/90"
                              >
                                Open in New Tab
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  )}

                  <FormItem className="mb-1 mt-2">
                    <FormControl>
                      <div className="flex gap-2">
                        <input
                          type="file"
                          ref={fileInputRef}
                          accept=".pdf"
                          onChange={handleFileChange}
                          className="hidden"
                        />
                        <div className="flex-1 px-3 py-2 border rounded-md text-sm">
                          {selectedFileName || 'No file selected'}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleUploadClick}
                          disabled={uploadingResume}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          {uploadingResume
                            ? 'Uploading...'
                            : localUserData?.resume_url
                              ? 'Update Resume'
                              : 'Upload Resume'}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="text-xs">
                      Please upload your resume (PDF only, max 7MB)
                    </FormDescription>
                  </FormItem>
                </div>
              </div>

              <FormField
                control={personalDetailsForm.control}
                name="linkedIn"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>LinkedIn *</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://linkedin.com/in/your-profile"
                        value={field.value || ''}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={personalDetailsForm.control}
                name="openToReverseHiring"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Open for Reverse Hiring</FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <div className="flex gap-2 justify-end">
                <Button type="button" variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#118073] hover:bg-[#118073]/90"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <Avatar className="h-24 w-24 relative">
                <AvatarImage
                  src={
                    localUserData?.avatar_url ||
                    '/placeholder.svg?height=96&width=96'
                  }
                  alt="Profile picture"
                  className={isImageUploading ? 'opacity-50' : ''}
                />
                <AvatarFallback>
                  {localUserData?.first_name?.charAt(0)}
                  {localUserData?.last_name?.charAt(0)}
                </AvatarFallback>
                {isImageUploading && (
                  <div className="absolute inset-0 bg-background/80 flex items-center justify-center rounded-full">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                )}
              </Avatar>
              <div>
                <h3 className="text-xl font-medium">
                  {localUserData?.first_name} {localUserData?.last_name}
                  {localUserData?.preferred_name &&
                    ` (${localUserData?.preferred_name})`}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {localUserData?.nationality === 'us'
                    ? 'United States'
                    : localUserData?.nationality === 'cn'
                      ? 'China'
                      : localUserData?.nationality === 'in'
                        ? 'India'
                        : localUserData?.nationality}
                </p>
              </div>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Contact Information
                  </h4>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{localUserData?.email}</span>
                  </div>
                  {localUserData?.phone_number && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{localUserData?.phone_number}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {usStates.find(
                        (state) => state.value === localUserData?.location
                      )?.label || localUserData?.location}
                    </span>
                  </div>
                </div>

                <div className="space-y-1">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Work Status
                  </h4>
                  <div className="flex items-center gap-2">
                    <Briefcase className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {localUserData?.need_sponsorship === 'citizen'
                        ? 'US Citizen'
                        : localUserData?.need_sponsorship === 'green-card'
                          ? 'Green Card'
                          : localUserData?.need_sponsorship === 'h1b'
                            ? 'H1B Sponsor Likely'
                            : localUserData?.need_sponsorship === 'opt'
                              ? 'OPT/CPT Available'
                              : localUserData?.need_sponsorship}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Check className="h-4 w-4 text-muted-foreground" />
                    <span>
                      {localUserData?.is_open_for_reverse_hiring
                        ? 'Open to Reverse Hiring'
                        : 'Not Open to Reverse Hiring'}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-1">
                  <h4 className="text-sm font-medium text-muted-foreground">
                    Professional Links
                  </h4>
                  {localUserData?.resume_url && (
                    <div className="p-3 border rounded-md bg-gray-50 mb-3">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 flex-shrink-0 bg-blue-100 rounded-md flex items-center justify-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-6 w-6 text-blue-700"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                            />
                          </svg>
                        </div>
                        <div className="flex-1 overflow-hidden">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {localUserData.resume_url.split('/').pop() ||
                              'Your Resume'}
                          </p>
                          <p className="text-xs text-gray-500">
                            Will be used for job applications
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="text-blue-600"
                              >
                                Preview
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-4xl h-[80vh]">
                              <DialogHeader>
                                <DialogTitle>Resume Preview</DialogTitle>
                              </DialogHeader>
                              <div className="relative w-full h-full min-h-[60vh] border rounded">
                                <iframe
                                  src={localUserData.resume_url}
                                  className="absolute inset-0 w-full h-full"
                                  title="Resume Preview"
                                />
                              </div>
                              <div className="flex justify-end">
                                <Button
                                  type="button"
                                  onClick={openResumeInNewTab}
                                  className="bg-[#118073] hover:bg-[#118073]/90"
                                >
                                  Open in New Tab
                                </Button>
                              </div>
                            </DialogContent>
                          </Dialog>
                          <a
                            href={localUserData.resume_url}
                            download
                            className="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 flex items-center rounded-md border border-blue-200 hover:bg-blue-50"
                          >
                            Download
                          </a>
                        </div>
                      </div>
                    </div>
                  )}
                  {localUserData?.portfolio && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={localUserData?.portfolio}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-[#118073] hover:text-[#118073]/90"
                      >
                        {localUserData?.portfolio}
                      </a>
                    </div>
                  )}
                  <div className="flex items-center gap-2">
                    <Linkedin className="h-4 w-4 text-muted-foreground" />
                    <a
                      href={localUserData?.linkedin_url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#118073] hover:text-[#118073]/90"
                    >
                      {localUserData?.linkedin_url}
                    </a>
                  </div>
                </div>

                {localUserData?.mbti && (
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium text-muted-foreground">
                      Personality
                    </h4>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span>MBTI: {localUserData?.mbti}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium text-muted-foreground">Bio</h4>
              <p className="text-sm">{localUserData?.bio}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
