'use server';

import Stripe from 'stripe';
import { stripe, SUBSCRIPTION_PLANS } from '@/utils/stripe/config';
import { createClient } from '@/utils/supabase/client';
import { createOrRetrieveCustomer } from '@/utils/db/stripe-db';
import {
  getURL,
  getErrorRedirect,
  calculateTrialEndUnixTimestamp
} from '@/utils/helpers';
import { getUser } from '../supabase/queries';
import { User } from '@/types/user';
import console, { error } from 'console';

// Define a type for your subscription plans
type SubscriptionPlan = {
  id: string; // Stripe price ID
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  trial_period_days?: number;
  frontend_name?: string;
};

// Define your available subscription plans
// export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
//   {
//     id: 'price_1NzXXX',  // Replace with your actual Stripe Price ID for Essential Access Pass
//     name: 'Essential Access Pass',
//     description: 'For active job seekers',
//     price: 5999, // $59.99 in cents
//     currency: 'usd',
//     interval: 'month', // This will be treated as a one-time payment in the checkout
//     trial_period_days: 0
//   },
//   {
//     id: 'price_1NzYYY',  // Replace with your actual Stripe Price ID for Pro Monthly Pass
//     name: 'Pro Monthly Pass',
//     description: 'For committed career builders',
//     price: 3999, // $39.99 in cents
//     currency: 'usd',
//     interval: 'month',
//     trial_period_days: 7
//   },
//   {
//     id: 'price_1NzZZZ',  // Replace with your actual Stripe Price ID for Elite Semi-Annual Pass
//     name: 'Elite Semi-Annual Pass',
//     description: 'For ambitious professionals seeking quick results',
//     price: 12999, // $129.99 in cents
//     currency: 'usd',
//     interval: 'month', // You'll need to set this up as a 6-month recurring subscription in Stripe
//     trial_period_days: 7
//   },
//   {
//     id: 'price_1NzAAA',  // Replace with your actual Stripe Price ID for Ultimate Annual Pass
//     name: 'Ultimate Annual Pass',
//     description: 'For growth-focused professionals',
//     price: 19999, // $199.99 in cents
//     currency: 'usd',
//     interval: 'year',
//     trial_period_days: 7
//   }
// ];

type CheckoutResponse = {
  errorRedirect?: string;
  sessionUrl?: string | null;
};

export async function checkoutWithStripe(
  priceId: string,
  user: User
): Promise<CheckoutResponse> {
  try {
    // Find the plan that matches the priceId
    const selectedPlan = SUBSCRIPTION_PLANS.find((plan) => plan.id === priceId);
    if (!selectedPlan) {
      throw new Error('Invalid subscription plan selected.');
    }

    // Retrieve or create the customer in Stripe
    let customer: string;
    try {
      customer = await createOrRetrieveCustomer({
        uuid: user.id || '',
        email: user.email || ''
      });
    } catch (err) {
      console.error(err);
      throw new Error('Unable to access customer record.');
    }

    // All plans are now subscriptions, no one-time payments
    const isSubscription = true;
    
    // Set up common params
    let params: Stripe.Checkout.SessionCreateParams = {
      allow_promotion_codes: true,
      billing_address_collection: 'required',
      customer,
      customer_update: {
        address: 'auto'
      },
      line_items: [
        {
          price: selectedPlan.id,
          quantity: 1
        }
      ],
      cancel_url: getURL(),
      success_url: getURL('/candidate'),
      mode: 'subscription', // All plans are now subscriptions
      metadata: {
        userId: user.id,
        userEmail: user.email,
        plan: selectedPlan.name
      }
    };

    // Add subscription data for all plans (no trial periods)
    params.subscription_data = {
      metadata: {
        userId: user.id,
        userEmail: user.email,
        plan: selectedPlan.name
      }
    };

    // Create a checkout session in Stripe
    let session;
    try {
      session = await stripe.checkout.sessions.create(params);
    } catch (err) {
      console.error(err);
      throw new Error('Unable to create checkout session.');
    }

    // Instead of returning a Response, just return the data or error.
    if (session) {
      return { sessionUrl: session.url };
    } else {
      throw new Error('Unable to create checkout session.');
    }
  } catch (error) {
    if (error instanceof Error) {
      return {
        errorRedirect: getErrorRedirect(
          '/account',
          error.message,
          'Please try again later or contact a system administrator.'
        )
      };
    } else {
      return {
        errorRedirect: getErrorRedirect(
          '/account',
          'An unknown error occurred.',
          'Please try again later or contact a system administrator.'
        )
      };
    }
  }
}

export async function createStripePortal(
  currentPath: string,
  userId: string,
  userEmail: string
) {
  try {
    if (!userId || !userEmail) {
      if (error) {
        console.error(error);
      }
      throw new Error('Could not get user session.');
    }

    let customer;
    try {
      customer = await createOrRetrieveCustomer({
        uuid: userId || '',
        email: userEmail || ''
      });
    } catch (err) {
      console.error(err);
      throw new Error('Unable to access customer record.');
    }

    if (!customer) {
      throw new Error('Could not get customer.');
    }

    // First check if the customer has any subscriptions
    const subscriptions = await stripe.subscriptions.list({
      customer,
      limit: 1,
      status: 'all' // Include all subscriptions (active, past, canceled)
    });
    
    // If customer has no subscription history, return a message
    if (subscriptions.data.length === 0) {
      console.log('Customer has no subscription history');
      return getErrorRedirect(
        currentPath,
        'No subscription found',
        'You don\'t have any subscriptions to manage.'
      );
    }
    
    try {
      const { url } = await stripe.billingPortal.sessions.create({
        customer,
        return_url: getURL('/candidate/membership')
      });
      if (!url) {
        throw new Error('Could not create billing portal');
      }
      return url;
    } catch (err) {
      console.error(err);
      throw new Error('Could not create billing portal');
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error(error);
      return getErrorRedirect(
        currentPath,
        error.message,
        'Please try again later or contact a system administrator.'
      );
    } else {
      return getErrorRedirect(
        currentPath,
        'An unknown error occurred.',
        'Please try again later or contact a system administrator.'
      );
    }
  }
}

// Get user's active subscription
export async function getUserSubscription(userId: string) {
  const supabase = createClient();

  // First, try to find by owner_id
  const { data, error } = await supabase
    .from('subscriptions')
    .select('*')
    .eq('owner_id', userId)
    .eq('status', 'active')
    .order('creation_date', { ascending: false })
    .limit(1)
    .single();
  console.log('Found subscription by owner_id:', data);
  // console.log('Error finding subscription by owner_id:', error);
  if (error) {
    // console.error('Error finding subscription by owner_id:', error);

    // Only if we didn't find a subscription by owner_id, try by owner_email
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('id', userId)
      .single();

    if (userError || !userData) {
      console.error('Error finding user:', userError);
      return null;
    }

    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('owner_email', userData.email)
      .eq('status', 'active')
      .order('creation_date', { ascending: false })
      .limit(1)
      .single();

    // if (subscriptionError) {
    //   console.error('Error fetching subscription by email:', subscriptionError);
    //   return null;
    // }

    return subscriptionData;
  }

  // console.log('Found subscription by owner_id:', data);
  // return data;
}

// Cancel a subscription
export async function cancelSubscription(subscriptionId: string) {
  try {
    const { stripe } = await import('@/utils/stripe/config');
    const supabase = createClient();

    // Get the subscription from Stripe
    const stripeSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: true
      }
    );

    // Update the subscription in the database
    const { data, error } = await supabase
      .from('subscriptions')
      .update({
        status: 'active', // Keep as active until the period ends
        end_date: new Date(
          stripeSubscription.current_period_end * 1000
        ).toISOString(),
        modified_date: new Date().toISOString()
      })
      .eq('subscription_id', subscriptionId)
      .select()
      .single();

    // if (error) {
    //   console.error('Error updating subscription in database:', error);
    //   throw error;
    // }

    return {
      success: true,
      subscription: data
    };
  } catch (error) {
    // console.error('Error canceling subscription:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
