'use server';

import { createClient } from '@/utils/supabase/server';
import { DailyJob } from '@/types/crawler';

/**
 * Get all download tracking records
 */
export async function getDownloadTracking() {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('download_file_tracker')
    .select(
      `
      id,
      user_id,
      file_id,
      clicked_at,
      users (
        email,
        vip
      ),
      daily_jobs (
        file_type,
        notice,
        posting_date
      )
    `
    )
    .order('clicked_at', { ascending: false });

  if (error) {
    console.error('Error fetching download tracking:', error);
    return [];
  }

  return data || [];
}

/**
 * Get most viewed files
 */
export async function getMostViewedFiles(limit = 10): Promise<DailyJob[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('daily_jobs')
    .select('*')
    .order('views_count', { ascending: false })
    .limit(limit);

  if (error) {
    console.error('Error fetching most viewed files:', error);
    return [];
  }

  return data || [];
}

/**
 * Get download counts by day
 */
export async function getDownloadCountsByDay(days = 30) {
  const supabase = createClient();

  // Create a date that's 'days' days ago
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  startDate.setHours(0, 0, 0, 0);

  const { data, error } = await supabase
    .from('download_file_tracker')
    .select('clicked_at')
    .gte('clicked_at', startDate.toISOString());

  if (error) {
    console.error('Error fetching download counts by day:', error);
    return [];
  }

  // Group by day
  const countsByDay: Record<string, number> = {};

  data?.forEach((item) => {
    const date = new Date(item.clicked_at);
    const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD

    if (!countsByDay[dateStr]) {
      countsByDay[dateStr] = 0;
    }

    countsByDay[dateStr]++;
  });

  // Format for return
  return Object.entries(countsByDay).map(([date, count]) => ({
    date,
    count
  }));
}

// Define a type for the job click response from Supabase
interface JobClickWithTracker {
  id: string;
  tracker_id: string;
  keyword: string | null;
  job_title: string | null;
  company: string | null;
  clicked_at: string;
  download_file_tracker: {
    file_id: string;
  };
}

/**
 * Direct database query for job clicks by user ID
 * Uses a simple direct approach to query job clicks efficiently
 */
export async function getJobClicksDirectly(userId: string) {
  const supabase = createClient();

  try {
    // Step 1: Get download trackers for this user directly with a single query
    const { data: userTrackers, error: trackerError } = await supabase
      .from('download_file_tracker')
      .select('id, file_id, user_id')
      .eq('user_id', userId)
      .limit(1000);

    if (trackerError) {
      return [];
    }

    if (!userTrackers || userTrackers.length === 0) {
      return [];
    }

    // Get the tracker IDs
    const trackerIds = userTrackers.map((t) => t.id);

    // Create a map of tracker IDs to file IDs for easier lookup
    const trackerToFileMap = new Map<string, string>();
    userTrackers.forEach((tracker) => {
      if (tracker.id && tracker.file_id) {
        trackerToFileMap.set(tracker.id, tracker.file_id);
      }
    });

    // Step 2: Get job clicks for these trackers directly using an IN query
    const { data: userClicks, error: clickError } = await supabase
      .from('job_click_history')
      .select('id, tracker_id, job_title, company, keyword, clicked_at')
      .in('tracker_id', trackerIds)
      .order('clicked_at', { ascending: false })
      .limit(1000);

    if (clickError) {
      return [];
    }

    // Return the job clicks with proper formatting
    return (userClicks || []).map((click) => ({
      id: click.id,
      job_id: trackerToFileMap.get(click.tracker_id) || click.tracker_id,
      job_title: click.job_title || 'Unknown Title',
      company: click.company || 'Unknown Company',
      keyword: click.keyword || 'Uncategorized',
      click_date: click.clicked_at
    }));
  } catch (error) {
    return [];
  }
}
