'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { getReferralPublicFirmCompanies } from '@/actions/job-market/publicFirms-companies';
import { ReferralRequestButton } from './referral-request-button';
import { v4 as uuidv4 } from 'uuid';
import { useUser } from '@/hooks/useUser';

interface Company {
  id: string;
  name: string;
}

export const StandaloneReferralButton = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const { user } = useUser();

  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const data = await getReferralPublicFirmCompanies();
        setCompanies(data);
      } catch (error) {
        console.error('Error fetching companies:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  if (loading) {
    return (
      <Button disabled size="sm">
        Loading...
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Select
        onValueChange={(value) => {
          const company = companies.find((c) => c.id === value);
          setSelectedCompany(company || null);
        }}
      >
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Select company" />
        </SelectTrigger>
        <SelectContent>
          {companies.map((company) => (
            <SelectItem key={company.id} value={company.id}>
              {company.name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {selectedCompany && (
        <ReferralRequestButton
          jobId={uuidv4()}
          jobTitle=""
          companyName={selectedCompany.name}
          companyId={selectedCompany.id}
          user={user}
          referralLink=""
          hasApplied={false}
        />
      )}
    </div>
  );
};
