"use client"

import { Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckoutButton } from "@/components/membership/checkout-button"
import { SUBSCRIPTION_PLANS } from "@/utils/stripe/config"

interface PricingCardProps {
  name: string
  price: string
  period: "monthly" | "annual" | "semi-annual" | "one-time"
  description: string
  features: readonly string[]
  ctaText: string
  popular?: boolean
  user: any
  loading: boolean
}

export function PricingCard({
  name,
  price,
  period,
  description,
  features,
  ctaText,
  popular = false,
  user,
  loading,
}: PricingCardProps) {
  // Find matching Stripe price ID using frontend_name field
  const stripePlan = SUBSCRIPTION_PLANS?.find((plan) => plan.frontend_name === name)

  return (
    <Card
      className={`flex flex-col h-full transition-all ${popular ? "border-primary shadow-lg scale-105" : "border-border"}`}
    >
      <CardHeader className="pb-6">
        {popular && <Badge className="self-start mb-2">Recommended</Badge>}
        <h3 className="text-xl font-bold">{name}</h3>
        <div className="flex items-baseline mt-2">
          <span className="text-3xl font-bold">{price}</span>
          <span className="ml-1 text-sm text-gray-500">
            {period === "monthly" && "/mo"}
            {period === "annual" && "/year"}
            {period === "semi-annual" && "/6 months"}
            {period === "one-time" && " one-time"}
          </span>
        </div>
        <p className="mt-2 text-sm text-gray-600">{description}</p>
      </CardHeader>

      <CardContent className="flex-grow">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <Check className="flex-shrink-0 w-5 h-5 mr-2 text-green-500" />
              <span className="text-sm">{feature}</span>
            </li>
          ))}
        </ul>
      </CardContent>

      <CardFooter className="pt-6">
        {stripePlan ? (
          <CheckoutButton
            priceId={stripePlan.id}
            ctaText={ctaText}
            variant={popular ? "default" : "outline"}
            className="w-full"
            user={user}
            loading={loading}
          />
        ) : (
          <Button className="w-full" variant={popular ? "default" : "outline"}>
            {ctaText}
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
