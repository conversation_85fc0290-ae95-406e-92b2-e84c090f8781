'use server';

import { createClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';
import type { CapstoneUser, CapstoneUserFormData } from '@/types/capstone-user';
import type {
  CapstoneProject,
  CapstoneProjectFormData
} from '@/types/capstone-project';
import { revalidatePath } from 'next/cache';

// Capstone Users Actions
export async function getCapstoneUsers(): Promise<CapstoneUser[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('capstone_users')
    .select('*')
    .order('creation_date', { ascending: false });

  if (error) {
    console.error('Error fetching capstone users:', error);
    return [];
  }

  return data || [];
}

export async function getCapstoneUser(
  id: string
): Promise<CapstoneUser | null> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('capstone_users')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching capstone user:', error);
    return null;
  }

  return data;
}

export async function updateCapstoneUser(
  id: string,
  formData: CapstoneUserFormData
) {
  const supabase = createClient();

  const { error } = await supabase
    .from('capstone_users')
    .update({
      date_started: formData.date_started
        ? new Date(formData.date_started).toISOString()
        : null,
      end_date: formData.end_date
        ? new Date(formData.end_date).toISOString()
        : null,
      status: formData.status,
      reviewers_feedback: formData.reviewers_feedback,
      modified_date: new Date().toISOString()
    })
    .eq('id', id);

  if (error) {
    console.error('Error updating capstone user:', error);
    return { success: false, error: error.message };
  }

  revalidatePath('/admin/capstone/users');
  return { success: true };
}

export async function deleteCapstoneUser(id: string) {
  const supabase = createClient();

  const { error } = await supabase.from('capstone_users').delete().eq('id', id);

  if (error) {
    console.error('Error deleting capstone user:', error);
    return { success: false, error: error.message };
  }

  revalidatePath('/admin/capstone/users');
  return { success: true };
}

// Capstone Projects Actions
export async function getCapstoneProjects(): Promise<CapstoneProject[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('capstone_projects')
    .select('*')
    .order('creation_date', { ascending: false });

  if (error) {
    console.error('Error fetching capstone projects:', error);
    return [];
  }

  return data || [];
}

export async function getCapstoneProject(
  id: string
): Promise<CapstoneProject | null> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('capstone_projects')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching capstone project:', error);
    return null;
  }

  return data;
}

export async function createCapstoneProject(formData: CapstoneProjectFormData) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('capstone_projects')
    .insert({
      id: uuidv4(), // Generate a UUID for the id field
      project_no: formData.project_no,
      project_title: formData.project_title,
      industry_jobs: formData.industry_jobs,
      mentors: formData.mentors,
      duration_days: formData.duration_days,
      type: formData.type,
      firm_url: formData.firm_url,
      project_description: formData.project_description,
      creation_date: new Date().toISOString(),
      modified_date: new Date().toISOString()
    })
    .select();

  if (error) {
    console.error('Error creating capstone project:', error);
    return { success: false, error: error.message };
  }

  revalidatePath('/admin/capstone/projects');
  return { success: true, data: data[0] };
}

export async function updateCapstoneProject(
  id: string,
  formData: CapstoneProjectFormData
) {
  const supabase = createClient();

  const { error } = await supabase
    .from('capstone_projects')
    .update({
      project_no: formData.project_no,
      project_title: formData.project_title,
      industry_jobs: formData.industry_jobs,
      mentors: formData.mentors,
      duration_days: formData.duration_days,
      type: formData.type,
      firm_url: formData.firm_url,
      project_description: formData.project_description,
      modified_date: new Date().toISOString()
    })
    .eq('id', id);

  if (error) {
    console.error('Error updating capstone project:', error);
    return { success: false, error: error.message };
  }

  revalidatePath('/admin/capstone/projects');
  return { success: true };
}

export async function deleteCapstoneProject(id: string) {
  const supabase = createClient();

  const { error } = await supabase
    .from('capstone_projects')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting capstone project:', error);
    return { success: false, error: error.message };
  }

  revalidatePath('/admin/capstone/projects');
  return { success: true };
}
