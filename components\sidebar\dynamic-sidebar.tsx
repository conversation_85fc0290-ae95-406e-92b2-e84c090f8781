'use client';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  UserCog,
  Briefcase,
  Gem,
  LockKeyhole,
  Rocket,
  LogOut,
  Building2,
  Target,
  ListChecks,
  ShieldCheck,
  Users,
  RadioTower,
  Star,
  BookHeadphones,
  Video,
  Home,
  BookOpen,
  Globe,
  Code,
  SquareCodeIcon,
  UserPlus,
  CreditCard,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { signOut } from '@/utils/bubble/auth'; // Adjust this import path as needed
import { toast } from '@/components/ui/use-toast'; // Adjust this import path as needed
import { GearIcon } from '@radix-ui/react-icons';
import { Square3Stack3DIcon } from '@heroicons/react/24/solid';
import { useEffect, useState } from 'react';
import { getUserSubscription } from '@/utils/stripe/server';
import { useUser } from '@/hooks/useUser';

const roleBasedNavigation = {
  company: [
    { icon: LayoutDashboard, label: 'Dashboard', path: '/company' },
    { icon: Briefcase, label: 'Jobs', path: '/company/posts' },
    { icon: Users, label: 'Applications', path: '/company/applications' },
    { icon: UserPlus, label: 'Candidates', path: '/company/candidates' }
  ],
  admin: [
    { icon: LayoutDashboard, label: 'Dashboard', path: '/admin' },
    { icon: Users, label: 'Users', path: '/admin/users', badge: 3 },
    {
      icon: Building2,
      label: 'Startup Companies',
      path: '/admin/startup/companies'
    },
    { icon: Briefcase, label: 'Startup Jobs', path: '/admin/startup/jobs' },
    {
      icon: LockKeyhole,
      label: 'Startup Applications',
      path: '/admin/startup/applications'
    },
    {
      icon: Target,
      label: 'Public Firm Companies',
      path: '/admin/publicFirms/companies'
    },
    {
      icon: ListChecks,
      label: 'Public Firm Jobs',
      path: '/admin/publicFirms/jobs'
    },
    {
      icon: UserPlus,
      label: 'Public Firm Referrals',
      path: '/admin/publicFirms/referrals'
    },
    { icon: UserCog, label: 'Manage Insiders', path: '/admin/insiders' },
    {
      icon: DollarSign,
      label: 'Insider Payouts',
      path: '/admin/insiders/payouts'
    },
    { icon: RadioTower, label: 'Blogs', path: '/admin/blogs' },
    { icon: Code, label: 'Daily Job Management', path: '/admin/daily-job' },
    {
      icon: Code,
      label: 'Daily Job Analytics',
      path: '/admin/daily-job/analytics'
    },

    {
      icon: Square3Stack3DIcon,
      label: 'Industrial Projects',
      path: '/admin/industrial-project/projects'
    },
    {
      icon: SquareCodeIcon,
      label: 'Industrial Project Users',
      path: '/admin/industrial-project/users'
    },
    { icon: GearIcon, label: 'Account Settings', path: '/admin/account' }
  ],
  candidate: [
    { icon: Home, label: 'My Profile', path: '/candidate' },
    {
      icon: LockKeyhole,
      label: 'Startup Applications',
      path: '/candidate/startup/applications'
    },
    {
      icon: UserPlus,
      label: 'Public Firm Referrals',
      path: '/candidate/public-firms/referrals'
    },

    {
      icon: BookHeadphones,
      label: 'AI Coaching',
      path: '/candidate/ai-coaching'
    },
    {
      icon: Users,
      label: 'Mock Interview',
      path: '/candidate/mock-interview'
    },
    {
      icon: BookOpen,
      label: 'Industrial Project',
      path: '/candidate/industrial-project'
    },
    {
      icon: Globe,
      label: 'Daily Job Posts',
      path: '/daily-job'
    }
  ],
  insider: [
    { icon: LayoutDashboard, label: 'Overview', path: '/insider' },
    {
      icon: Rocket,
      label: 'Referral Requests',
      path: '/insider/referral-requests'
    },
    {
      icon: DollarSign,
      label: 'Payouts',
      path: '/insider/payouts'
    },
    { icon: Gem, label: 'Account Settings', path: '/insider/account-settings' }
  ]
};

export function DynamicSidebar({
  role
}: {
  role: 'admin' | 'candidate' | 'insider' | 'company';
}) {
  const pathname = usePathname();
  const router = useRouter();
  const { user } = useUser();
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get the base navigation items for the role
  let navItems = [...roleBasedNavigation[role]];

  useEffect(() => {
    async function fetchSubscription() {
      if (role === 'candidate' && user?.id) {
        try {
          const sub = await getUserSubscription(user.id);
          setSubscription(sub);
        } catch (error) {
          console.error('Error fetching subscription:', error);
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    }

    fetchSubscription();
  }, [role, user]);

  // Add subscription plan item for candidates
  useEffect(() => {
    if (role === 'candidate') {
      const planItem = {
        icon: CreditCard,
        label: subscription ? 'Manage Plan' : 'Upgrade Plan',
        path: subscription ? '/api/create-portal' : '/candidate/membership'
      };

      // Find the index where we want to insert the plan item (after Web Crawler)
      const webCrawlerIndex = navItems.findIndex(
        (item) => item.path === '/candidate/web-crawler'
      );

      if (webCrawlerIndex !== -1) {
        // Create a new array with the plan item inserted after Web Crawler
        const updatedNavItems = [
          ...navItems.slice(0, webCrawlerIndex + 1),
          planItem,
          ...navItems.slice(webCrawlerIndex + 1)
        ];
        navItems = updatedNavItems;
      } else {
        // If Web Crawler not found, just add to the end
        navItems.push(planItem);
      }
    }
  }, [role, subscription, navItems]);

  const handleSignOut = async () => {
    try {
      const result = await signOut();
      toast({
        title: 'Signed out',
        description: 'See you soon!'
      });
      // Handle redirection if a path is provided and router exists
      if (result.redirectPath && router) {
        router.push(result.redirectPath);
      }
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  return (
    <aside className="fixed h-screen w-[270px] border-r-2 transition-all duration-300 hover:w-[280px]">
      <div className="flex flex-col p-4">
        <div className="mb-6 flex items-center gap-3 px-2">
          <Image
            src="/favicon_new.png"
            alt="InternUp Logo"
            width={30}
            height={30}
          />
          <Link href="/">
            <span className="text-xl font-bold tracking-tighter text-[#36BA98]">
              InternUp
            </span>
          </Link>
        </div>

        <Separator className="mb-6 bg-gradient-to-r from-transparent via-cyan-400/40 to-transparent" />

        <ScrollArea className="h-[calc(100vh-160px)]">
          <nav className="space-y-2 pr-4">
            {navItems.map((item) => {
              const isActive = pathname === item.path;
              const Icon = item.icon;

              return (
                <Link key={item.path} href={item.path}>
                  <Button
                    variant="ghost"
                    className={cn(
                      'h-12 w-full justify-start gap-3 rounded-xl border-2 border-transparent px-4 text-base font-medium transition-all',
                      ' hover:bg-[#36BA98] hover:text-white',
                      isActive ? 'bg-[#118073] text-white' : 'text-black'
                    )}
                  >
                    <Icon className="h-5 w-5 transition-transform group-hover:scale-110" />
                    <span>{item.label}</span>
                    {/* {item.badge && (
                      <span className="ml-auto rounded-full bg-rose-500/80 px-2 py-1 text-xs">
                        {item.badge}
                      </span>
                    )} */}
                  </Button>
                </Link>
              );
            })}
          </nav>
        </ScrollArea>
      </div>
    </aside>
  );
}
