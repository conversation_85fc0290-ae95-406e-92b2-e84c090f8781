'use server';

import { createClient } from '@/utils/supabase/server';
import type { DailyJob, DailyJobFormData } from '@/types/crawler';

import { v4 as uuidv4 } from 'uuid';

export async function getDailyJobs(): Promise<DailyJob[]> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('daily_jobs')
    .select('*')
    .order('posting_date', { ascending: false });

  if (error) {
    console.error('Error fetching daily jobs:', error);
    return [];
  }

  return data || [];
}

export async function getDailyJob(id: string): Promise<DailyJob | null> {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('daily_jobs')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching daily job:', error);
    return null;
  }

  return data;
}

export async function createDailyJob(formData: DailyJobFormData) {
  const supabase = createClient();

  const { data, error } = await supabase
    .from('daily_jobs')
    .insert({
      id: uuidv4(), // Generate a UUID for the id column
      posting_date: formData.posting_date
        ? new Date(formData.posting_date).toISOString()
        : null,
      file_type: formData.file_type,
      notice: formData.notice,
      crawler_file: formData.crawler_file,
      creation_date: new Date().toISOString(),
      modified_date: new Date().toISOString()
    })
    .select();

  if (error) {
    console.error('Error creating daily job:', error);
    return { success: false, error: error.message };
  }

  // revalidatePath("/admin/web_crawler")
  return { success: true, data: data[0] };
}

export async function updateDailyJob(id: string, formData: DailyJobFormData) {
  const supabase = createClient();

  const { error } = await supabase
    .from('daily_jobs')
    .update({
      posting_date: formData.posting_date
        ? new Date(formData.posting_date).toISOString()
        : null,
      file_type: formData.file_type,
      notice: formData.notice,
      crawler_file: formData.crawler_file,
      modified_date: new Date().toISOString()
    })
    .eq('id', id);

  if (error) {
    console.error('Error updating daily job:', error);
    return { success: false, error: error.message };
  }

  // revalidatePath("/admin/web_crawler")
  return { success: true };
}

export async function deleteDailyJob(id: string) {
  const supabase = createClient();

  const { error } = await supabase.from('daily_jobs').delete().eq('id', id);

  if (error) {
    console.error('Error deleting daily job:', error);
    return { success: false, error: error.message };
  }

  // revalidatePath("/admin/web_crawler")
  return { success: true };
}

/**
 * Track a user's download of a daily job file
 * @param jobId - The ID of the job file being downloaded
 * @param userId - The ID of the user downloading the file
 * @param isVip - Whether the user is a VIP or not
 * @returns Success status and updated job data
 */
export async function trackFileDownload(
  jobId: string,
  userId: string,
  isVip: boolean
): Promise<{ success: boolean; data?: DailyJob; error?: string }> {
  const supabase = createClient();

  try {
    // First, get the current job to access the download arrays
    const { data: job, error: fetchError } = await supabase
      .from('daily_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (fetchError) {
      console.error('Error fetching job for download tracking:', fetchError);
      return { success: false, error: fetchError.message };
    }

    if (!job) {
      return { success: false, error: 'Job not found' };
    }

    // Determine which array to update based on VIP status
    const arrayField = isVip ? 'downloader_vip' : 'downloader_nonvip';

    // Create a new array with the user ID added (if not already present)
    const currentArray = job[arrayField] || [];

    // Check if user already downloaded this file
    if (currentArray.includes(userId)) {
      // User already downloaded, no need to update
      return { success: true, data: job };
    }

    // Add user to the download array
    const updatedArray = [...currentArray, userId];

    // Update the database
    const { data: updatedJob, error: updateError } = await supabase
      .from('daily_jobs')
      .update({
        [arrayField]: updatedArray,
        modified_date: new Date().toISOString()
      })
      .eq('id', jobId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating download tracking:', updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true, data: updatedJob };
  } catch (error) {
    console.error('Error in trackFileDownload:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error tracking download'
    };
  }
}

/**
 * Tracks when a user views or downloads a job file
 * This function both adds an entry to the download_file_tracker table
 * and increments the views count on the daily_jobs table
 *
 * @param jobId - The ID of the job file being viewed
 * @param userId - The ID of the user viewing the file
 * @param isVip - Whether the user is a VIP or not
 * @returns Success status and error information if applicable
 */
export async function trackFileView(
  jobId: string,
  userId: string,
  isVip: boolean
): Promise<{ success: boolean; error?: string }> {
  // Using normal client for regular operations
  const supabase = createClient();

  try {
    // Use a direct upsert approach with ON CONFLICT DO UPDATE
    const { error: upsertError } = await supabase.rpc('upsert_file_tracker', {
      p_user_id: userId,
      p_file_id: jobId,
      p_timestamp: new Date().toISOString()
    });

    if (upsertError) {
      console.error('Error upserting tracking record:', upsertError);
      // Continue execution even if this fails - we'll still update the view counts
    }

    // 2. Increment the appropriate counter in the daily_jobs table
    const counterField = isVip ? 'vip_count' : 'nonvip_count';
    // Use the appropriate array field based on VIP status
    const arrayField = isVip ? 'downloader_vip' : 'downloader_nonvip';

    // First get the current job to access the current counter values
    const { data: job, error: jobFetchError } = await supabase
      .from('daily_jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (jobFetchError) {
      console.error('Error fetching job for counter update:', jobFetchError);
      return { success: false, error: jobFetchError.message };
    }

    if (!job) {
      return { success: false, error: 'Job not found' };
    }

    // Increment the counters (handle null values)
    const currentVipOrNonVipCount =
      (job[counterField as keyof typeof job] as number) || 0;
    const currentViewsCount = (job.views_count as number) || 0;

    // Get the current download array and add the user if not already present
    const currentArray =
      (job[arrayField as keyof typeof job] as string[]) || [];
    let updatedArray = currentArray;

    // Only add the user to the array if they're not already in it
    if (!currentArray.includes(userId)) {
      updatedArray = [...currentArray, userId];
    }

    // Update the counters and the appropriate downloader array
    const { error: updateError } = await supabase
      .from('daily_jobs')
      .update({
        [counterField]: currentVipOrNonVipCount + 1,
        views_count: currentViewsCount + 1,
        [arrayField]: updatedArray,
        modified_date: new Date().toISOString()
      })
      .eq('id', jobId);

    if (updateError) {
      console.error('Error updating job counters:', updateError);
      return { success: false, error: updateError.message };
    }

    return { success: true };
  } catch (error) {
    console.error('Exception in trackFileView:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error tracking file view'
    };
  }
}

/**
 * Gets only daily jobs that were created on or after May 11th, 2025
 * These are the only jobs that have statistics tracking enabled
 */
export async function getStatisticsEnabledJobs(): Promise<DailyJob[]> {
  const supabase = createClient();

  // Target date: May 11th, 2025
  const statisticsEnabledDate = new Date('2025-05-11T00:00:00Z');

  const { data, error } = await supabase
    .from('daily_jobs')
    .select('*')
    .gte('creation_date', statisticsEnabledDate.toISOString())
    .order('posting_date', { ascending: false });

  if (error) {
    console.error('Error fetching statistics-enabled jobs:', error);
    return [];
  }

  return data || [];
}

/**
 * Gets user details for analytics dashboard
 * @param userIds Array of user IDs to fetch details for
 * @returns Array of user details with basic information
 */
export async function getUserDetailsForAnalytics(userIds: string[]) {
  const supabase = createClient();

  if (userIds.length === 0) return [];

  try {
    const { data, error } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, vip')
      .in('id', userIds);

    if (error) {
      console.error('Error fetching user details for analytics:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getUserDetailsForAnalytics:', error);
    return [];
  }
}
