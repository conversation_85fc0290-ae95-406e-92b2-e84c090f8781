"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/hooks/useUser"
import { useToast } from "@/components/ui/use-toast"
import { DynamicSidebar } from "@/components/sidebar/dynamic-sidebar"
import { UserNavigation } from "@/components/user-navigation"

export default function CompanyLayout({ children }: { children: React.ReactNode }) {
  const { user, loading } = useUser()
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    // Check if user is logged in and is a company
    if (!loading && user && user.user_type?.toLowerCase() !== "company") {
      toast({
        title: "Access denied",
        description: "You don't have access to the company area",
        variant: "destructive",
      })
      router.push("/signin")
      return
    }

    // Check if user is verified
    if (!loading && user && user.user_type?.toLowerCase() === "company" && user.verified === false) {
      toast({
        title: "Verification required",
        description: "Please verify your email to access the company area",
        variant: "destructive",
      })
      router.push(`/verify?email=${encodeURIComponent(user.email)}&userType=company`)
    }
  }, [user, loading, router, toast])

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  if (!user || user.user_type?.toLowerCase() !== "company") {
    return null
  }

  // Don't render the layout if user is not verified
  if (user.verified === false) {
    return null
  }

  return (
    <div className="flex min-h-screen">
      <DynamicSidebar role="company" />
      <div className="flex-1 flex flex-col ml-[270px]">
        <UserNavigation
          userType="company"
          userName={user?.first_name || user?.company_name || 'Company'}
          userImage={user?.avatar_url || user?.logo_url || undefined}
        />
        <main className="flex-1 overflow-y-auto p-8">{children}</main>
      </div>
    </div>
  )
}
