'use client';

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { UserCheck, Users, Download } from 'lucide-react';

export interface StatsSummaryProps {
  totalDownloads: number;
  vipDownloads: number;
  nonVipDownloads: number;
  uniqueUsers: number;
}

export const StatsSummary = ({
  totalDownloads,
  vipDownloads,
  nonVipDownloads,
  uniqueUsers
}: StatsSummaryProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
          <Download className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{totalDownloads}</div>
          <p className="text-xs text-muted-foreground">
            For the selected period
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">VIP Downloads</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{vipDownloads}</div>
          <p className="text-xs text-muted-foreground">
            {totalDownloads > 0
              ? Math.round((vipDownloads / totalDownloads) * 100)
              : 0}
            % of total
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">
            Non-VIP Downloads
          </CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{nonVipDownloads}</div>
          <p className="text-xs text-muted-foreground">
            {totalDownloads > 0
              ? Math.round((nonVipDownloads / totalDownloads) * 100)
              : 0}
            % of total
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <CardTitle className="text-sm font-medium">Unique Users</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{uniqueUsers}</div>
          <p className="text-xs text-muted-foreground">
            Distinct users who downloaded
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
