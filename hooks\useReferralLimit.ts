'use client';

import { useState, useEffect } from 'react';

import { useToast } from '@/components/ui/use-toast';
import { getWeeklyReferralCount } from '@/actions/job-market/publicFirm-referrals';

export function useReferralLimit(userId?: string) {
  const [referralCount, setReferralCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasReachedLimit, setHasReachedLimit] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const checkReferralLimit = async () => {
    if (!userId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Use the server action to get the count
      const currentCount = await getWeeklyReferralCount(userId);

      // Update the state with the count
      setReferralCount(currentCount);
      setHasReachedLimit(currentCount >= 2);
    } catch (err) {
      console.error('Error checking referral limit:', err);
      setError('Failed to check referral limit');
      toast({
        title: 'Error',
        description:
          'Could not check your weekly referral count. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh the referral count
  const refreshCount = () => {
    checkReferralLimit();
  };

  // Check referral limit on mount and when userId changes
  useEffect(() => {
    checkReferralLimit();
  }, [userId]);

  return {
    referralCount,
    isLoading,
    hasReachedLimit,
    error,
    refreshCount
  };
}
