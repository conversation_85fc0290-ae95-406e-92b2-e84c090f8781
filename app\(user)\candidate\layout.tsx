// app/(user)/candidate/layout.tsx
'use client';

import type React from 'react';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/hooks/useUser';
import { useToast } from '@/components/ui/use-toast';
import { DynamicSidebar } from '@/components/sidebar/dynamic-sidebar';
import { UserNavigation } from '@/components/user-navigation';

export default function CandidateLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useUser();
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    // First check if user is logged in and is a candidate
    if (!loading && user && user.user_type.toLowerCase() !== 'candidate') {
      toast({
        title: 'Access denied',
        description: "You don't have access to the candidate area",
        variant: 'destructive'
      });
      router.push('/signin');
      return;
    }

    // Then check if user is verified
    if (
      !loading &&
      user &&
      user.user_type.toLowerCase() === 'candidate' &&
      user.verified === false
    ) {
      toast({
        title: 'Verification required',
        description: 'Please verify your email to access the candidate area',
        variant: 'destructive'
      });
      router.push(
        `/verify?email=${encodeURIComponent(user.email)}&userType=candidate`
      );
    }
  }, [user, loading, router, toast]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!user || user.user_type.toLowerCase() !== 'candidate') {
    return null;
  }

  // Don't render the layout if user is not verified
  if (user.verified === false) {
    return null;
  }

  return (
    <div className="flex min-h-screen">
      <DynamicSidebar role="candidate" />
      <div className="flex-1 flex flex-col ml-[270px]">
        <UserNavigation
          userType="candidate"
          userName={user?.first_name || 'Candidate'}
          userImage={user?.avatar_url}
        />
        <main className="flex-1 overflow-y-auto p-8">{children}</main>
      </div>
    </div>
  );
}
