"use client"

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

const faqItems = [
  {
    question: "Can I unsubscribe at any time?",
    answer:
      "Yes, you can cancel your subscription at any time. If you cancel, you'll continue to have access to your membership benefits until the end of your current billing period.",
  },
  {
    question: "Will my subscription automatically renew?",
    answer:
      "Yes, all subscriptions automatically renew at the end of their period to ensure uninterrupted access to your benefits. You'll receive an email reminder before renewal, and you can cancel anytime from your account settings.",
  },
  {
    question: "Can I upgrade or downgrade my plan at any time?",
    answer:
      "You can upgrade your plan at any time, and the new benefits will be available immediately. When downgrading, the changes will take effect at the start of your next billing cycle.",
  },
  {
    question: "What happens if I downgrade my subscription?",
    answer:
      "When you downgrade, you will continue to have access to your current plan until the end of your billing period. After that, your subscription will switch to the new plan with its associated features and pricing.",
  },
  {
    question: "Are there any credits or refunds when downgrading?",
    answer:
      "We do not provide partial refunds for downgrades. Your new plan and pricing will take effect at the start of your next billing cycle.",
  },
  {
    question: "How do these credits work?",
    answer:
      "Credits are applied to your account and can be used for future subscription payments or one-time purchases. They do not expire and will automatically be applied to your next billing cycle.",
  },
]

export function SubscriptionFaq() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container px-4 mx-auto md:px-6">
        <div className="max-w-3xl mx-auto">
          <h2 className="mb-8 text-3xl font-bold text-center">Frequently Asked Questions</h2>
          <p className="mb-10 text-center text-gray-600">
            Find quick answers to your questions about our subscription pricing, payment options, and billing policies.
            Please contact us if you have any further questions.
          </p>

          <Accordion type="single" collapsible className="w-full">
            {faqItems.map((item, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">{item.question}</AccordionTrigger>
                <AccordionContent>
                  <p className="text-gray-600">{item.answer}</p>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  )
}

