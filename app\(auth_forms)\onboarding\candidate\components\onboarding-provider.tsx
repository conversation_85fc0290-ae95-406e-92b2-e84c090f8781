'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

// Define the types for our context
type OnboardingData = {
  personalInfo: {
    firstName: string;
    lastName: string;
    preferredName: string;
    email: string;
    phoneNumber: string;
    nationality: string;
  };
  jobPreferences: {
    location: string;
    jobType: string[];
    salary: string;
    industry: string[];
    remoteWork: boolean;
  };
  aboutYou: {
    resume: File | null;
    portfolioUrl: string;
    linkedin: string;
    additionalInfo: string;
  };
};

type OnboardingContextType = {
  data: OnboardingData;
  updatePersonalInfo: (info: Partial<OnboardingData['personalInfo']>) => void;
  updateJobPreferences: (
    prefs: Partial<OnboardingData['jobPreferences']>
  ) => void;
  updateAboutYou: (about: Partial<OnboardingData['aboutYou']>) => void;
  isStepCompleted: (step: string) => boolean;
  markStepAsCompleted: (step: string) => void;
};

// Default state values
const defaultOnboardingData: OnboardingData = {
  personalInfo: {
    firstName: '',
    lastName: '',
    preferredName: '',
    email: '',
    phoneNumber: '',
    nationality: ''
  },
  jobPreferences: {
    location: '',
    jobType: [],
    salary: '',
    industry: [],
    remoteWork: false
  },
  aboutYou: {
    resume: null,
    portfolioUrl: '',
    linkedin: '',
    additionalInfo: ''
  }
};

// Create context
const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

// Provider component
export function OnboardingProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const [data, setData] = useState<OnboardingData>(defaultOnboardingData);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  // Load saved data from localStorage on initial render
  useEffect(() => {
    const loadSavedData = () => {
      try {
        // Load personal info
        const savedPersonalInfo = localStorage.getItem(
          'onboarding_personal_info'
        );
        if (savedPersonalInfo) {
          setData((prev) => ({
            ...prev,
            personalInfo: JSON.parse(savedPersonalInfo)
          }));
        }

        // Load job preferences
        const savedJobPreferences = localStorage.getItem(
          'onboarding_job_preferences'
        );
        if (savedJobPreferences) {
          setData((prev) => ({
            ...prev,
            jobPreferences: JSON.parse(savedJobPreferences)
          }));
        }

        // Load completed steps
        const savedCompletedSteps = localStorage.getItem(
          'onboarding_completed_steps'
        );
        if (savedCompletedSteps) {
          setCompletedSteps(JSON.parse(savedCompletedSteps));
        }
      } catch (error) {
        console.error('Error loading saved onboarding data:', error);
      }
    };

    loadSavedData();
  }, []);

  // Save completed steps to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(
      'onboarding_completed_steps',
      JSON.stringify(completedSteps)
    );
  }, [completedSteps]);

  // Update functions
  const updatePersonalInfo = (
    info: Partial<OnboardingData['personalInfo']>
  ) => {
    setData((prev) => ({
      ...prev,
      personalInfo: { ...prev.personalInfo, ...info }
    }));
  };

  const updateJobPreferences = (
    prefs: Partial<OnboardingData['jobPreferences']>
  ) => {
    setData((prev) => ({
      ...prev,
      jobPreferences: { ...prev.jobPreferences, ...prefs }
    }));
  };

  const updateAboutYou = (about: Partial<OnboardingData['aboutYou']>) => {
    setData((prev) => ({
      ...prev,
      aboutYou: { ...prev.aboutYou, ...about }
    }));
  };

  const isStepCompleted = (step: string) => {
    return completedSteps.includes(step);
  };

  const markStepAsCompleted = (step: string) => {
    if (!completedSteps.includes(step)) {
      setCompletedSteps((prev) => [...prev, step]);
    }
  };

  return (
    <OnboardingContext.Provider
      value={{
        data,
        updatePersonalInfo,
        updateJobPreferences,
        updateAboutYou,
        isStepCompleted,
        markStepAsCompleted
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
}

// Custom hook to use the context
export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
