import createMDX from 'fumadocs-mdx/config';

const withMDX = createMDX();

/** @type {import('next').NextConfig} */
const config = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: ''
      },
      {
        protocol: 'http', 
        hostname: '127.0.0.1', 
        port: '64321'
      },
      {
        protocol: 'https',
        hostname: 'llmgwifgtszjgjlzlwjq.supabase.co',
        port: ''
      },
      {
        protocol: 'https',
        hostname: 'f3f8c3f7c334ada9407b72e0028b9909.cdn.bubble.io',
        port: ''
      },
      {
        protocol: 'https',
        hostname: 'cbjkjobahufvdjnhffkp.supabase.co',
        port: ''
      }
    ]
  }
};

export default withMDX(config);
