'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

// Define the types for our context
type OnboardingData = {
  companyInfo: {
    name: string;
    description: string;
    company_crunchbase: string;
    company_linkedin: string;
    contact_email: string;
    current_funding: string;
    founder_linkedin: string;
    founder_school: string;
    industry: string;
    legally_incorporated: boolean;
    location: string;
    logo_url: string | null;
    open_for_reverse_hiring: boolean;
  };
};

type OnboardingContextType = {
  data: OnboardingData;
  updateCompanyInfo: (info: Partial<OnboardingData['companyInfo']>) => void;
  isStepCompleted: (step: string) => boolean;
  markStepAsCompleted: (step: string) => void;
};

// Default state values
const defaultOnboardingData: OnboardingData = {
  companyInfo: {
    name: '',
    description: '',
    company_crunchbase: '',
    company_linkedin: '',
    contact_email: '',
    current_funding: '',
    founder_linkedin: '',
    founder_school: '',
    industry: '',
    legally_incorporated: false,
    location: '',
    logo_url: null,
    open_for_reverse_hiring: false
  }
};

// Create context
const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined
);

// Provider component
export function OnboardingProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const [data, setData] = useState<OnboardingData>(defaultOnboardingData);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  // Load saved data from localStorage on initial render
  useEffect(() => {
    const loadSavedData = () => {
      try {
        // Load company info
        const savedCompanyInfo = localStorage.getItem(
          'company_onboarding_info'
        );
        if (savedCompanyInfo) {
          setData((prev) => ({
            ...prev,
            companyInfo: JSON.parse(savedCompanyInfo)
          }));
        }

        // Load completed steps
        const savedCompletedSteps = localStorage.getItem(
          'company_onboarding_completed_steps'
        );
        if (savedCompletedSteps) {
          setCompletedSteps(JSON.parse(savedCompletedSteps));
        }
      } catch (error) {
        console.error('Error loading saved onboarding data:', error);
      }
    };

    loadSavedData();
  }, []);

  // Save completed steps to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(
      'company_onboarding_completed_steps',
      JSON.stringify(completedSteps)
    );
  }, [completedSteps]);

  // Update functions
  const updateCompanyInfo = (info: Partial<OnboardingData['companyInfo']>) => {
    const updatedInfo = {
      ...data.companyInfo,
      ...info
    };

    setData((prev) => ({
      ...prev,
      companyInfo: updatedInfo
    }));

    // Save to localStorage
    localStorage.setItem(
      'company_onboarding_info',
      JSON.stringify(updatedInfo)
    );
  };

  const isStepCompleted = (step: string) => {
    return completedSteps.includes(step);
  };

  const markStepAsCompleted = (step: string) => {
    if (!completedSteps.includes(step)) {
      setCompletedSteps((prev) => [...prev, step]);
    }
  };

  return (
    <OnboardingContext.Provider
      value={{
        data,
        updateCompanyInfo,
        isStepCompleted,
        markStepAsCompleted
      }}
    >
      {children}
    </OnboardingContext.Provider>
  );
}

// Custom hook to use the context
export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
