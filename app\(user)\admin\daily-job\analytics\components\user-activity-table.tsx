'use client';

import { useState, useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { User<PERSON>he<PERSON>, Users } from 'lucide-react';
import type { DailyJob } from '@/types/crawler';
import { UserDetailModal } from './user-detail-modal';
import { getUserDetailsForAnalytics } from '@/actions/admin/web-crawler';

// Interface for user details
interface UserDetails {
  id: string;
  email: string;
  fullName: string;
  isVip: boolean;
  downloads: number;
}

export interface UserActivityTableProps {
  jobs: DailyJob[];
}

export const UserActivityTable = ({ jobs }: UserActivityTableProps) => {
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [topUsers, setTopUsers] = useState<UserDetails[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserDetails = async () => {
      setLoading(true);
      try {
        // First get the basic activity data with download counts
        const userActivity = getUserActivityData();

        if (userActivity.length === 0) {
          setTopUsers([]);
          setLoading(false);
          return;
        }

        // Extract user IDs to fetch details
        const userIds = userActivity.map((user) => user.username);

        // Fetch user details from the server
        const userDetails = await getUserDetailsForAnalytics(userIds);

        // Map the details to our format
        const detailedUsers = userActivity.map((user) => {
          // Find the user details or use defaults
          const details = userDetails.find(
            (detail) => detail.id === user.username
          ) || {
            email: user.username,
            first_name: '',
            last_name: '',
            vip: user.isVip
          };

          // Create the full name
          const fullName =
            `${details.first_name || ''} ${details.last_name || ''}`.trim() ||
            'N/A';

          return {
            id: user.username,
            email: details.email || user.username,
            fullName,
            isVip: user.isVip,
            downloads: user.downloads
          };
        });

        setTopUsers(detailedUsers);
      } catch (error) {
        console.error('Error fetching user details:', error);

        // Fallback to basic data if API call fails
        const basicUsers = getUserActivityData().map((user) => ({
          id: user.username,
          email: user.username,
          fullName: 'N/A',
          isVip: user.isVip,
          downloads: user.downloads
        }));

        setTopUsers(basicUsers);
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetails();
  }, [jobs]);

  const getUserActivityData = () => {
    const userActivity = new Map<
      string,
      { username: string; downloads: number; isVip: boolean }
    >();

    jobs.forEach((job) => {
      const vipUsers = job.downloader_vip || [];
      const nonVipUsers = job.downloader_nonvip || [];

      vipUsers.forEach((user) => {
        if (userActivity.has(user)) {
          const existing = userActivity.get(user)!;
          userActivity.set(user, {
            ...existing,
            downloads: existing.downloads + 1
          });
        } else {
          userActivity.set(user, { username: user, downloads: 1, isVip: true });
        }
      });

      nonVipUsers.forEach((user) => {
        if (userActivity.has(user)) {
          const existing = userActivity.get(user)!;
          userActivity.set(user, {
            ...existing,
            downloads: existing.downloads + 1
          });
        } else {
          userActivity.set(user, {
            username: user,
            downloads: 1,
            isVip: false
          });
        }
      });
    });

    return Array.from(userActivity.values())
      .sort((a, b) => b.downloads - a.downloads)
      .slice(0, 10);
  };

  const handleUserClick = (user: UserDetails) => {
    setSelectedUser(user);
  };

  return (
    <div className="overflow-x-auto rounded-lg">
      {loading ? (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-700"></div>
        </div>
      ) : (
        <table className="w-full border-collapse min-w-full table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-3 text-left">Email</th>
              <th className="p-3 text-left">Name</th>
              <th className="p-3 text-left">Type</th>
              <th className="p-3 text-left">Downloads</th>
            </tr>
          </thead>
          <tbody>
            {topUsers.map((user, index) => (
              <tr
                key={index}
                className="border-t hover:bg-gray-50 cursor-pointer"
                onClick={() => handleUserClick(user)}
              >
                <td className="p-3 font-medium">{user.email}</td>
                <td className="p-3">{user.fullName}</td>
                <td className="p-3">
                  {user.isVip ? (
                    <span className="inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                      <UserCheck className="h-3 w-3 mr-1" />
                      VIP
                    </span>
                  ) : (
                    <span className="inline-flex items-center bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                      <Users className="h-3 w-3 mr-1" />
                      Standard
                    </span>
                  )}
                </td>
                <td className="p-3">{user.downloads}</td>
              </tr>
            ))}
            {topUsers.length === 0 && (
              <tr className="border-t">
                <td colSpan={4} className="p-3 text-center text-gray-500">
                  No data available for the selected period
                </td>
              </tr>
            )}
          </tbody>
        </table>
      )}

      {selectedUser && (
        <UserDetailModal
          isOpen={!!selectedUser}
          onClose={() => setSelectedUser(null)}
          username={selectedUser.email}
          userId={selectedUser.id}
          isVip={selectedUser.isVip}
          fullName={selectedUser.fullName}
        />
      )}
    </div>
  );
};
