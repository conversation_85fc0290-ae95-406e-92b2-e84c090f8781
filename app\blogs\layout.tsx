'use client';
import { marketingConfig } from '@/config/marketing';

import CircularNavigation from '@/components/navigation';
import FooterPrimary from '@/components/footer-primary';
import React from 'react';
import { useUser } from '@/hooks/useUser';

interface BlogsLayoutProps {
  children: React.ReactNode;
}

export default function BlogsLayout({ children }: BlogsLayoutProps) {
  const { user, loading } = useUser();

  return (
    <div className="flex min-h-screen flex-col items-center w-full">
      <CircularNavigation
        items={marketingConfig.mainNav}
        user={user ? true : false}
      />
      <main className="flex-1">{children}</main>
      <FooterPrimary />
    </div>
  );
}
