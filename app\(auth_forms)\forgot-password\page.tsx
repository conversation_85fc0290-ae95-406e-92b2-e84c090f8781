'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { resetPasswordForEmail } from '@/utils/supabase/auth';
import { getURL } from '@/lib/utils';
import Particles from '@/components/magicui/particles';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

// Form validation schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address')
});

type FormValues = z.infer<typeof formSchema>;

export default function ForgotPasswordPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const supabase = createClient();

  // Initialize form with React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: ''
    }
  });

  const onSubmit = async (values: FormValues) => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    try {
      // Check migration status first
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('migration_status')
        .eq('email', values.email)
        .single();

      if (userError) {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: 'Could not verify account status.'
        });
        return;
      }

      if (userData && userData.migration_status !== 'migrated') {
        toast({
          title: 'Password Reset Required',
          description:
            'Please go to the Sign In page and enter your registered email to reset your password.',
          variant: 'default',
          duration: 6000
        });
        router.push('/signin');
        return;
      }

      const result = await resetPasswordForEmail(
        values.email,
        getURL('/reset-password')
      );

      if (result.type === 'success') {
        setIsSuccess(true);
      } else {
        toast({
          variant: 'destructive',
          title: result.title,
          description: result.description
        });
      }
    } catch (error) {
      console.error('Password reset error:', error);
      toast({
        title: 'Reset Error',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-[100dvh] bg-background">
      {/* Left Section with Illustration */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Reset Your Password</h1>
            <p className="text-xl">
              Enter your email address and we'll send you instructions to reset
              your password.
            </p>
          </div>
        </div>

        <div className="relative z-10">
          <p className="text-sm opacity-80">
            {new Date().getFullYear()} InternUp. All rights reserved.
          </p>
        </div>

        {/* Decorative elements */}
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40"></div>
        <Particles
          className="absolute inset-0"
          quantity={300}
          size={1}
          ease={1}
          refresh
        />
      </div>

      {/* Right Section with Form */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto">
        <div className="flex items-center justify-between mb-8">
          <Link
            href="/signin"
            className="rounded-md p-2 transition-colors hover:bg-muted"
            prefetch={false}
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back</span>
          </Link>
        </div>

        <div className="max-w-md mx-auto">
          <div className="space-y-6 text-center mb-8">
            <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent">
              Reset Password
            </h1>
            <p className="text-muted-foreground">
              {isSuccess
                ? 'Check your email for the reset link'
                : 'Enter your email to receive reset instructions'}
            </p>
          </div>

          {!isSuccess && (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          autoComplete="email"
                          disabled={isSubmitting}
                          className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full bg-[#118073] hover:bg-[#16a38a] text-white transition-colors"
                  size="lg"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : 'Send Reset Link'}
                </Button>
              </form>
            </Form>
          )}

          <div className="text-center mt-8">
            <Link
              href="/signin"
              className="text-[#118073] hover:text-[#16a38a] text-sm font-medium hover:underline transition-colors"
              prefetch={false}
            >
              Back to Sign in
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
