'use client';

import { useState } from 'react';
import type { DailyJob } from '@/types/crawler';
import { formatDate } from '@/lib/utils';
import { CreateDailyJobDialog } from './create-daily-job-dialog';
import { EditDailyJobDialog } from './edit-daily-job-dialog';
import { Pencil, Trash, Users, UserCheck, FileSpreadsheet } from 'lucide-react';
import { deleteDailyJob } from '@/actions/admin/web-crawler';
import { Badge } from '@/components/ui/badge';

interface DailyJobsListProps {
  initialJobs: DailyJob[];
}

export function DailyJobsList({ initialJobs }: DailyJobsListProps) {
  const [jobs, setJobs] = useState<DailyJob[]>(initialJobs);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState<DailyJob | null>(null);

  const handleDeleteClick = async (id: string) => {
    if (confirm('Are you sure you want to delete this job?')) {
      const result = await deleteDailyJob(id);
      if (result.success) {
        setJobs(jobs.filter((job) => job.id !== id));
      }
    }
  };

  const handleEditClick = (job: DailyJob) => {
    setSelectedJob(job);
    setIsEditDialogOpen(true);
  };

  const handleJobCreated = (newJob: DailyJob) => {
    setJobs([newJob, ...jobs]);
    setIsCreateDialogOpen(false);
  };

  const handleJobUpdated = (updatedJob: DailyJob) => {
    setJobs(jobs.map((job) => (job.id === updatedJob.id ? updatedJob : job)));
    setIsEditDialogOpen(false);
    setSelectedJob(null);
  };

  const getFileNameFromUrl = (url: string | null) => {
    if (!url) return 'No file';
    const parts = url.split('/');
    return parts[parts.length - 1];
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <p className="mb-4">{jobs.length} results</p>
        <button
          type="button"
          onClick={() => setIsCreateDialogOpen(true)}
          className="px-4 py-2 bg-gray-800 text-white rounded"
        >
          Create
        </button>
      </div>

      <div className="overflow-x-auto rounded-lg border">
        <table className="w-full border-collapse min-w-full table-auto">
          <thead>
            <tr className="bg-gray-100">
              <th className="p-3 text-left">Date</th>
              <th className="p-3 text-left">Type</th>
              <th className="p-3 text-left">File</th>
              <th className="p-3 text-left">Notice</th>
              <th className="p-3 text-left">N_VIP</th>
              <th className="p-3 text-left">Records</th>
              <th className="p-3 text-left">VIP</th>
              <th className="p-3 text-left">Records</th>
              <th className="p-3 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            {jobs.map((job) => (
              <tr key={job.id} className="border-t">
                <td className="p-3">
                  {formatDate(
                    job.posting_date
                      ? new Date(job.posting_date).toISOString()
                      : ''
                  )}
                </td>
                <td className="p-3">{job.file_type || 'Daily Jobs'}</td>
                <td className="p-3">
                  {job.crawler_file ? (
                    <a
                      href={job.crawler_file}
                      className="flex items-center text-blue-500 hover:underline"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <FileSpreadsheet className="h-4 w-4 mr-1" />
                      <span className="truncate max-w-[150px]">
                        {getFileNameFromUrl(job.crawler_file)}
                      </span>
                    </a>
                  ) : (
                    <span className="text-gray-400">No file</span>
                  )}
                </td>
                <td className="p-3">{job.notice}</td>
                <td className="p-3">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1 text-gray-500" />
                    <span>{job.downloader_nonvip?.length || 0}</span>
                  </div>
                </td>
                <td className="p-3">
                  <div className="flex flex-wrap gap-1 max-w-[150px] overflow-hidden">
                    {job.downloader_nonvip &&
                    job.downloader_nonvip.length > 0 ? (
                      job.downloader_nonvip.map((user, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs truncate"
                        >
                          {user}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-gray-400">No records</span>
                    )}
                  </div>
                </td>
                <td className="p-3">
                  <div className="flex items-center">
                    <UserCheck className="h-4 w-4 mr-1 text-gray-500" />
                    <span>{job.downloader_vip?.length || 0}</span>
                  </div>
                </td>
                <td className="p-3">
                  <div className="flex flex-wrap gap-1 max-w-[150px] overflow-hidden">
                    {job.downloader_vip && job.downloader_vip.length > 0 ? (
                      job.downloader_vip.map((user, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs truncate"
                        >
                          {user}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-gray-400">No records</span>
                    )}
                  </div>
                </td>
                <td className="p-3">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEditClick(job)}
                      className="text-teal-600"
                    >
                      <Pencil size={20} />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(job.id)}
                      className="text-red-500"
                    >
                      <Trash size={20} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <CreateDailyJobDialog
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onJobCreated={handleJobCreated}
      />

      {selectedJob && (
        <EditDailyJobDialog
          isOpen={isEditDialogOpen}
          onClose={() => {
            setIsEditDialogOpen(false);
            setSelectedJob(null);
          }}
          onJobUpdated={handleJobUpdated}
          job={selectedJob}
        />
      )}
    </div>
  );
}
