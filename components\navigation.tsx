'use client';

import * as React from 'react';
import Link from 'next/link';
import { MainNavItem } from 'types';
import { cn } from '@/lib/utils';
import { MobileNav } from '@/components/mobile-nav';
import { Button, buttonVariants } from '@/components/ui/button';
import Image from 'next/image';
import { ChevronDown, LogOut, Menu, X } from 'lucide-react';
import { signOut } from '@/utils/bubble/auth';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

interface CircularNavProps {
  items?: MainNavItem[];
  children?: React.ReactNode;
  user?: boolean;
  userType?: string | null;
}

export default function CircularNavigation({
  items,
  children,
  user,
  userType
}: CircularNavProps) {
  const [showMobileMenu, setShowMobileMenu] = React.useState<boolean>(false);
  const [activeDropdown, setActiveDropdown] = React.useState<string | null>(
    null
  );
  const { toast } = useToast();
  const router = useRouter();

  // Prevent body scroll when mobile menu is open
  React.useEffect(() => {
    if (showMobileMenu) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [showMobileMenu]);

  const handleSignOut = async () => {
    try {
      const result = await signOut();
      toast({
        title: 'Signed out',
        description: 'See you soon!'
      });
      // Handle redirection if a path is provided and router exists
      if (result.redirectPath && router) {
        router.push(result.redirectPath);
      }
      window.location.reload();
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // Determine dashboard path based on user type
  const getDashboardPath = () => {
    if (!user) return '/signin';

    switch (userType?.toLowerCase()) {
      case 'admin':
        return '/admin';
      case 'candidate':
        return '/candidate';
      case 'insider':
        return '/insider';
      case 'company':
        return '/company';
      default:
        return '/';
    }
  };

  // Handle closing the dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Don't do anything if the click is inside a dropdown or trigger
      const clickedElement = event.target as Element;
      const isDropdownClick = clickedElement.closest('[data-dropdown]');

      if (!isDropdownClick) {
        setActiveDropdown(null);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  return (
    <>
      <nav className="flex flex-wrap items-center justify-between w-4/5 p-4 md:p-1 gap-4 md:gap-20 md:bg-zinc-50 md:dark:bg-zinc-900 md:rounded-full md:px-8 md:border-2 md:border-muted/30 md:dark:border-muted/80 md:shadow-md mx-auto mt-4 backdrop-blur-sm md:backdrop-blur-none">
        <Link href={'/'} className="flex items-center">
          <div className="flex items-center space-x-2">
            <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
              <Image src="/favicon_new.png" alt="logo" width={25} height={25} />
            </div>
            <span className="text-lg md:text-xl font-extrabold tracking-tightest">
              InternUp
            </span>
          </div>
        </Link>

        {items?.length ? (
          <div className="hidden md:flex space-x-6">
            {items?.map((item, index) => (
              <div key={index} className="relative">
                {item.items?.length ? (
                  <div
                    data-dropdown="trigger"
                    className={cn(
                      'text-black transition-colors hover:text-foreground/80 flex items-center gap-1 cursor-pointer',
                      item.disabled && 'cursor-not-allowed opacity-80'
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      setActiveDropdown(
                        activeDropdown === item.title ? null : item.title
                      );
                    }}
                    onMouseEnter={() => setActiveDropdown(item.title)}
                  >
                    {item.title}
                    <ChevronDown
                      className={cn(
                        'h-4 w-4 transition-transform',
                        activeDropdown === item.title && 'rotate-180'
                      )}
                    />
                  </div>
                ) : (
                  <Link
                    href={item.disabled ? '#' : item.href}
                    className={cn(
                      'text-black transition-colors hover:text-foreground/80 flex items-center gap-1',
                      item.disabled && 'cursor-not-allowed opacity-80'
                    )}
                    onMouseEnter={() => setActiveDropdown(null)}
                  >
                    {item.title}
                  </Link>
                )}

                {item.items?.length ? (
                  <div
                    data-dropdown="content"
                    className={cn(
                      'absolute left-0 top-full mt-2 w-48 rounded-md bg-white dark:bg-zinc-800 shadow-lg ring-1 ring-black ring-opacity-5 transition-all z-50',
                      activeDropdown === item.title
                        ? 'opacity-100 visible translate-y-0'
                        : 'opacity-0 invisible translate-y-1 pointer-events-none'
                    )}
                    onMouseEnter={() => setActiveDropdown(item.title)}
                    onMouseLeave={(e) => {
                      // Check if we're moving to the trigger element
                      const relatedTarget = e.relatedTarget as Element;
                      const isMovingToTrigger = relatedTarget?.closest(
                        '[data-dropdown="trigger"]'
                      );

                      // Only clear if not moving to trigger
                      if (!isMovingToTrigger) {
                        setActiveDropdown(null);
                      }
                    }}
                  >
                    <div className="py-1">
                      {item.items.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          href={subItem.disabled ? '#' : subItem.href}
                          className={cn(
                            'block px-4 py-2 text-sm text-black hover:bg-gray-100 dark:hover:bg-zinc-700 transition-colors',
                            subItem.disabled && 'cursor-not-allowed opacity-80'
                          )}
                        >
                          {subItem.title}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : null}
              </div>
            ))}
          </div>
        ) : null}

        <div className="flex items-center space-x-2">
          <div className="hidden md:flex items-center space-x-2">
            <Link
              href={getDashboardPath()}
              className={cn(
                buttonVariants({ variant: 'default', size: 'sm' }),
                'rounded-full p-2 md:p-5 text-xs md:text-sm'
              )}
            >
              {user ? 'Dashboard' : 'Login'}
            </Link>
            {!user && (
              <Link
                href={'/signup'}
                className={cn(
                  buttonVariants({ variant: 'outline', size: 'sm' }),
                  'rounded-full p-2 md:p-5 text-xs md:text-sm'
                )}
              >
                Sign Up
              </Link>
            )}
            {user && (
              <Button
                variant="ghost"
                onClick={handleSignOut}
                className="flex rounded-full items-center gap-2 text-red-500 bg-red-500/10 hover:bg-red-600 hover:text-white"
                size="sm"
              >
                <LogOut className="h-4 w-4" />
                <span className="hidden sm:inline-block">Logout</span>
              </Button>
            )}
          </div>

          <button
            className="md:hidden flex items-center justify-center rounded-md p-2 transition-colors hover:bg-muted"
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            aria-label="Toggle mobile menu"
          >
            {showMobileMenu ? (
              <X className="h-5 w-5" />
            ) : (
              <Menu className="h-5 w-5" />
            )}
            <span className="sr-only">Toggle Menu</span>
          </button>
        </div>
      </nav>

      {showMobileMenu && items && (
        <MobileNav
          items={items}
          user={user}
          userType={userType}
          onClose={() => setShowMobileMenu(false)}
        >
          {children}
        </MobileNav>
      )}
    </>
  );
}
