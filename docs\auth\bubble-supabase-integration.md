# Bubble and Supabase Integration

## Overview

This document explains the integration between Bubble and Supabase in the InternUp application's authentication system. It details how the two platforms work together, the data flow between them, and the strategy for eventually migrating all authentication from Bubble to Supabase.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [User Data Structure](#user-data-structure)
3. [Authentication Flow](#authentication-flow)
4. [Token Management](#token-management)
5. [User Synchronization](#user-synchronization)
6. [Migration Strategy](#migration-strategy)
7. [Technical Implementation](#technical-implementation)
8. [Security Considerations](#security-considerations)

## Architecture Overview

### Current Hybrid System

The authentication system uses a hybrid approach:

- **Bubble**: Handles traditional email/password authentication
- **Supabase**: Manages OAuth authentication and stores additional user data

This dual-database approach allows for:
1. Backward compatibility with existing Bubble users
2. Modern OAuth capabilities through Supabase
3. A gradual migration path from Bubble to Supabase

### System Components

- **BubbleClient**: A utility class for interacting with the Bubble API
- **Supabase Client**: Handles Supabase authentication and database operations
- **Auth Utilities**: Functions that coordinate between the two systems
- **User Context**: Provides user information throughout the application

## User Data Structure

### Bubble User Data

In Bubble, user data includes:
- User ID
- Email
- Password (hashed)
- User type
- Authentication tokens

### Supabase User Data

In Supabase, user data includes:
- ID (same as Bubble user ID for email/password users)
- Email
- User type
- Verification status
- Verification code (temporary)
- Additional profile information

### Relationship

The key to the integration is using the Bubble user ID as the primary key in the Supabase users table. This creates a one-to-one relationship between users in both systems.

## Authentication Flow

### Email/Password Authentication

1. User signs up or signs in with email/password
2. Bubble handles the authentication and returns a token
3. The token is stored in localStorage
4. User data is created or retrieved from Supabase using the Bubble user ID

```typescript
// From utils/bubble/auth.ts
export async function signInWithPassword(formData: FormData) {
  try {
    const email = String(formData.get('email')).trim();
    const password = String(formData.get('password')).trim();

    // Authenticate with Bubble
    const response = await bubbleClient.signIn(email, password);

    if (response.status === 'success') {
      // Store tokens from Bubble
      const { token, user_id } = response.response;
      setTokens(token, user_id);

      // Get user data from Supabase
      const supabase = createClient();
      const { data, error } = await supabase
        .from('users')
        .select('user_type')
        .eq('id', response.response.user_id)
        .single();

      // Handle user data and redirection
      // ...
    }
  } catch (error) {
    // Error handling
  }
}
```

### OAuth Authentication

1. User initiates OAuth authentication
2. Supabase handles the OAuth flow with the provider
3. After successful authentication:
   - For new users: A Bubble account is created with a random password
   - For existing users: The Bubble user ID is retrieved
4. User data is synchronized between Supabase and Bubble

## Token Management

### Bubble Tokens

Bubble authentication tokens are managed using utility functions:

```typescript
// From utils/bubble/queries.ts
export const getTokens = () => {
  if (typeof window !== 'undefined') {
    try {
      const token = localStorage.getItem('bubble_auth_token');
      const user_id = localStorage.getItem('bubble_user_id');
      return { token, user_id };
    } catch (error) {
      console.error('Error in getTokens:', error);
      return { token: null, user_id: null };
    }
  }
  // Return null tokens if not in a browser environment
  return { token: null, user_id: null };
};

export const setTokens = (token: string, user_id: string) => {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem('bubble_auth_token', token);
      localStorage.setItem('bubble_user_id', user_id);
    } catch (error) {
      console.error('Error in setTokens:', error);
    }
  }
};
```

### Supabase Session

Supabase sessions are managed automatically through cookies:

```typescript
// From utils/supabase/middleware.ts
export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({
            request
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        }
      }
    }
  );

  return supabaseResponse;
}
```

## User Synchronization

### Creating New Users

When a new user signs up with email/password:

1. User is created in Bubble first
2. Bubble returns a user ID and token
3. A corresponding record is created in Supabase with the Bubble user ID

```typescript
// From utils/bubble/auth.ts
// Create user in Supabase with Bubble user ID
const supabase = createClient();
const { error: supabaseError } = await supabase.from('users').insert({
  id: bubbleResponse.response.user_id,
  email: email,
  user_type: user_type,
  startup_company: startup_company,
  verified: false,
  verification_code: verificationCode,
  verification_expiration: verificationExpiration.toISOString()
});
```

When a new user signs up with OAuth:

1. User is authenticated through Supabase
2. A corresponding account is created in Bubble with a random password
3. The Bubble user ID is stored in the Supabase user record

```typescript
// From app/auth/callback/auth-callback-handler.tsx
// Generate a random secure password for Bubble
const randomPassword = Array(20)
  .fill('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*')
  .map(x => x[Math.floor(Math.random() * x.length)])
  .join('');

// Create the user in Bubble
const bubbleResponse = await bubbleClient.signUp(email, randomPassword, finalUserType);

bubbleUserId = bubbleResponse.response.user_id;
bubbleToken = bubbleResponse.response.token;

// Store the tokens
localStorage.setItem('bubble_auth_token', bubbleToken);
localStorage.setItem('bubble_user_id', bubbleUserId);
```

### Retrieving User Data

The application retrieves user data from both systems as needed:

```typescript
// From utils/bubble/client.ts
async getCurrentUser(): Promise<any> {
  const { token, user_id } = getTokens();

  if (!token || !user_id) {
    return null;
  }

  try {
    const response = await this.dataRequest(`/obj/User/${user_id}`, {
      method: 'GET'
    });

    return response.response || null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}
```

```typescript
// From utils/supabase/queries.ts
export async function getUser(supabase) {
  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) return null;

    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) throw error;

    return data;
  } catch (error) {
    console.error('Error in getUser:', error);
    return null;
  }
}
```

## Migration Strategy

### Current Approach

The current hybrid system is designed to facilitate a gradual migration from Bubble to Supabase:

1. All new users are created in both systems
2. Authentication can happen through either system
3. User data is synchronized between the systems

### Migration Steps

The planned migration path includes:

1. **Phase 1 (Current)**: Hybrid system with Bubble for email/password auth and Supabase for OAuth
2. **Phase 2**: Implement email/password authentication in Supabase while maintaining Bubble compatibility
3. **Phase 3**: Migrate existing Bubble users to Supabase authentication
4. **Phase 4**: Deprecate Bubble authentication and use Supabase exclusively

### Technical Considerations

The migration requires careful handling of:

- User passwords (cannot be directly transferred due to hashing)
- Authentication tokens
- User data consistency
- Session management

## Technical Implementation

### BubbleClient

The BubbleClient class provides methods for interacting with the Bubble API:

```typescript
// From utils/bubble/client.ts
class BubbleClient {
  public baseUrl: string;
  public apiKey: string;
  private version: string;

  constructor() {
    this.baseUrl = BUBBLE_CONFIG.API_URL;
    this.apiKey = BUBBLE_CONFIG.API_KEY;
    this.version = BUBBLE_CONFIG.APP_VERSION;
  }

  // Methods for API requests
  // Authentication methods
  // User data methods
}
```

### Authentication Utilities

The auth.ts file contains functions that coordinate between Bubble and Supabase:

```typescript
// From utils/bubble/auth.ts
export async function signUp(formData: FormData) {
  // Sign up with Bubble
  // Create user in Supabase
  // Send verification email
}

export async function signInWithPassword(formData: FormData) {
  // Sign in with Bubble
  // Get user data from Supabase
  // Handle redirection
}

export async function signUpWithOAuth(provider: string, userType: string) {
  // Initiate OAuth flow with Supabase
  // Store user type for later use
}
```

### Middleware

The middleware ensures authentication state is maintained across requests:

```typescript
// From middleware.ts
export async function middleware(request: NextRequest) {
  const response = await updateSession(request);
  response.headers.set('x-current-path', request.nextUrl.pathname);
  return response;
}
```

## Security Considerations

### Token Security

- Bubble tokens are stored in localStorage
- Supabase sessions use secure HTTP-only cookies
- API requests include appropriate authorization headers

### Password Security

- Passwords are never stored in plain text
- OAuth users have randomly generated passwords for their Bubble accounts
- Password reset is handled securely through email links

### Data Protection

- User data is protected by row-level security in Supabase
- API keys are stored as environment variables
- Sensitive operations require authentication

---

This documentation provides an overview of the integration between Bubble and Supabase in the authentication system. For detailed implementation, refer to the source code in the following files:

- `utils/bubble/client.ts`: Bubble API client
- `utils/bubble/auth.ts`: Authentication functions
- `utils/supabase/client.ts`: Supabase client for client-side operations
- `utils/supabase/server.ts`: Supabase client for server-side operations
- `middleware.ts`: Authentication middleware
