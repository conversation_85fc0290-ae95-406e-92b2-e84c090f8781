'use client';

import { useState, useEffect } from 'react';
import { XCircleIcon, CheckCircleIcon } from 'lucide-react';
import {
  getApplications,
  updateApplicationStatus
} from '@/actions/admin/startup/applications';
import { Button } from '@/components/ui/button';

import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import type { StartupApplication } from '@/types/startups';
import { useUser } from '@/hooks/useUser';
export default function ApplicationsPage() {
  const [applications, setApplications] = useState<StartupApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedApplication, setSelectedApplication] =
    useState<StartupApplication | null>(null);
  const [isDocumentDialogOpen, setIsDocumentDialogOpen] = useState(false);
  const [documentType, setDocumentType] = useState<
    'resume' | 'cover' | 'additional'
  >('resume');
  const { user, loading } = useUser();

  useEffect(() => {
    // Only fetch applications when user data is loaded
    if (user && !loading) {
      fetchApplications();
    }
  }, [user, loading, currentPage, searchTerm]);

  const fetchApplications = async () => {
    setIsLoading(true);
    try {
      // Only proceed if user is available
      if (!user) {
        console.error('User not available');
        return;
      }

      const {
        data,
        totalPages: pages,
        totalCount: count
      } = await getApplications({
        page: currentPage,
        searchTerm,
        user // Add the user property here
      });
      setApplications(data || []);
      setTotalPages(pages || 1);
      setTotalCount(count || 0);
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptApplication = async (id: string, accept: boolean) => {
    try {
      const newStatus = accept ? 'Accepted' : 'Rejected';
      await updateApplicationStatus(id, newStatus, user);
      fetchApplications();
    } catch (error) {
      console.error('Error updating application status:', error);
    }
  };

  const openDocument = (
    application: StartupApplication,
    type: 'resume' | 'cover' | 'additional'
  ) => {
    setSelectedApplication(application);
    setDocumentType(type);
    setIsDocumentDialogOpen(true);
  };

  const getDocumentUrl = () => {
    if (!selectedApplication) return '';

    switch (documentType) {
      case 'resume':
        return selectedApplication.resume || '';
      case 'cover':
        return selectedApplication.cover_note_file || '';
      case 'additional':
        return selectedApplication.additional_materials || '';
      default:
        return '';
    }
  };

  // Helper function to get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Accepted':
        return 'bg-green-500';
      case 'Rejected':
        return 'bg-red-500';
      case 'Pending':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  // Show loading state while user data is being fetched
  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-4"></div>
          <p>Loading user data...</p>
        </div>
      </div>
    );
  }

  // Show message if no user is found or user is not an admin
  if (!user || user.user_type?.toLowerCase() !== 'admin') {
    return (
      <div className="container mx-auto py-8 px-4 h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-2">Access Denied</p>
          <p>You need admin privileges to access this page</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 h-screen">
      <h1 className="text-3xl font-bold mb-2">Startup Applications</h1>
      <p className="text-gray-600 mb-6">Here are all startup applications</p>

      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-gray-500">{totalCount} results</p>

        <div className="flex gap-4">
          <div className="flex gap-2">
            <Input
              placeholder="Search for an job application"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[400px]"
            />
            <Button
              onClick={() => user && !loading && fetchApplications()}
              disabled={!user || loading}
            >
              Search
            </Button>
          </div>
        </div>
      </div>

      <div className="overflow-y-auto h-[calc(80vh)]">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="p-3 text-left font-medium text-gray-500">
                Company
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Job title
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Candidate name
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Applied Date
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Status
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Application Docs
              </th>

              <th className="p-3 text-left font-medium text-gray-500">
                Accept
              </th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : applications.length === 0 ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  No applications found
                </td>
              </tr>
            ) : (
              applications.map((application) => (
                <tr key={application.id} className="border-b">
                  <td className="p-3">{application.company_name || 'N/A'}</td>
                  <td className="p-3">{application.job_title || 'N/A'}</td>
                  <td className="p-3">{application.candidate || 'N/A'}</td>
                  <td className="p-3">
                    {application.creation_date
                      ? new Date(application.creation_date).toLocaleDateString(
                          'en-US',
                          {
                            month: 'short',
                            day: '2-digit',
                            year: '2-digit'
                          }
                        )
                      : 'N/A'}
                  </td>
                  <td className="p-3">
                    {/* Read-only status display */}
                    <Badge
                      className={`${getStatusBadgeColor(application.status || '')} text-white`}
                      variant="outline"
                    >
                      {application.status}
                    </Badge>
                  </td>
                  <td className="p-3">
                    <div className="flex flex-col gap-1">
                      {application.resume && (
                        <button
                          onClick={() => openDocument(application, 'resume')}
                          className="bg-gray-700 text-white text-xs px-2 py-1 rounded flex items-center justify-center"
                        >
                          Resume
                        </button>
                      )}
                      {application.cover_note_file && (
                        <button
                          onClick={() => openDocument(application, 'cover')}
                          className="bg-gray-700 text-white text-xs px-2 py-1 rounded flex items-center justify-center"
                        >
                          Cover...
                        </button>
                      )}
                      {application.additional_materials && (
                        <button
                          onClick={() =>
                            openDocument(application, 'additional')
                          }
                          className="bg-gray-700 text-white text-xs px-2 py-1 rounded flex items-center justify-center"
                        >
                          Additional
                        </button>
                      )}
                    </div>
                  </td>

                  <td className="p-3">
                    {/* Replace Delete with Accept buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={() =>
                          handleAcceptApplication(application.id, false)
                        }
                        className="bg-red-500 text-white text-xs px-2 py-1 rounded hover:bg-red-600 flex items-center"
                        title="Failed Requirement"
                      >
                        <XCircleIcon className="h-4 w-4 mr-1" />
                        Fail
                      </button>
                      <button
                        onClick={() =>
                          handleAcceptApplication(application.id, true)
                        }
                        className="bg-[#118073] text-white text-xs px-2 py-1 rounded hover:bg-green-600 flex items-center"
                        title="Accept as Accepted"
                      >
                        <CheckCircleIcon className="h-4 w-4 mr-1" />
                        Accept
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      <Dialog
        open={isDocumentDialogOpen}
        onOpenChange={setIsDocumentDialogOpen}
      >
        <DialogContent className="sm:max-w-[800px] h-[80vh]">
          <DialogHeader>
            <DialogTitle>
              {documentType === 'resume'
                ? 'Resume'
                : documentType === 'cover'
                  ? 'Cover Letter'
                  : 'Additional Materials'}
            </DialogTitle>
          </DialogHeader>
          <div className="h-full">
            {getDocumentUrl() && (
              <iframe
                src={getDocumentUrl()}
                className="w-full h-full border-0"
                title="Document Viewer"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
