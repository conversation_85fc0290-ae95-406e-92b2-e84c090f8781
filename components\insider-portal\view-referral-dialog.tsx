'use client';

import type * as React from 'react';
import { Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

interface ReferralDetails {
  id: string;
  jobTitle: string;
  jobLink?: string;
  candidateName: string;
  companyName: string;
  location?: string;
  date: string;
  resume?: string;
  additionalMaterials?: string;
  coverLetter?: string;
}

interface ViewReferralDialogProps {
  referral: ReferralDetails;
  trigger?: React.ReactNode;
}

export function ViewReferralDialog({
  referral,
  trigger
}: ViewReferralDialogProps) {
  const DocumentPreview = ({
    type,
    label,
    url
  }: {
    type: 'resume' | 'additional' | 'coverLetter';
    label: string;
    url?: string;
  }) => (
    <div className="space-y-2">
      <Label htmlFor={type}>{label}</Label>
      <Card className="relative">
        <CardContent className="flex items-center justify-between p-3">
          <div className="text-sm truncate max-w-[200px]">
            {url ? url.split('/').pop() : 'No document uploaded'}
          </div>
          {url && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                // Open document in new tab
                window.open(url, '_blank');
              }}
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View {label}</span>
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="icon">
            <Eye className="h-4 w-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Referral Details</DialogTitle>
          <DialogDescription>
            Detailed information about this referral request.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Job Title</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {referral.jobTitle}
              </div>
            </div>
            <div className="space-y-2">
              <Label>Job Link</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {referral.jobLink ? (
                  <a
                    href={referral.jobLink}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline truncate block"
                  >
                    {referral.jobLink}
                  </a>
                ) : (
                  <span className="text-muted-foreground">Not provided</span>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Applicant Name</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {referral.candidateName}
              </div>
            </div>
            <div className="space-y-2">
              <Label>Company Name</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {referral.companyName}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Location</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {referral.location || (
                  <span className="text-muted-foreground">Not provided</span>
                )}
              </div>
            </div>
            <div className="space-y-2">
              <Label>Date</Label>
              <div className="p-2 border rounded-md bg-muted/20">
                {referral.date}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <DocumentPreview
              type="resume"
              label="Resume"
              url={referral.resume}
            />
            <DocumentPreview
              type="additional"
              label="Additional Materials"
              url={referral.additionalMaterials}
            />
            <DocumentPreview
              type="coverLetter"
              label="Cover Letter"
              url={referral.coverLetter}
            />
          </div>

          <div className="flex justify-end">
            <DialogTrigger asChild>
              <Button>Close</Button>
            </DialogTrigger>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
