2025-03-27T18:09:52.307Z - No user session found 
2025-03-27T18:09:52.439Z - No user session found 
2025-03-27T18:16:38.766Z - No user session found 
2025-03-27T18:16:39.018Z - No user session found 
2025-03-27T18:17:59.000Z - No user session found 
2025-03-27T18:17:59.301Z - No user session found 
2025-03-27T18:19:10.583Z - No user session found 
2025-03-27T18:19:10.806Z - No user session found 
2025-03-27T20:41:48.955Z - No user session found when fetching subscription 
2025-03-27T20:41:49.114Z - No user session found when fetching subscription 
2025-03-27T20:42:17.668Z - No user session found when fetching subscription 
2025-03-27T20:42:17.690Z - No user session found when fetching subscription 
2025-03-27T20:43:29.764Z - No user session found when fetching subscription 
2025-03-27T20:43:29.963Z - No user session found when fetching subscription 
2025-03-27T20:44:15.856Z - Attempting to get user session 
2025-03-27T20:44:15.857Z - No user session found when fetching subscription 
2025-03-27T20:44:16.145Z - Attempting to get user session 
2025-03-27T20:44:16.146Z - No user session found when fetching subscription 
2025-03-27T20:44:30.393Z - Attempting to get user session 
2025-03-27T20:44:30.394Z - No user session found when fetching subscription 
2025-03-27T20:44:30.752Z - Attempting to get user session 
2025-03-27T20:44:30.753Z - No user session found when fetching subscription 
2025-03-27T20:45:21.394Z - Attempting direct user lookup in getCurrentUserSubscription 
2025-03-27T20:45:21.395Z - No session found, attempting to get user via request cookie 
2025-03-27T20:45:21.395Z - All auth methods failed, returning null 
2025-03-27T20:45:21.702Z - Attempting direct user lookup in getCurrentUserSubscription 
2025-03-27T20:45:21.703Z - No session found, attempting to get user via request cookie 
2025-03-27T20:45:21.703Z - All auth methods failed, returning null 
2025-03-27T20:45:59.639Z - Attempting to get user session 
2025-03-27T20:45:59.641Z - No user session found when fetching subscription 
2025-03-27T20:45:59.814Z - Attempting to get user session 
2025-03-27T20:45:59.815Z - No user session found when fetching subscription 
2025-03-27T20:50:10.339Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T20:50:10.342Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T20:50:10.456Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "active",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T20:50:10.690Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T20:50:10.691Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T20:50:11.009Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "active",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:01:23.135Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:01:23.138Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:01:23.237Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:01:23.586Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:01:23.587Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:01:23.677Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:04:06.252Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:04:06.254Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:04:06.493Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:04:06.494Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:04:06.951Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:04:06.952Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:04:07.159Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:04:07.160Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:05:02.294Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": false
  }
}
2025-03-27T21:05:03.283Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": false
  }
}
2025-03-27T21:05:24.132Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:05:24.134Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:05:24.320Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:05:24.320Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:05:24.790Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:05:24.791Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:05:24.996Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:05:24.997Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:06:58.208Z - Checking subscription for userId: {
  "userId": "1740045473424x519467489014936450"
}
2025-03-27T21:06:58.537Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:06:58.651Z - Found subscription but status is invalid {
  "status": "inactive",
  "allowed": [
    "active",
    "trialing"
  ]
}
2025-03-27T21:06:58.827Z - Checking subscription for userId: {
  "userId": "1740045473424x519467489014936450"
}
2025-03-27T21:06:59.166Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:06:59.369Z - Found subscription but status is invalid {
  "status": "inactive",
  "allowed": [
    "active",
    "trialing"
  ]
}
2025-03-27T21:07:12.694Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:07:12.696Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:07:12.921Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:07:12.922Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:07:13.412Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:07:13.413Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:07:13.601Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:07:13.602Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:07:17.881Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:07:17.882Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:07:18.206Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:07:18.207Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:07:18.506Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:07:18.507Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:07:18.734Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:07:18.735Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:09:45.551Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:09:45.553Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:09:45.752Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:09:45.753Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:09:46.101Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": true
  }
}
2025-03-27T21:09:46.103Z - User has active subscription flag, checking for subscription record {
  "subscriptionId": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07"
}
2025-03-27T21:09:46.296Z - Found subscription record in database {
  "subscription": {
    "id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "cancelable_date": null,
    "end_date": "2025-03-31T12:02:03",
    "owner_email": "<EMAIL>",
    "plan": "Pro Monthly Pass",
    "start_date": "2025-03-24T12:02:39",
    "status": "inactive",
    "subscription_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "creation_date": "2025-03-24T12:02:39",
    "modified_date": "2025-03-24T15:04:38.82",
    "slug": "prod_RzqD0GUVzX4W3M",
    "creator": "<EMAIL>",
    "bubble_id": "sub_1R69iFSBB0x7N6EyxMC7IiMH",
    "owner_id": "1740045473424x519467489014936450"
  }
}
2025-03-27T21:09:46.297Z - Subscription found but status is not active/trialing {
  "status": "inactive"
}
2025-03-27T21:10:41.954Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": false
  }
}
2025-03-27T21:10:42.419Z - User data retrieved {
  "userData": {
    "subscription_id": "d39dd2ca-59d5-4a74-bf30-6adea5a56c07",
    "is_subscription_active": false
  }
}
