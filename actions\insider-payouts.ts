'use server';

import { createClient } from '@/utils/supabase/server';
import { stripe } from '@/utils/stripe/config';
import { revalidatePath } from 'next/cache';
import { v4 as uuidv4 } from 'uuid';
import {
  InsiderPaymentAccount,
  InsiderPayout,
  InsiderPayoutSettings,
  InsiderPayoutFormData,
  InsiderBulkPayoutFormData,
  InsiderPaymentAccountFormData,
  InsiderPayoutSettingsFormData,
  InsiderWithPayoutInfo
} from '@/types/insider-payouts';
// import { User } from '@/types/user';
import PaginatedResponse from '@/types/pagination';
import { getPaymentAccountForUser } from '@/app/(user)/insider/payouts/utils';

/**
 * Get an insider's payment account
 */
export async function getInsiderPaymentAccount(
  insiderIdOrEmail: string,
  isEmail: boolean = false
): Promise<InsiderPaymentAccount | null> {
  console.log(`Getting insider payment account for ${isEmail ? 'email' : 'ID'}: ${insiderIdOrEmail}`);
  const supabase = createClient();

  try {
    // Try to directly get the account if we're using an ID
    if (!isEmail) {
      console.log(`Directly querying with insider ID: ${insiderIdOrEmail}`);
      const { data: directData, error: directError } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('id', insiderIdOrEmail)
        .maybeSingle(); // Use maybeSingle instead of single to avoid errors if not found

      if (directData) {
        console.log('Found payment account directly with ID:', directData);
        return directData as InsiderPaymentAccount;
      }

      if (directError && directError.code !== 'PGRST116') {
        // PGRST116 is the error code for "no rows returned", which is expected if record doesn't exist
        console.error('Error directly fetching insider payment account:', directError);
      } else {
        console.log(`No payment account found directly with ID: ${insiderIdOrEmail}`);
      }
    }

    // If we're using an email or the direct ID lookup failed, try the email path
    if (isEmail) {
      // First get the insider ID from the email
      console.log(`Looking up insider ID for email: ${insiderIdOrEmail}`);
      const { data: insider, error: insiderError } = await supabase
        .from('insiders')
        .select('id')
        .eq('user_email', insiderIdOrEmail)
        .maybeSingle();

      if (insiderError && insiderError.code !== 'PGRST116') {
        console.error('Error finding insider by email:', insiderError);
        return null;
      }

      if (!insider) {
        console.log(`No insider found with email: ${insiderIdOrEmail}`);
        return null;
      }

      console.log(`Found insider with ID: ${insider.id} for email: ${insiderIdOrEmail}`);

      // Now get the payment account with the insider ID
      const { data, error } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .eq('id', insider.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        console.error(`Error fetching payment account for insider ID ${insider.id}:`, error);
        return null;
      }

      if (!data) {
        console.log(`No payment account found for insider ID: ${insider.id}`);
        return null;
      }

      console.log('Found payment account via email lookup:', data);
      return data as InsiderPaymentAccount;
    }

    // If we get here, no account was found
    console.log(`No payment account found for ${isEmail ? 'email' : 'ID'}: ${insiderIdOrEmail}`);
    return null;
  } catch (error) {
    console.error('Error in getInsiderPaymentAccount:', error);
    return null;
  }
}

/**
 * Create or update an insider's payment account
 */
export async function upsertInsiderPaymentAccount(
  insiderId: string,
  accountData: InsiderPaymentAccountFormData
): Promise<InsiderPaymentAccount> {
  const supabase = createClient();

  // Check if the account already exists
  const { data: existingAccount } = await supabase
    .from('insider_payment_accounts')
    .select('*')
    .eq('id', insiderId)
    .single();

  const now = new Date().toISOString();

  // Prepare the data to insert or update
  const accountRecord = {
    id: insiderId,
    payment_method: accountData.payment_method,
    updated_at: now
  };

  if (existingAccount) {
    // Update existing account
    const { data, error } = await supabase
      .from('insider_payment_accounts')
      .update(accountRecord)
      .eq('id', insiderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating insider payment account:', error);
      throw new Error('Failed to update payment account');
    }

    revalidatePath('/insider/payouts');
    return data as InsiderPaymentAccount;
  } else {
    // Create new account
    const { data, error } = await supabase
      .from('insider_payment_accounts')
      .insert({
        ...accountRecord,
        created_at: now
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating insider payment account:', error);
      throw new Error('Failed to create payment account');
    }

    revalidatePath('/insider/payouts');
    return data as InsiderPaymentAccount;
  }
}

/**
 * Create a Stripe Connect account for an insider
 */
export async function createStripeConnectAccount(
  insiderId: string,
  email: string,
  firstName: string,
  lastName: string
): Promise<{ accountId: string; accountLink: string }> {
  try {
    console.log(`Creating Stripe Connect account for user ID: ${insiderId}, email: ${email}`);
    const supabase = createClient();

    // First, check if an insider record exists for this email
    const { data: existingInsider, error: insiderError } = await supabase
      .from('insiders')
      .select('*')
      .eq('user_email', email)
      .maybeSingle();

    if (insiderError && insiderError.code !== 'PGRST116') {
      console.error('Error checking for existing insider:', insiderError);
    }

    // Generate a new insiderId if needed
    let actualInsiderId = existingInsider?.id;

    // If insider doesn't exist, create it with a new UUID
    if (!existingInsider) {
      // Generate a unique ID for the insider (don't use the user's auth ID)
      const newInsiderId = uuidv4();
      console.log(`Creating new insider record with ID: ${newInsiderId} for email: ${email}`);

      try {
        const { data: newInsider, error: createError } = await supabase
          .from('insiders')
          .insert({
            id: newInsiderId,
            user_email: email,
            first_name: firstName,
            last_name: lastName,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();

        if (createError) {
          console.error('Error creating insider record:', createError);
          // Continue without the insider record - we'll store email directly
        } else {
          console.log('Successfully created insider record:', newInsider);
          actualInsiderId = newInsider.id;
        }
      } catch (createError) {
        console.error('Exception creating insider record:', createError);
        // Continue without the insider record
      }
    } else {
      console.log(`Found existing insider with ID: ${existingInsider.id}`);
    }

    // Check for existing payment account with this email
    try {
      // First check our database for any existing payment account with this email
      const { data: existingAccounts } = await supabase
        .from('insider_payment_accounts')
        .select('*')
        .or(`email.eq."${email}",user_id.eq."${insiderId}"`)
        .order('created_at', { ascending: false })
        .limit(1);

      if (existingAccounts && existingAccounts.length > 0 && existingAccounts[0].stripe_account_id) {
        const existingAccount = existingAccounts[0];
        console.log(`Found existing payment account with Stripe ID: ${existingAccount.stripe_account_id}`);

        // Verify the Stripe account still exists
        try {
          const stripeAccount = await stripe.accounts.retrieve(existingAccount.stripe_account_id);

          // Create a new account link for the existing account
          const accountLink = await stripe.accountLinks.create({
            account: stripeAccount.id,
            refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/insider/payouts?refresh=true`,
            return_url: `${process.env.NEXT_PUBLIC_APP_URL}/insider/payouts?success=true&accountId=${stripeAccount.id}`,
            type: 'account_onboarding',
          });

          return { accountId: stripeAccount.id, accountLink: accountLink.url };
        } catch (stripeError) {
          console.error(`Error retrieving Stripe account ${existingAccount.stripe_account_id}:`, stripeError);
          // The Stripe account may have been deleted, continue to create a new one
        }
      }
    } catch (dbError) {
      console.error('Error checking for existing payment accounts:', dbError);
      // Continue to create a new account
    }

    // Create a Stripe Connect account
    console.log('Creating new Stripe Connect account...');
    const account = await stripe.accounts.create({
      type: 'express',
      email: email,
      business_type: 'individual',
      individual: {
        first_name: firstName,
        last_name: lastName,
      },
      capabilities: {
        transfers: { requested: true },
        card_payments: { requested: true }
      },
      metadata: {
        userId: insiderId,
        insiderId: actualInsiderId,
        email: email
      }
    });

    console.log(`Successfully created Stripe account with ID: ${account.id}`);

    // Create an account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/insider/payouts?refresh=true`,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/insider/payouts?success=true&accountId=${account.id}`,
      type: 'account_onboarding',
    });

    // Create the payment account record
    await createOrUpdatePaymentAccount(supabase, account.id, actualInsiderId, email, insiderId);

    revalidatePath('/insider/payouts');
    return { accountId: account.id, accountLink: accountLink.url };
  } catch (error) {
    console.error('Error creating Stripe Connect account:', error);
    throw new Error(`Failed to create Stripe Connect account: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Helper function to create or update a payment account record
 */
async function createOrUpdatePaymentAccount(
  supabase: any,
  stripeAccountId: string,
  insiderId: string | undefined,
  email: string,
  userId: string
): Promise<void> {
  // Generate a unique ID for the payment account record
  const paymentAccountId = uuidv4();

  // First try the admin API
  try {
    console.log(`Creating payment account record with ID: ${paymentAccountId}`);
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/admin/create-payment-account`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        accountId: paymentAccountId,
        insiderId: insiderId,
        userEmail: email,
        userId: userId,
        stripeAccountId: stripeAccountId,
        status: 'pending'
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Error response from admin create endpoint:', errorData);
      throw new Error(`Admin create failed: ${errorData.error || 'Unknown error'}`);
    }

    const result = await response.json();
    console.log('Successfully created payment account record via admin API:', result);
    return;
  } catch (apiError) {
    console.error('Error creating payment account via admin API:', apiError);
  }

  // Try direct insert with full fields
  try {
    console.log(`Trying direct insert with ID: ${paymentAccountId}`);
    const { error: insertError } = await supabase
      .from('insider_payment_accounts')
      .insert({
        id: paymentAccountId,
        insider_id: insiderId,
        email: email,
        user_id: userId,
        stripe_account_id: stripeAccountId,
        account_status: 'pending',
        payment_method: 'stripe',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (insertError) {
      console.error('Direct insert failed:', insertError);
      throw new Error(`Direct insert failed: ${insertError.message}`);
    }

    console.log('Successfully created payment account record via direct insert');
    return;
  } catch (insertError) {
    console.error('Error in direct insert:', insertError);
  }

  // Last resort: Try with only the original columns
  try {
    console.log(`Trying basic insert with ID: ${paymentAccountId}`);
    const { error: basicInsertError } = await supabase
      .from('insider_payment_accounts')
      .insert({
        id: paymentAccountId,
        stripe_account_id: stripeAccountId,
        account_status: 'pending',
        payment_method: 'stripe',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (basicInsertError) {
      console.error('Basic insert also failed:', basicInsertError);
      // At this point, we've tried everything - just log the error and continue
      // The Stripe account was created successfully, so the user can still proceed
    } else {
      console.log('Successfully created basic payment account record');
    }
  } catch (basicError) {
    console.error('Error in basic insert:', basicError);
  }
}

/**
 * Get an insider's payout settings
 */
export async function getInsiderPayoutSettings(
  insiderIdOrEmail: string,
  isEmail: boolean = false
): Promise<InsiderPayoutSettings | null> {
  const supabase = createClient();

  try {
    let query = supabase
      .from('insider_payout_settings')
      .select('*');

    if (isEmail) {
      // First get the insider ID from the email
      const { data: insider, error: insiderError } = await supabase
        .from('insiders')
        .select('id')
        .eq('user_email', insiderIdOrEmail)
        .single();

      if (insiderError) {
        console.error('Error finding insider by email:', insiderError);
        return null;
      }

      query = query.eq('id', insider.id);
    } else {
      query = query.eq('id', insiderIdOrEmail);
    }

    const { data, error } = await query.single();

    if (error) {
      console.error('Error fetching insider payout settings:', error);
      return null;
    }

    return data as InsiderPayoutSettings;
  } catch (error) {
    console.error('Error in getInsiderPayoutSettings:', error);
    return null;
  }
}

/**
 * Create or update an insider's payout settings
 */
export async function upsertInsiderPayoutSettings(
  insiderId: string,
  settingsData: InsiderPayoutSettingsFormData
): Promise<InsiderPayoutSettings> {
  const supabase = createClient();

  // Check if settings already exist
  const { data: existingSettings } = await supabase
    .from('insider_payout_settings')
    .select('*')
    .eq('id', insiderId)
    .single();

  const now = new Date().toISOString();

  // Prepare the data to insert or update
  const settingsRecord = {
    id: insiderId,
    payout_method: settingsData.payout_method,
    payout_threshold: settingsData.payout_threshold,
    payout_frequency: settingsData.payout_frequency,
    updated_at: now
  };

  if (existingSettings) {
    // Update existing settings
    const { data, error } = await supabase
      .from('insider_payout_settings')
      .update(settingsRecord)
      .eq('id', insiderId)
      .select()
      .single();

    if (error) {
      console.error('Error updating insider payout settings:', error);
      throw new Error('Failed to update payout settings');
    }

    revalidatePath('/insider/payouts');
    return data as InsiderPayoutSettings;
  } else {
    // Create new settings
    const { data, error } = await supabase
      .from('insider_payout_settings')
      .insert({
        ...settingsRecord,
        created_at: now
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating insider payout settings:', error);
      throw new Error('Failed to create payout settings');
    }

    revalidatePath('/insider/payouts');
    return data as InsiderPayoutSettings;
  }
}

/**
 * Get an insider's payout history
 */
export async function getInsiderPayouts(
  insiderId: string,
  page: number = 1,
  limit: number = 10
): Promise<PaginatedResponse<InsiderPayout>> {
  const supabase = createClient();
  const startIndex = (page - 1) * limit;

  const { data, error, count } = await supabase
    .from('insider_payouts')
    .select('*', { count: 'exact' })
    .eq('insider_id', insiderId)
    .order('created_at', { ascending: false })
    .range(startIndex, startIndex + limit - 1);

  if (error) {
    console.error('Error fetching insider payouts:', error);
    throw new Error('Failed to fetch payouts');
  }

  return {
    data: data as InsiderPayout[],
    count: count || 0,
    page,
    pageSize: limit
  };
}

/**
 * Create a new payout for an insider
 */
export async function createInsiderPayout(
  payoutData: InsiderPayoutFormData
): Promise<InsiderPayout> {
  const supabase = createClient();

  // Get the insider's payment account directly from the database
  const { data: paymentAccount, error: accountError } = await supabase
    .from('insider_payment_accounts')
    .select('*')
    .eq('insider_id', payoutData.insider_id)
    .single();

  // If there's an error or no payment account, throw an error
  if (accountError || !paymentAccount) {
    console.error('Error fetching payment account:', accountError);
    throw new Error('Insider does not have a payment account set up. The insider must set up their payment account from their dashboard.');
  }

  // Get the insider's available earnings
  const { data: insider, error: insiderError } = await supabase
    .from('insiders')
    .select('earnings_available, earnings_paid')
    .eq('id', payoutData.insider_id)
    .single();

  if (insiderError || !insider) {
    console.error('Error fetching insider:', insiderError);
    throw new Error('Failed to fetch insider information');
  }

  // Check if the insider has enough earnings available
  if (insider.earnings_available < payoutData.amount) {
    throw new Error('Insufficient earnings available for payout');
  }

  const now = new Date().toISOString();
  const payoutId = uuidv4();

  // Create the payout record
  const payoutRecord: Partial<InsiderPayout> = {
    id: payoutId,
    insider_id: payoutData.insider_id,
    amount: payoutData.amount,
    status: 'pending',
    payout_method: payoutData.payout_method,
    stripe_payout_id: null,
    // stripe_transfer_id: null,
    // expected_arrival_date: null,
    // Removed arrival_date field since it doesn't exist in the database schema
    description: payoutData.description || 'Referral earnings payout',
    metadata: null,
    created_at: now,
    updated_at: now
  };

  // If using Stripe, process the payout through Stripe
  if (payoutData.payout_method === 'stripe') {
    if (paymentAccount?.stripe_account_id) {
      try {
        // First, check if the Stripe account has the required capabilities
        const accountDetails = await stripe.accounts.retrieve(paymentAccount.stripe_account_id);
        console.log('Account capabilities:', accountDetails.capabilities);
        
        // Check if we're in development mode and if the BYPASS_CAPABILITY_CHECK env var is set
        const isDevelopment = process.env.NODE_ENV === 'development';
        const bypassCapabilityCheck = process.env.BYPASS_CAPABILITY_CHECK === 'true';
        
        // Only proceed if transfers capability is active OR we're in dev mode with bypass enabled
        if (accountDetails.capabilities?.transfers !== 'active' && !(isDevelopment && bypassCapabilityCheck)) {
          console.error('Account does not have active transfer capability');
          console.log(`isDevelopment: ${isDevelopment}, bypassCapabilityCheck: ${bypassCapabilityCheck}`);
          
          payoutRecord.status = 'failed';
          payoutRecord.description = 'Payment failed: Stripe account needs to complete verification';
          
          // Insert the failed payout record
          const { data, error } = await supabase
            .from('insider_payouts')
            .insert(payoutRecord)
            .select()
            .single();
            
          if (error) {
            console.error('Error inserting failed payout record:', error);
            throw new Error('Failed to create payout record');
          }
          
          throw new Error('Stripe account needs to complete verification. The user needs to finish their Stripe Connect onboarding process.');
        }
        
        // If we're bypassing capability checks in development, log it
        if (isDevelopment && bypassCapabilityCheck && accountDetails.capabilities?.transfers !== 'active') {
          console.log('WARNING: Bypassing Stripe capability check in development mode!');
        }
        // First, create a balance transaction to the connected account
        const transfer = await stripe.transfers.create({
          amount: payoutData.amount,
          currency: 'usd',
          destination: paymentAccount?.stripe_account_id,
          metadata: {
            insider_id: payoutData.insider_id,
            insider_payout_id: payoutId
          }
        });

        // Then, create a payout from the connected account to their bank account
        const payout = await stripe.payouts.create(
          {
            amount: payoutData.amount,
            currency: 'usd',
            metadata: {
              insider_id: payoutData.insider_id,
              insider_payout_id: payoutId
            }
          },
          {
            stripeAccount: paymentAccount?.stripe_account_id
          }
        );

        // Update the payout record with the Stripe payout and transfer IDs
        payoutRecord.stripe_payout_id = payout.id;
        // payoutRecord.stripe_transfer_id = transfer.id;
        payoutRecord.status = 'processing';
        // Set expected arrival date based on Stripe payout estimation
        // if (payout.arrival_date) {
        //   payoutRecord.expected_arrival_date = new Date(payout.arrival_date * 1000).toISOString();
        // }
        payoutRecord.metadata = {
          stripe_transfer: transfer,
          stripe_payout: payout
        };
      } catch (error) {
        console.error('Error processing Stripe payout:', error);
        // Don't throw an error, just mark as pending for manual processing
        console.log('Marking payout as pending due to Stripe processing error');
        payoutRecord.status = 'pending';
        payoutRecord.metadata = {
          note: 'Pending manual processing - Stripe processing error',
          error: error instanceof Error ? error.message : String(error)
        };
      }
    } else {
      // No Stripe account ID yet, mark as pending for manual processing
      console.log('No Stripe account ID found, marking payout as pending for manual processing');
      payoutRecord.status = 'pending';
      payoutRecord.metadata = {
        note: 'Pending manual processing - Stripe account not connected'
      };
    }
  }

  // Insert the payout record
  const { data, error } = await supabase
    .from('insider_payouts')
    .insert(payoutRecord)
    .select()
    .single();

  if (error) {
    console.error('Error creating insider payout:', error);
    throw new Error('Failed to create payout');
  }

  // Update the insider's earnings
  await supabase
    .from('insiders')
    .update({
      earnings_available: insider.earnings_available - payoutData.amount,
      earnings_paid: (insider.earnings_paid || 0) + payoutData.amount
    })
    .eq('id', payoutData.insider_id);

  revalidatePath('/admin/insiders/payouts');
  revalidatePath('/insider/payouts');

  return data as InsiderPayout;
}

/**
 * Get all insiders with their payout information
 */
export async function getInsidersWithPayoutInfo(
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ''
): Promise<PaginatedResponse<InsiderWithPayoutInfo>> {
  const supabase = createClient();
  const startIndex = (page - 1) * limit;

  let query = supabase
    .from('insiders')
    .select(`
      id,
      first_name,
      last_name,
      user_email,
      earnings_available,
      earnings_paid,
      earnings_pending,
      insider_payment_accounts (*)
    `, { count: 'exact' });

  if (searchTerm) {
    query = query.or(
      `first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,user_email.ilike.%${searchTerm}%`
    );
  }

  const { data, error, count } = await query
    .order('earnings_available', { ascending: false })
    .range(startIndex, startIndex + limit - 1);

  if (error) {
    console.error('Error fetching insiders with payout info:', error);
    throw new Error('Failed to fetch insiders');
  }

  // Transform the data to match the InsiderWithPayoutInfo interface
  const transformedData = data.map(insider => {
    const paymentAccount = insider.insider_payment_accounts?.[0] || null;

    return {
      id: insider.id,
      first_name: insider.first_name,
      last_name: insider.last_name,
      user_email: insider.user_email,
      earnings_available: insider.earnings_available || 0,
      earnings_paid: insider.earnings_paid || 0,
      earnings_pending: insider.earnings_pending || 0,
      stripe_account_id: paymentAccount?.stripe_account_id || null,
      account_status: paymentAccount?.account_status || 'not_setup',
      payment_method: paymentAccount?.payment_method || 'none',
      created_at: paymentAccount?.created_at || null,
      updated_at: paymentAccount?.updated_at || null
    };
  });

  return {
    data: transformedData as InsiderWithPayoutInfo[],
    count: count || 0,
    page,
    pageSize: limit
  };
}

/**
 * Process automatic payouts for eligible insiders
 */
export async function processAutomaticPayouts(): Promise<number> {
  const supabase = createClient();

  // Get all insiders with automatic payout settings and available earnings
  const { data: eligibleInsiders, error } = await supabase
    .from('insiders')
    .select(`
      id,
      earnings_available,
      insider_payment_accounts!inner(stripe_account_id, account_status),
      insider_payout_settings!inner(payout_method, payout_threshold)
    `)
    .gt('earnings_available', 0)
    .eq('insider_payment_accounts.account_status', 'active')
    .eq('insider_payout_settings.payout_method', 'automatic');

  if (error) {
    console.error('Error fetching eligible insiders for automatic payouts:', error);
    throw new Error('Failed to fetch eligible insiders');
  }

  let processedCount = 0;

  // Process payouts for each eligible insider
  for (const insider of eligibleInsiders) {
    // Skip if earnings are below threshold
    if (insider.earnings_available < insider.insider_payout_settings[0].payout_threshold) {
      continue;
    }

    try {
      // Create a payout for the insider
      await createInsiderPayout({
        insider_id: insider.id,
        amount: insider.earnings_available,
        payout_method: 'stripe',
        description: 'Automatic payout of referral earnings'
      });

      processedCount++;
    } catch (error) {
      console.error(`Error processing automatic payout for insider ${insider.id}:`, error);
      // Continue with the next insider
    }
  }

  return processedCount;
}

/**
 * Process bulk payouts for multiple insiders
 */
export async function processBulkPayouts(
  insiderIds: string[],
  payoutData: InsiderBulkPayoutFormData
): Promise<{ success: number; failed: number }> {
  const supabase = createClient();
  let successCount = 0;
  let failedCount = 0;

  // Process each insider in the list
  for (const insiderId of insiderIds) {
    try {
      // Get the insider's available earnings
      const { data: insider, error: insiderError } = await supabase
        .from('insiders')
        .select('earnings_available')
        .eq('id', insiderId)
        .single();

      if (insiderError || !insider || insider.earnings_available <= 0) {
        console.error(`Error or insufficient earnings for insider ${insiderId}:`, insiderError || 'No earnings available');
        failedCount++;
        continue;
      }

      // We'll create the payment account automatically in createInsiderPayout if needed

      // Create a payout for this insider
      await createInsiderPayout({
        insider_id: insiderId,
        amount: insider.earnings_available,
        payout_method: payoutData.payout_method,
        description: payoutData.description || 'Bulk payout of referral earnings'
      });

      successCount++;
    } catch (error) {
      console.error(`Error processing payout for insider ${insiderId}:`, error);
      failedCount++;
    }
  }

  revalidatePath('/admin/insiders/payouts');
  return { success: successCount, failed: failedCount };
}

/**
 * Add to an insider's available earnings balance
 */
export async function addToInsiderBalance(
  insiderId: string,
  amount: number,
  description: string = 'Manual balance adjustment'
): Promise<{ success: boolean; newBalance: number }> {
  const supabase = createClient();

  // Get the insider's current earnings
  const { data: insider, error: insiderError } = await supabase
    .from('insiders')
    .select('earnings_available')
    .eq('id', insiderId)
    .single();

  if (insiderError || !insider) {
    console.error('Error fetching insider:', insiderError);
    throw new Error('Failed to fetch insider information');
  }

  // Calculate the new balance
  const newBalance = (insider.earnings_available || 0) + amount;

  // Update the insider's earnings
  const { error } = await supabase
    .from('insiders')
    .update({
      earnings_available: newBalance
    })
    .eq('id', insiderId);

  if (error) {
    console.error('Error updating insider balance:', error);
    throw new Error('Failed to update insider balance');
  }

  // Create a record of this adjustment
  const now = new Date().toISOString();
  const adjustmentId = uuidv4();

  const { error: recordError } = await supabase
    .from('insider_payouts')
    .insert({
      id: adjustmentId,
      insider_id: insiderId,
      amount: amount,
      status: 'completed',
      payout_method: 'manual_adjustment',
      description: description,
      metadata: { type: 'balance_adjustment', previous_balance: insider.earnings_available },
      created_at: now,
      updated_at: now
    });

  if (recordError) {
    console.error('Error recording balance adjustment:', recordError);
    // Don't throw here, as the balance was already updated
  }

  revalidatePath('/admin/insiders/payouts');
  revalidatePath('/insider/payouts');

  return { success: true, newBalance };
}
