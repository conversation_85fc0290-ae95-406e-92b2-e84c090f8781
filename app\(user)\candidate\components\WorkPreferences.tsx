'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { workPreferencesSchema, jobTypes, workTypes } from '../constants';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Pencil, Check } from 'lucide-react';
import { CandidatePreferencesUpdateData } from '@/types/user';
import { updateCandidatePreferences } from '@/actions/admin/candidate';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

interface WorkPreferencesProps {
  user: any;
  localUserData: any;
  setLocalUserData: (data: any) => void;
}

export function WorkPreferences({
  user,
  localUserData,
  setLocalUserData
}: WorkPreferencesProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const workPreferencesForm = useForm<z.infer<typeof workPreferencesSchema>>({
    resolver: zodResolver(workPreferencesSchema),
    defaultValues: {
      jobTypes: user?.work_preferences || [],
      workTypes: user?.work_types || [],
      activelyLooking: user?.seeking_job || false,
      seekingCoaching: user?.seeking_training || false
    }
  });

  async function onSubmit(values: z.infer<typeof workPreferencesSchema>) {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'User ID not found',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const updateData: CandidatePreferencesUpdateData = {
        work_preferences: values.jobTypes,
        work_types: values.workTypes,
        seeking_job: values.activelyLooking,
        seeking_training: values.seekingCoaching
      };

      await updateCandidatePreferences(user.id, updateData);

      updateLocalUserData(updateData);

      toast({
        title: 'Success',
        description: 'Work preferences updated successfully'
      });

      setIsEditing(false);
      router.refresh();
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to update preferences',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  function updateLocalUserData(newData: any) {
    setLocalUserData((prev: any) => ({
      ...prev,
      ...newData
    }));
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Work Preferences</CardTitle>
            <CardDescription>
              Select the types of jobs that you are interested in applying to in
              InterUp.
            </CardDescription>
          </div>
          {!isEditing && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Pencil className="h-4 w-4 mr-2" />
              Edit
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isEditing ? (
          <Form {...workPreferencesForm}>
            <form
              onSubmit={workPreferencesForm.handleSubmit(onSubmit)}
              className="space-y-6"
            >
              <FormField
                control={workPreferencesForm.control}
                name="jobTypes"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>Job Types</FormLabel>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      {jobTypes.map((jobType) => (
                        <FormField
                          key={jobType}
                          control={workPreferencesForm.control}
                          name="jobTypes"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={jobType}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(jobType)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([
                                            ...field.value,
                                            jobType
                                          ])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== jobType
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {jobType}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={workPreferencesForm.control}
                name="workTypes"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel>Work Type</FormLabel>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {workTypes.map((workType) => (
                        <FormField
                          key={workType}
                          control={workPreferencesForm.control}
                          name="workTypes"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={workType}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(workType)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([
                                            ...field.value,
                                            workType
                                          ])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== workType
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  {workType}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="space-y-4">
                <FormField
                  control={workPreferencesForm.control}
                  name="activelyLooking"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Are you currently actively looking for a position?
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={workPreferencesForm.control}
                  name="seekingCoaching"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          Are you currently actively looking for career
                          coaching?
                        </FormLabel>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex gap-2 justify-end">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-[#118073] hover:bg-[#118073]/90"
                >
                  Save Changes
                </Button>
              </div>
            </form>
          </Form>
        ) : (
          <div className="space-y-6">
            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">
                Selected Job Types
              </h4>
              <div className="flex flex-wrap gap-2">
                {localUserData?.work_preferences?.map((jobType: string) => (
                  <Badge
                    key={jobType}
                    variant="secondary"
                    className="bg-[#118073] hover:bg-[#118073]/90 border-none text-white"
                  >
                    {jobType}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">
                Work Type Preferences
              </h4>
              <div className="flex flex-wrap gap-2">
                {localUserData?.work_types?.map((workType: string) => (
                  <Badge
                    key={workType}
                    variant="secondary"
                    className="bg-[#118073] hover:bg-[#118073]/90 border-none text-white"
                  >
                    {workType}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="text-sm font-medium text-muted-foreground">
                Job Search Status
              </h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Check
                    className={`h-4 w-4 ${localUserData?.seeking_job ? 'text-[#118073]' : 'text-muted-foreground'}`}
                  />
                  <span>
                    {localUserData?.seeking_job
                      ? 'Actively looking for a position'
                      : 'Not actively looking for a position'}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Check
                    className={`h-4 w-4 ${localUserData?.seeking_training ? 'text-[#118073]' : 'text-muted-foreground'}`}
                  />
                  <span>
                    {localUserData?.seeking_training
                      ? 'Seeking career coaching'
                      : 'Not seeking career coaching'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
