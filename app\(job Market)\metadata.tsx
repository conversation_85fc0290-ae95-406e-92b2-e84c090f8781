import { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    default: 'InternUp Job Market - Find Your Perfect Opportunity',
    template: '%s | InternUp Jobs'
  },
  description:
    'Browse thousands of internship and job opportunities tailored for international students. Find your perfect match with our advanced job search platform.',
  keywords: [
    'job search',
    'internship opportunities',
    'international student jobs',
    'entry level positions',
    'career opportunities',
    'job listings',
    'work abroad',
    'student employment',
    'job matching',
    'career search'
  ],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    // url: siteConfig.url, // Uncomment when siteConfig is available
    title: 'InternUp Job Market',
    description:
      'Find your perfect internship or job opportunity as an international student',
    siteName: 'InternUp'
    // images: [
    //   {
    //     url: '/job-market-og.jpg',
    //     width: 1200,
    //     height: 630,
    //     alt: 'InternUp Job Market Platform'
    //   }
    // ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'InternUp Job Market',
    description:
      'Find your perfect internship or job opportunity as an international student'
    // images: ['/job-market-twitter.jpg']
  },
  alternates: {
    // canonical: `${siteConfig.url}/jobs` // Uncomment when siteConfig is available
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-snippet': -1,
      'max-image-preview': 'large',
      'max-video-preview': -1
    }
  }
};
