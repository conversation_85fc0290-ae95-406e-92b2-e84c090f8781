"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import { checkoutWithStripe } from "@/utils/stripe/server"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"

interface CheckoutButtonProps {
  priceId: string
  ctaText: string
  variant?: "default" | "outline"
  className?: string
  user: any
  loading: boolean
}

export function CheckoutButton({
  priceId,
  ctaText,
  variant = "default",
  className = "",
  user,
  loading,
}: CheckoutButtonProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const { toast } = useToast()
  const router = useRouter()

  const handleCheckout = async () => {
    if (!user) {
      router.push("/signin")
      return
    }

    setIsProcessing(true)
    try {
      const { sessionUrl, errorRedirect } = await checkoutWithStripe(priceId, user)

      if (errorRedirect) {
        toast({
          title: "Error",
          description: "Could not redirect to checkout. Please try again.",
          variant: "destructive",
        })
        return
      }

      if (sessionUrl) {
        console.log("This is the sessionUrl", sessionUrl)
        window.location.href = sessionUrl
      }
    } catch (error) {
      console.error("Checkout error:", error)
      toast({
        title: "Error",
        description: "Could not process checkout. Please try again later.",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Button className={className} variant={variant} disabled={loading || isProcessing} onClick={handleCheckout}>
      {loading || isProcessing ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
      {loading ? "Loading..." : isProcessing ? "Processing..." : ctaText}
    </Button>
  )
}

