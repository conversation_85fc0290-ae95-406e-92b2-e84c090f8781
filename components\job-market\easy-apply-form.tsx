'use client';

import { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { v4 as uuidv4 } from 'uuid';
import { StartupApplication } from '@/types/startups';
import { createStartupApplication } from '@/actions/job-market/startup-applications';
import { DialogFooter } from '@/components/ui/dialog';
import { Upload } from 'lucide-react';
import { uploadFile } from '@/utils/supabase/storage/client';

// Define the form schema with validation
export const easyApplyFormSchema = z
  .object({
    resume: z.string().optional().or(z.instanceof(File)),
    useExistingResume: z.boolean().default(true),
    coverNote: z.string().optional(),
    yoe_internship: z.string().optional(),
    yoe_job: z.string().optional()
  })
  .refine(
    (data) => {
      // Validate that either useExistingResume is true OR resume is provided
      return data.useExistingResume === true || data.resume;
    },
    {
      message: 'Please provide a resume or use your existing resume',
      path: ['resume']
    }
  );

export type EasyApplyFormValues = z.infer<typeof easyApplyFormSchema>;

interface EasyApplyFormProps {
  user: any;
  jobId: string;
  jobTitle: string;
  companyId?: string;
  companyName: string;
  hasApplied: boolean;
  applicationData?: Partial<StartupApplication> | null;
  onSubmitSuccess: (application: Partial<StartupApplication>) => void;
  onClose: () => void;
}

export function EasyApplyForm({
  user,
  jobId,
  jobTitle,
  companyId,
  companyName,
  hasApplied,
  applicationData,
  onSubmitSuccess,
  onClose
}: EasyApplyFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadingResume, setUploadingResume] = useState(false);
  const [selectedFileName, setSelectedFileName] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Create form with user data pre-filled
  const form = useForm<EasyApplyFormValues>({
    resolver: zodResolver(easyApplyFormSchema),
    defaultValues: {
      resume: '',
      useExistingResume: !!user?.resume_url,
      coverNote: '',
      yoe_internship: user?.yoe_internship || '',
      yoe_job: user?.yoe_job || ''
    }
  });

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (7MB limit)
    if (file.size > 7 * 1024 * 1024) {
      toast({
        title: 'File too large',
        description: 'Please upload a file smaller than 7MB.',
        variant: 'destructive'
      });
      return;
    }

    // Check file type (PDF only)
    if (file.type !== 'application/pdf') {
      toast({
        title: 'Invalid file type',
        description: 'Please upload a PDF file only.',
        variant: 'destructive'
      });
      return;
    }

    setSelectedFileName(file.name);
    form.setValue('resume', file);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const onSubmit = async (values: EasyApplyFormValues) => {
    try {
      setIsSubmitting(true);

      if (hasApplied) {
        toast({
          title: 'Already Applied',
          description: 'You have already applied for this position.',
          variant: 'destructive'
        });
        onClose();
        return;
      }

      let resumeUrl = '';

      // Check if user wants to use existing resume
      if (values.useExistingResume) {
        if (!user?.resume_url) {
          toast({
            title: 'Resume Required',
            description: 'No existing resume found. Please upload a resume.',
            variant: 'destructive'
          });
          return;
        }
        resumeUrl = user.resume_url;
      } else {
        // Upload new resume if it's a file
        if (values.resume instanceof File) {
          setUploadingResume(true);
          const result = await uploadFile({
            file: values.resume,
            bucket: 'candidate-resumes',
            folder: `startup_applications_user_${user.id || user.email}_${jobId}`
          });

          setUploadingResume(false);

          if (result.error) {
            toast({
              title: 'Resume Upload Failed',
              description: result.error,
              variant: 'destructive'
            });
            return;
          }

          resumeUrl = result.fileUrl;
        } else if (typeof values.resume === 'string' && values.resume) {
          // If it's already a URL string
          resumeUrl = values.resume;
        } else {
          toast({
            title: 'Resume Required',
            description: 'Please provide a resume.',
            variant: 'destructive'
          });
          return;
        }
      }

      // Create application object with all required fields
      const application: Partial<StartupApplication> = {
        id: uuidv4(),
        job: jobId,
        job_title: jobTitle,
        company: companyId,
        company_name: companyName,
        candidate: user?.email || '',
        creator: user?.email || '',
        resume: resumeUrl,
        cover_note_file: values.coverNote || '',
        yoe_internship: values.yoe_internship || '',
        yoe_job: values.yoe_job || '',
        status: 'Pending',
        creation_date: new Date().toISOString()
      };

      // Submit application
      await createStartupApplication(application);

      // Call success handler
      onSubmitSuccess(application);

      // Show success toast
      toast({
        title: 'Application Submitted!',
        description: 'Your application has been submitted successfully.'
      });

      // Close the dialog
      onClose();

      // Reset form
      form.reset();
    } catch (error) {
      console.error('Error submitting application:', error);
      toast({
        title: 'Application Failed',
        description:
          'There was an error submitting your application. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium">Resume</h3>
            {user?.resume_url && (
              <Badge variant="outline" className="bg-green-50 text-green-700">
                Resume Available
              </Badge>
            )}
          </div>

          {user?.resume_url ? (
            <>
              <div className="p-4 border rounded-md bg-gray-50">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 flex-shrink-0 bg-blue-100 rounded-md flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-blue-700"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {user.resume_url.split('/').pop() || 'Your Resume'}
                    </p>
                    <p className="text-xs text-gray-500">
                      Will be used for your application
                    </p>
                  </div>
                  <a
                    href={user.resume_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    View
                  </a>
                </div>
              </div>

              <FormField
                control={form.control}
                name="useExistingResume"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 py-2">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        disabled={hasApplied}
                        className="h-4 w-4 text-[#36BA98] border-gray-300 rounded"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Use my existing resume</FormLabel>
                      <FormDescription>
                        Uncheck to upload a different resume for this
                        application
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {!form.watch('useExistingResume') && (
                <FormField
                  control={form.control}
                  name="resume"
                  render={({ field: { value, onChange, ...field } }) => (
                    <FormItem>
                      <FormLabel>Upload New Resume</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          <input
                            type="file"
                            ref={fileInputRef}
                            accept=".pdf"
                            onChange={handleFileChange}
                            className="hidden"
                            disabled={hasApplied}
                          />
                          <div className="flex-1 px-3 py-2 border rounded-md text-sm">
                            {selectedFileName || 'No file selected'}
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleUploadClick}
                            disabled={uploadingResume || hasApplied}
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            {uploadingResume ? 'Uploading...' : 'Upload'}
                          </Button>
                        </div>
                      </FormControl>
                      <FormDescription>
                        Upload your resume (PDF only, max 7MB)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </>
          ) : (
            <FormField
              control={form.control}
              name="resume"
              render={({ field: { value, onChange, ...field } }) => (
                <FormItem>
                  <FormLabel>
                    Resume <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="flex gap-2">
                      <input
                        type="file"
                        ref={fileInputRef}
                        accept=".pdf"
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={hasApplied}
                      />
                      <div className="flex-1 px-3 py-2 border rounded-md text-sm">
                        {selectedFileName || 'No file selected'}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleUploadClick}
                        disabled={uploadingResume || hasApplied}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {uploadingResume ? 'Uploading...' : 'Upload'}
                      </Button>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Please upload your resume (PDF only, max 7MB)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <FormField
          control={form.control}
          name="coverNote"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Cover Note (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Why are you interested in this position? Add any additional information you'd like the employer to know."
                  className="min-h-[120px]"
                  {...field}
                  disabled={hasApplied}
                  readOnly={hasApplied}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {!user?.resume_url &&
          !form.watch('resume') &&
          !form.watch('useExistingResume') && (
            <Alert variant="destructive" className="mt-2">
              <AlertTitle>Resume Required</AlertTitle>
              <AlertDescription>
                You must provide a resume link to apply for this position.
              </AlertDescription>
            </Alert>
          )}

        {hasApplied && (
          <Alert className="mt-4">
            <AlertTitle>Application already submitted</AlertTitle>
            <AlertDescription>
              You've already applied for this position on{' '}
              {applicationData?.creation_date
                ? new Date(applicationData.creation_date).toLocaleDateString()
                : 'recently'}
              . You cannot modify your application through this form.
            </AlertDescription>
          </Alert>
        )}

        <DialogFooter className="gap-2 sm:gap-0">
          <Button type="button" variant="outline" onClick={onClose}>
            {hasApplied ? 'Close' : 'Cancel'}
          </Button>

          {!hasApplied && (
            <Button
              type="submit"
              className="bg-[#36BA98] hover:bg-[#2da885] text-white"
              disabled={isSubmitting || hasApplied}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Application'}
            </Button>
          )}
        </DialogFooter>
      </form>
    </Form>
  );
}
