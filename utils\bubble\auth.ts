import { bubbleClient } from '../bubble/client';
import type { BubbleAuthResponse } from '../bubble/types';
import { createClient } from '../supabase/client';
import { Provider } from '@supabase/supabase-js';
import { setTokens } from './queries';

// Helper function to generate random string ID (similar to companies.ts)
function generateId(): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${randomStr}`;
}

export async function signUp(formData: FormData) {
  try {
    const email = String(formData.get('email')).trim();
    const password = String(formData.get('password')).trim();
    const user_type = String(formData.get('userType')).trim();
    const startup_company = formData.get('company')
      ? String(formData.get('company')).trim()
      : null;

    if (!user_type) {
      return {
        type: 'error',
        title: 'No User Type Selected',
        description: 'Please select a User type'
      };
    }

    // First sign up with Bubble
    const bubbleResponse: BubbleAuthResponse = await bubbleClient.signUp(
      email,
      password,
      user_type
    );

    if (bubbleResponse.status === 'success') {
      // Store tokens from Bubble response
      const { token, user_id } = bubbleResponse.response;
      setTokens(token, user_id);

      // Generate a 6-digit OTP code
      const verificationCode = Math.floor(
        100000 + Math.random() * 900000
      ).toString();
      // Set expiration time (30 minutes from now)
      const verificationExpiration = new Date();
      verificationExpiration.setMinutes(
        verificationExpiration.getMinutes() + 30
      );
      console.log('This is the bubble response', bubbleResponse);
      // Create user in Supabase with Bubble user ID
      const supabase = createClient();
      const { error: supabaseError } = await supabase.from('users').insert({
        id: bubbleResponse.response.user_id,
        email: email,
        user_type: user_type,
        startup_company: startup_company,
        verified: false,
        verification_code: verificationCode,
        verification_expiration: verificationExpiration.toISOString()
      });

      console.log('Supabase error', supabaseError);
      // if (supabaseError) throw new Error('Failed to create user in Supabase', supabaseError);

      // Prepare email content based on user type
      let subject, body;

      if (user_type.toLowerCase() === 'candidate') {
        subject = 'Verify Your InternUp Candidate Account';
        body = `Welcome to InternUp! Your verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
      } else if (user_type.toLowerCase() === 'company') {
        subject = 'Verify Your InternUp Company Account';
        body = `Thank you for registering your company with InternUp! Your verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
      } else {
        subject = 'Verify Your InternUp Account';
        body = `Welcome to InternUp! Your verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
      }

      // Send verification email using the new sendemail workflow
      await bubbleClient.sendEmail(email, subject, body);

      return {
        type: 'success',
        title: 'Verification Required',
        description: 'Please check your email for the verification code.',
        showOTP: true
      };
    }

    return {
      type: 'error',
      title: 'Sign Up Failed',
      description:
        bubbleResponse.message || 'Something went wrong. Please try again.'
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'Sign Up Failed',
      description: error instanceof Error ? error.message : 'An error occurred'
    };
  }
}

export async function verifyEmail(email: string, otp: string) {
  try {
    // Get user data from Supabase to check verification code
    const supabase = createClient();
    const { data, error } = await supabase
      .from('users')
      .select(
        'verification_code, verification_expiration, user_type, startup_company'
      )
      .eq('email', email)
      .single();

    if (error) throw new Error('Error fetching user data');

    // Check if verification code exists and matches
    if (!data || !data.verification_code) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'No verification code found for this email'
      };
    }

    // Check if verification code has expired
    if (data.verification_expiration) {
      const expirationTime = new Date(data.verification_expiration);
      const currentTime = new Date();

      if (currentTime > expirationTime) {
        return {
          type: 'error',
          title: 'Verification Failed',
          description:
            'Verification code has expired. Please request a new one.'
        };
      }
    }

    // Check if the OTP matches
    if (data.verification_code !== otp) {
      return {
        type: 'error',
        title: 'Verification Failed',
        description: 'Invalid verification code'
      };
    }

    // Update verification status in Supabase
    const { error: updateError } = await supabase
      .from('users')
      .update({
        verified: true,
        // Clear the verification code and expiration after successful verification
        verification_code: null,
        verification_expiration: null
      })
      .eq('email', email);

    if (updateError) throw new Error('Error updating verification status');

    // If user type is company, create a company entity in startup_companies table
    if (data.user_type?.toLowerCase() === 'company' && !data.startup_company) {
      try {
        // Get company name from the user's profile or use a default
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('startup_company')
          .eq('email', email)
          .single();

        if (userError)
          throw new Error('Error fetching user data for company creation');

        const companyName =
          userData?.startup_company ||
          `Company-${generateId().substring(0, 8)}`;

        // Create company in startup_companies table
        const { data: companyData, error: companyError } = await supabase
          .from('startup_companies')
          .insert({
            id: generateId(),
            name: companyName,
            creator_email: email,
            created_at: new Date().toISOString()
          })
          .select();

        if (companyError) {
          console.error('Error creating company:', companyError);
        } else if (companyData && companyData.length > 0) {
          // Update user with company name
          const { error: userUpdateError } = await supabase
            .from('users')
            .update({
              startup_company: companyName
            })
            .eq('email', email);

          if (userUpdateError) {
            console.error(
              'Error updating user with company name:',
              userUpdateError
            );
          }
        }
      } catch (companyCreationError) {
        console.error(
          'Error in company creation process:',
          companyCreationError
        );
        // Don't throw here, as we still want the verification to succeed
      }
    }

    // Determine redirect path based on user type
    let redirectPath = '/dashboard';

    if (data && data.user_type) {
      const userType = data.user_type.toLowerCase();
      switch (userType) {
        case 'candidate':
          redirectPath = '/candidate';
          break;
        case 'insider':
          redirectPath = '/insider';
          break;
        case 'company':
          redirectPath = '/company';
          break;
        case 'admin':
          redirectPath = '/admin';
          break;
      }
    }

    return {
      type: 'success',
      title: 'Email Verified',
      description: 'Your email has been verified successfully.',
      redirectPath: redirectPath
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'Verification Failed',
      description: error instanceof Error ? error.message : 'An error occurred'
    };
  }
}

export async function signInWithPassword(formData: FormData) {
  try {
    const email = String(formData.get('email')).trim();
    const password = String(formData.get('password')).trim();
    const response: BubbleAuthResponse = await bubbleClient.signIn(
      email,
      password
    );

    if (response.status === 'success') {
      // Store tokens using the helper function from queries.ts
      const { token, user_id } = response.response;
      setTokens(token, user_id);

      // Get user type directly from database
      const supabase = createClient();
      const { data, error } = await supabase
        .from('users')
        .select('user_type')
        .eq('id', response.response.user_id)
        .single();
      console.log('This is the data', data);
      console.log('This is the error', error);

      if (error) throw new Error('Error fetching user data');

      const userType = data?.user_type?.toLowerCase();
      let redirectPath = '/dashboard';

      // For email/password login, if user_type is admin, redirect to admin
      // Otherwise follow the normal flow based on user type
      if (userType === 'admin') {
        redirectPath = '/admin';
      } else {
        switch (userType) {
          case 'candidate':
            redirectPath = '/candidate';
            break;
          case 'insider':
            redirectPath = '/insider';
            break;
          case 'company':
            redirectPath = '/company';
            break;
        }
      }

      return {
        type: 'success',
        title: 'Signed In',
        description: 'You are now signed in.',
        redirectPath: redirectPath
      };
    }

    return {
      type: 'error',
      title: 'Sign In Failed',
      description: response.message || 'Invalid credentials'
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'Sign In Failed',
      description: error instanceof Error ? error.message : 'An error occurred'
    };
  }
}

export async function signInWithOAuth(provider: string, userType: string) {
  try {
    const supabase = createClient();
    // Ensure the redirectURL is absolute and includes the origin
    const redirectURL = new URL(
      'api/auth/callback',
      window.location.origin
    ).toString();

    // Store user type in localStorage before redirect
    localStorage.setItem('oauth_user_type', userType);

    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider as Provider,
      options: {
        redirectTo: redirectURL,
        queryParams: {
          user_type: userType, // Pass userType as query param
          flow: 'signin' // This is a sign-in flow
        },
        scopes:
          provider === 'linkedin' ? 'r_emailaddress r_liteprofile' : undefined
      }
    });

    if (error) throw error;

    return {
      type: 'success',
      title: 'Redirecting...',
      description: `Signing in with ${provider}`
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'OAuth Sign-in Failed',
      description:
        error instanceof Error
          ? error.message
          : 'Please try again or use another sign-in method.'
    };
  }
}

export async function signUpWithOAuth(provider: string, userType: string) {
  try {
    const supabase = createClient();
    // Ensure the redirectURL is absolute and includes the origin
    const redirectURL = new URL(
      'api/auth/callback',
      window.location.origin
    ).toString();

    // Store user type in localStorage before redirect
    localStorage.setItem('oauth_user_type', userType);

    const { error } = await supabase.auth.signInWithOAuth({
      provider: provider as Provider,
      options: {
        redirectTo: redirectURL,
        queryParams: {
          user_type: userType, // Pass userType as query param
          flow: 'signup' // This is a sign-up flow
        },
        scopes:
          provider === 'linkedin' ? 'r_emailaddress r_liteprofile' : undefined
      }
    });

    if (error) throw error;

    return {
      type: 'success',
      title: 'Redirecting...',
      description: `Creating account with ${provider}`
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'OAuth Sign-up Failed',
      description:
        error instanceof Error
          ? error.message
          : 'Please try again or use another sign-up method.'
    };
  }
}

export async function signOut() {
  try {
    // Remove local storage items
    localStorage.removeItem('bubble_auth_token');
    localStorage.removeItem('bubble_user_id');
    localStorage.removeItem('user_type');

    // Sign out from Supabase
    const supabase = createClient();
    await supabase.auth.signOut();

    return {
      type: 'success',
      title: 'Signed Out',
      description: 'You have been successfully signed out.',
      redirectPath: '/'
    };
  } catch (error) {
    return {
      type: 'error',
      title: 'Sign Out Failed',
      description: 'You could not be signed out.'
    };
  }
}

// New function to resend verification code for an existing account
export async function resendVerificationCode(email: string, userType: string) {
  try {
    // Check if the user exists in Supabase
    const supabase = createClient();
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('id, user_type, verified')
      .eq('email', email)
      .single();

    if (fetchError || !existingUser) {
      return {
        type: 'error',
        title: 'User Not Found',
        description: 'Could not find your account. Please try signing up again.'
      };
    }

    // If user already verified
    if (existingUser.verified) {
      return {
        type: 'error',
        title: 'Already Verified',
        description:
          'Your account is already verified. Please proceed to sign in.'
      };
    }

    // Generate a new 6-digit OTP code
    const verificationCode = Math.floor(
      100000 + Math.random() * 900000
    ).toString();

    // Set new expiration time (30 minutes from now)
    const verificationExpiration = new Date();
    verificationExpiration.setMinutes(verificationExpiration.getMinutes() + 30);

    // Update the user record with the new OTP and expiration
    const { error: updateError } = await supabase
      .from('users')
      .update({
        verification_code: verificationCode,
        verification_expiration: verificationExpiration.toISOString()
      })
      .eq('email', email);

    if (updateError) {
      console.error('Error updating verification code:', updateError);
      throw new Error('Failed to update verification code');
    }

    // Prepare email content based on user type
    let subject, body;

    // Use the existing user's user_type from the database
    const userTypeToUse = existingUser.user_type || userType;

    if (userTypeToUse.toLowerCase() === 'candidate') {
      subject = 'Your New InternUp Verification Code';
      body = `Your new verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
    } else if (userTypeToUse.toLowerCase() === 'company') {
      subject = 'Your New InternUp Company Verification Code';
      body = `Your new verification code for your company account is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
    } else {
      subject = 'Your New InternUp Verification Code';
      body = `Your new verification code is: ${verificationCode}. This code will expire in 30 minutes. Please enter this code to complete your registration.`;
    }

    // Send the verification email
    await bubbleClient.sendEmail(email, subject, body);

    return {
      type: 'success',
      title: 'Verification Code Sent',
      description: 'A new verification code has been sent to your email.',
      showOTP: true
    };
  } catch (error) {
    console.error('Error in resendVerificationCode:', error);
    return {
      type: 'error',
      title: 'Failed to Resend Code',
      description:
        error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

// Function to handle forgot password
export async function forgotPassword(email: string) {
  try {
    // Call the Bubble API to initiate password reset
    const response = await bubbleClient.forgotPassword(email);

    return {
      success: true,
      data: response,
      message:
        'Password reset email sent successfully. Please check your inbox.'
    };
  } catch (error) {
    console.error('Error in forgotPassword:', error);

    return {
      success: false,
      message:
        error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

// Function to handle reset password for logged in users
export async function resetPassword(
  email: string,
  oldPassword: string,
  newPassword: string
) {
  try {
    // Call the Bubble API to reset password
    const response = await bubbleClient.resetPassword(
      email,
      oldPassword,
      newPassword
    );

    return {
      success: true,
      data: response,
      message: 'Password has been reset successfully.'
    };
  } catch (error) {
    console.error('Error in resetPassword:', error);

    // Handle the specific error for wrong password
    if (
      error instanceof Error &&
      error.message.includes('old password is not right')
    ) {
      throw new Error(
        'The current password you entered is incorrect. Please try again.'
      );
    }

    // For other errors
    throw error;
  }
}
