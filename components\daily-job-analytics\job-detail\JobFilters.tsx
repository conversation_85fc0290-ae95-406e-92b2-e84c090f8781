import { type JobDetailRow } from '@/types/google-sheets';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, X } from 'lucide-react';

type FilterField = {
  key: string;
  label: string;
};

type JobFiltersProps = {
  jobDetails: JobDetailRow[];
  filteredJobDetails: JobDetailRow[];
  filterableFields: FilterField[];
  activeFilters: Record<string, string>;
  searchTerm: string;
  pageSize: number;
  page: number;
  totalPages: number;
  onSearchChange: (value: string) => void;
  onFilterChange: (field: string, value: string) => void;
  onPageSizeChange: (value: string) => void;
  clearAllFilters: () => void;
  getUniqueValues: (field: keyof JobDetailRow) => string[];
};

export const JobFilters = ({
  jobDetails,
  filteredJobDetails,
  filterableFields,
  activeFilters,
  searchTerm,
  pageSize,
  page,
  totalPages,
  onSearchChange,
  onFilterChange,
  onPageSizeChange,
  clearAllFilters,
  getUniqueValues
}: JobFiltersProps) => {
  return (
    <div className="mt-4 space-y-4">
      <div className="flex flex-col gap-4">
        {/* Search bar */}
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search jobs..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        {/* Filter controls */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
          {filterableFields.map((field) => (
            <div key={field.key} className="space-y-1">
              <label htmlFor={`filter-${field.key}`} className="sr-only">
                Filter by {field.label}
              </label>
              <Select
                value={activeFilters[field.key] || 'all'}
                onValueChange={(value) => onFilterChange(field.key, value)}
              >
                <SelectTrigger id={`filter-${field.key}`} className="w-full">
                  <SelectValue placeholder={`${field.label}...`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any {field.label}</SelectItem>
                  {getUniqueValues(field.key as keyof JobDetailRow).map(
                    (value) => (
                      <SelectItem
                        key={value}
                        value={
                          value ||
                          `unknown-${Math.random().toString(36).substring(2, 9)}`
                        }
                      >
                        {value || '(Empty)'}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>
          ))}
        </div>

        {/* Active filters display */}
        {(Object.keys(activeFilters).length > 0 || searchTerm) && (
          <div className="flex flex-wrap gap-2 mt-2">
            {searchTerm && (
              <Badge variant="secondary" className="flex items-center gap-1">
                Search: {searchTerm}
                <button
                  onClick={() => onSearchChange('')}
                  className="ml-1 rounded-full hover:bg-muted p-0.5"
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">Clear search</span>
                </button>
              </Badge>
            )}

            {Object.entries(activeFilters).map(([field, value]) => (
              <Badge
                key={field}
                variant="secondary"
                className="flex items-center gap-1"
              >
                {field}: {value}
                <button
                  onClick={() => onFilterChange(field, 'all')}
                  className="ml-1 rounded-full hover:bg-muted p-0.5"
                >
                  <X className="h-3 w-3" />
                  <span className="sr-only">Remove filter</span>
                </button>
              </Badge>
            ))}

            {(Object.keys(activeFilters).length > 0 || searchTerm) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="h-6 px-2 text-xs"
              >
                Clear all
              </Button>
            )}
          </div>
        )}

        {/* Page size selector */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Showing{' '}
            {Math.min(filteredJobDetails.length, 1 + (page - 1) * pageSize)}-
            {Math.min(page * pageSize, filteredJobDetails.length)} of{' '}
            {filteredJobDetails.length} jobs
            {(searchTerm || Object.keys(activeFilters).length > 0) &&
              ` (filtered from ${jobDetails.length} total)`}
          </div>

          <Select value={pageSize.toString()} onValueChange={onPageSizeChange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Rows per page" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="25">25 per page</SelectItem>
              <SelectItem value="50">50 per page</SelectItem>
              <SelectItem value="100">100 per page</SelectItem>
              <SelectItem value="200">200 per page</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};
