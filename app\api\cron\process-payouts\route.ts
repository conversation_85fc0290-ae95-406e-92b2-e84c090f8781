import { NextRequest, NextResponse } from 'next/server';
import { processAutomaticPayouts } from '@/actions/insider-payouts';

// Set dynamic to force-dynamic to ensure the function runs on each request
export const dynamic = 'force-dynamic';

/**
 * This API route is designed to be called by a cron job to process automatic payouts
 * It should be scheduled to run daily, weekly, or monthly depending on your needs
 * 
 * Example cron schedule:
 * - Daily: 0 0 * * * (midnight every day)
 * - Weekly: 0 0 * * 0 (midnight every Sunday)
 * - Monthly: 0 0 1 * * (midnight on the 1st of every month)
 */
export async function GET(req: NextRequest) {
  try {
    // Verify API key for security (if provided in the request)
    const apiKey = req.headers.get('x-api-key');
    const configuredApiKey = process.env.CRON_API_KEY;
    
    if (configuredApiKey && apiKey !== configuredApiKey) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Process automatic payouts
    const processedCount = await processAutomaticPayouts();
    
    return NextResponse.json({
      success: true,
      message: `Processed ${processedCount} automatic payouts`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error processing automatic payouts:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process automatic payouts',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
