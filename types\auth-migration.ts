import { User } from '@/types/user';

export enum MigrationStatus {
  REQUIRES_PASSWORD_RESET = 'requires_password_reset',
  PASSWORD_RESET_SENT = 'password_reset_sent',
  MIGRATION_IN_PROGRESS = 'migration_in_progress',
  MIGRATED = 'migrated',
  MIGRATION_FAILED = 'migration_failed',
  NOT_ELIGIBLE = 'not_eligible'
}

export type MigrationStatusData = {
  migration_status: MigrationStatus;
  updated_at: string;
};

export type MigrationStatusResponse = {
  success: boolean;
  data?: MigrationStatus;
  error?: string;
};

export type UsersByStatusResponse = {
  success: boolean;
  data?: User[];
  total?: number;
  error?: string;
};

export class MigrationError extends Error {
  constructor(
    message: string,
    public code: string,
    public status: number = 500
  ) {
    super(message);
    this.name = 'MigrationError';
  }
}

export const MIGRATION_ERROR_CODES = {
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  INVALID_STATUS: 'INVALID_STATUS',
  DATABASE_ERROR: 'DATABASE_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR'
} as const;

export type MigrationErrorCode =
  (typeof MIGRATION_ERROR_CODES)[keyof typeof MIGRATION_ERROR_CODES];
