'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

export interface PeriodSelectorProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

export const PeriodSelector = ({
  selectedPeriod,
  onPeriodChange
}: PeriodSelectorProps) => {
  return (
    <div className="flex justify-end">
      <Tabs
        defaultValue="7days"
        value={selectedPeriod}
        onValueChange={onPeriodChange}
        className="w-[400px]"
      >
        <TabsList className="grid grid-cols-4 w-full">
          <TabsTrigger value="7days">7 Days</TabsTrigger>
          <TabsTrigger value="30days">30 Days</TabsTrigger>
          <TabsTrigger value="90days">90 Days</TabsTrigger>
          <TabsTrigger value="all">All Time</TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
};
