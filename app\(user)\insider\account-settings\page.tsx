'use client';

import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { getInsider, updateInsider } from '@/actions/admin/insider';
import type { Insider } from '@/types/insider';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';
import {
  Pencil,
  Loader2,
  Lock,
  AlertCircle,
  Check,
  Eye,
  EyeOff
} from 'lucide-react';
import { z } from 'zod';
import type { InsiderFormData } from '@/types/insider';
import { useUser } from '@/hooks/useUser';
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { resetPassword } from '@/utils/bubble/auth';
import { createClient } from '@/utils/supabase/client';

const services = [
  'Resume Polishing',
  'Industry Insights',
  'Mock Interview'
] as const;

const workTypes = [
  'Full Time',
  'Part Time',
  'Volunteer',
  'OPT/CPT',
  'Internship'
] as const;

const insiderProfileSchema = z.object({
  first_name: z
    .string()
    .min(2, { message: 'First name must be at least 2 characters.' }),
  last_name: z
    .string()
    .min(2, { message: 'Last name must be at least 2 characters.' }),
  position: z.string().min(2, { message: 'Position is required.' }),
  start_up_company: z
    .string()
    .min(2, { message: 'Startup company is required.' }),
  public_firm: z.string().min(2, { message: 'Public firm is required.' }),
  linkedin: z
    .string()
    .url({ message: 'Please enter a valid LinkedIn URL.' })
    .optional(),
  user_email: z
    .string()
    .email({ message: 'Please enter a valid email address.' }),
  services: z
    .array(z.string())
    .min(1, { message: 'Please select at least one service.' }),
  worktype: z.array(z.string()).optional(),
  avalable_time: z.string().optional(),
  avalilable_day: z.string().optional(),
  requirements: z.string().optional()
});

const securitySchema = z
  .object({
    oldPassword: z
      .string()
      .min(8, { message: 'Old password must be at least 8 characters.' }),
    newPassword: z
      .string()
      .min(8, { message: 'New password must be at least 8 characters.' }),
    confirmPassword: z
      .string()
      .min(8, { message: 'Confirm password must be at least 8 characters.' })
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword']
  });

export default function AccountSettingsPage() {
  const [insider, setInsider] = useState<Insider | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [isOAuthUser, setIsOAuthUser] = useState(false);
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetPasswordError, setResetPasswordError] = useState<string | null>(
    null
  );
  const [resetPasswordSuccess, setResetPasswordSuccess] = useState(false);
  const router = useRouter();
  const { user, loading: userLoading } = useUser();
  const { toast } = useToast();

  const form = useForm<InsiderFormData>({
    resolver: zodResolver(insiderProfileSchema),
    defaultValues: {
      first_name: '',
      last_name: '',
      position: '',
      public_firm: '',
      linkedin: '',
      user_email: '',
      services: [],
      worktype: []
    }
  });

  const securityForm = useForm({
    resolver: zodResolver(securitySchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  useEffect(() => {
    if (!user) return;
    async function fetchInsider() {
      try {
        const insiderData = await getInsider(user.id);

        if (!insiderData) {
          toast({
            title: 'Error',
            description: 'Insider profile not found',
            variant: 'destructive'
          });
          return;
        }

        // Check if user is logged in via OAuth (Supabase)
        const supabase = createClient();
        const {
          data: { session }
        } = await supabase.auth.getSession();

        // If there's a valid Supabase session, the user is authenticated via OAuth
        setIsOAuthUser(!!session);

        setInsider(insiderData);

        form.reset({
          first_name: insiderData.first_name || '',
          last_name: insiderData.last_name || '',
          position: insiderData.position || '',
          public_firm: insiderData.public_firm || '',
          linkedin: insiderData.linkedin || '',
          user_email: insiderData.user_email || '',
          services: insiderData.services || [],
          worktype: insiderData.worktype || []
        });
      } catch (error) {
        console.error('Error fetching insider:', error);
        toast({
          title: 'Error',
          description:
            error instanceof Error ? error.message : 'Failed to load profile',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    }

    fetchInsider();
  }, [form, router, user]);

  const onSubmit = async (data: InsiderFormData) => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'Please login to update your profile',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Preserve existing fields that aren't in the form anymore
      const updateData = {
        ...data,
        creation_date: insider?.creation_date || '',
        modified_date: new Date().toISOString(),
        account_status: insider?.account_status || '',
        start_up_company: insider?.start_up_company || '',
        avalable_time: insider?.avalable_time || '',
        avalilable_day: insider?.avalilable_day || '',
        requirements: insider?.requirements || ''
      };

      // Pass the insider's ID, not the user ID for updating
      const insiderId = insider?.id;
      if (!insiderId) {
        throw new Error('Insider ID is missing');
      }

      const updatedInsider = await updateInsider(insiderId, updateData);

      if (!updatedInsider) {
        throw new Error('Failed to update profile');
      }

      setInsider(updatedInsider);

      toast({
        title: 'Success',
        description: 'Profile updated successfully'
      });

      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update profile',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (insider) {
      form.reset({
        first_name: insider.first_name || '',
        last_name: insider.last_name || '',
        position: insider.position || '',
        public_firm: insider.public_firm || '',
        linkedin: insider.linkedin || '',
        user_email: insider.user_email || '',
        services: insider.services || [],
        worktype: insider.worktype || []
        // Removed fields that are no longer in the form
      });
    }
    setIsEditing(false);
  };

  const handleSubmit = async () => {
    const values = form.getValues();
    await onSubmit(values);
  };

  const onSecuritySubmit = async (data: z.infer<typeof securitySchema>) => {
    if (!user?.email) return;

    setIsSubmitting(true);
    setResetPasswordError(null);
    setResetPasswordSuccess(false);

    try {
      await resetPassword(user.email, data.oldPassword, data.newPassword);

      setResetPasswordSuccess(true);
      securityForm.reset({
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      toast({
        title: 'Password updated',
        description: 'Your password has been updated successfully.',
        variant: 'default'
      });
    } catch (error) {
      console.error('Error resetting password:', error);

      // Extract the error message for display
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to reset password. Please try again.';

      setResetPasswordError(errorMessage);

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-[#118073]" />
      </div>
    );
  }

  if (userLoading) {
    return <Loader2 className="h-8 w-8 animate-spin text-[#118073]" />;
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
        <p className="text-muted-foreground">
          Manage your profile and preferences
        </p>
      </div>

      <Tabs
        defaultValue="profile"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="credentials">Credentials</TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0">
              <div>
                <CardTitle>Personal Details</CardTitle>
                <CardDescription>
                  Your profile information and preferences
                </CardDescription>
              </div>
              {!isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  className="hover:bg-[#118073]/10"
                >
                  <Pencil className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              )}
            </CardHeader>
            <CardContent>
              {isEditing ? (
                <Form {...form}>
                  <form className="space-y-6">
                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="first_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First name</FormLabel>
                            <FormControl>
                              <Input placeholder="First name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="last_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last name</FormLabel>
                            <FormControl>
                              <Input placeholder="Last name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="position"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Position</FormLabel>
                          <FormControl>
                            <Input placeholder="Your position" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid gap-4 md:grid-cols-2">
                      <FormField
                        control={form.control}
                        name="public_firm"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Public Firm</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Your public firm"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="user_email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Your email"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="services"
                      render={() => (
                        <FormItem>
                          <FormLabel>Please Select Services</FormLabel>
                          <div className="grid gap-4">
                            {services.map((service) => (
                              <FormField
                                key={service}
                                control={form.control}
                                name="services"
                                render={({ field }) => {
                                  return (
                                    <FormItem
                                      key={service}
                                      className="flex flex-row items-start space-x-3 space-y-0"
                                    >
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value?.includes(
                                            service
                                          )}
                                          onCheckedChange={(checked) => {
                                            return checked
                                              ? field.onChange([
                                                  ...field.value,
                                                  service
                                                ])
                                              : field.onChange(
                                                  field.value?.filter(
                                                    (value) => value !== service
                                                  )
                                                );
                                          }}
                                        />
                                      </FormControl>
                                      <FormLabel className="font-normal">
                                        {service}
                                      </FormLabel>
                                    </FormItem>
                                  );
                                }}
                              />
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="worktype"
                      render={() => (
                        <FormItem>
                          <FormLabel>Open to accept referrals in</FormLabel>
                          <div className="grid gap-4">
                            {workTypes.map((type) => (
                              <FormField
                                key={type}
                                control={form.control}
                                name="worktype"
                                render={({ field }) => {
                                  return (
                                    <FormItem
                                      key={type}
                                      className="flex flex-row items-start space-x-3 space-y-0"
                                    >
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value?.includes(type)}
                                          onCheckedChange={(checked) => {
                                            return checked
                                              ? field.onChange([
                                                  ...(field.value || []),
                                                  type
                                                ])
                                              : field.onChange(
                                                  field.value?.filter(
                                                    (value) => value !== type
                                                  )
                                                );
                                          }}
                                        />
                                      </FormControl>
                                      <FormLabel className="font-normal">
                                        {type}
                                      </FormLabel>
                                    </FormItem>
                                  );
                                }}
                              />
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="avalable_time"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Available Time</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Your available time"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="avalilable_day"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Available Day</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Your available day"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="requirements"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Requirements</FormLabel>
                          <FormControl>
                            <Input placeholder="Your requirements" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              ) : (
                <div className="space-y-6">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <h3 className="font-medium text-sm">First Name</h3>
                      <p className="text-muted-foreground">
                        {insider?.first_name || '-'}
                      </p>
                    </div>
                    <div>
                      <h3 className="font-medium text-sm">Last Name</h3>
                      <p className="text-muted-foreground">
                        {insider?.last_name || '-'}
                      </p>
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Position</h3>
                    <p className="text-muted-foreground">
                      {insider?.position || '-'}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Public Firm</h3>
                    <p className="text-muted-foreground">
                      {insider?.public_firm || '-'}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Email</h3>
                    <p className="text-muted-foreground">
                      {insider?.user_email || '-'}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Services</h3>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {insider?.services?.map((service: string) => (
                        <span
                          key={service}
                          className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm"
                        >
                          {service}
                        </span>
                      )) || '-'}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Work Types</h3>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {insider?.worktype?.map((type: string) => (
                        <span
                          key={type}
                          className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm"
                        >
                          {type}
                        </span>
                      )) || '-'}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Available Time</h3>
                    <p className="text-muted-foreground">
                      {insider?.avalable_time || '-'}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Available Day</h3>
                    <p className="text-muted-foreground">
                      {insider?.avalilable_day || '-'}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-sm">Requirements</h3>
                    <p className="text-muted-foreground">
                      {insider?.requirements || '-'}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
            {isEditing && (
              <CardFooter className="flex justify-end space-x-4 pt-6">
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSubmitting}
                  className="hover:bg-[#118073]/10"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="bg-[#118073] hover:bg-[#118073]/90"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </CardFooter>
            )}
          </Card>
        </TabsContent>

        {/* Credentials Tab */}
        <TabsContent value="credentials" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Password</CardTitle>
              <CardDescription>Change your password</CardDescription>
            </CardHeader>
            <CardContent>
              {isOAuthUser ? (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>OAuth Account</AlertTitle>
                  <AlertDescription>
                    You signed up using a social provider. Password management
                    is handled by your provider.
                  </AlertDescription>
                </Alert>
              ) : (
                <>
                  {resetPasswordSuccess && (
                    <Alert className="mb-6 bg-green-50 border-green-200">
                      <Check className="h-4 w-4 text-green-600" />
                      <AlertTitle className="text-green-600">
                        Success
                      </AlertTitle>
                      <AlertDescription className="text-green-700">
                        Your password has been updated successfully.
                      </AlertDescription>
                    </Alert>
                  )}

                  {resetPasswordError && (
                    <Alert className="mb-6 bg-red-50 border-red-200">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <AlertTitle className="text-red-600">Error</AlertTitle>
                      <AlertDescription className="text-red-700">
                        {resetPasswordError}
                      </AlertDescription>
                    </Alert>
                  )}

                  <Form {...securityForm}>
                    <form
                      onSubmit={securityForm.handleSubmit(onSecuritySubmit)}
                      className="space-y-6"
                    >
                      <FormField
                        control={securityForm.control}
                        name="oldPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Current Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type={showOldPassword ? 'text' : 'password'}
                                  className="pl-10 pr-10"
                                  placeholder="Enter your current password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  className="absolute right-3 top-3 text-muted-foreground"
                                  onClick={() =>
                                    setShowOldPassword(!showOldPassword)
                                  }
                                >
                                  {showOldPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={securityForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>New Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type={showNewPassword ? 'text' : 'password'}
                                  className="pl-10 pr-10"
                                  placeholder="Enter your new password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  className="absolute right-3 top-3 text-muted-foreground"
                                  onClick={() =>
                                    setShowNewPassword(!showNewPassword)
                                  }
                                >
                                  {showNewPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={securityForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm Password</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                  type={
                                    showConfirmPassword ? 'text' : 'password'
                                  }
                                  className="pl-10 pr-10"
                                  placeholder="Confirm your new password"
                                  {...field}
                                />
                                <button
                                  type="button"
                                  className="absolute right-3 top-3 text-muted-foreground"
                                  onClick={() =>
                                    setShowConfirmPassword(!showConfirmPassword)
                                  }
                                >
                                  {showConfirmPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                  ) : (
                                    <Eye className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-[#118073] hover:bg-[#118073]/90"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Updating...
                          </>
                        ) : (
                          'Update Password'
                        )}
                      </Button>
                    </form>
                  </Form>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
