// utils/supabase/admin.ts
import { createClient } from '@supabase/supabase-js'

/**
 * Creates a Supabase admin client with service role key
 * WARNING: This should only be used server-side
 */
export function createAdminClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY!, // Updated to match env.mjs
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

/**
 * Checks if a user exists in Auth system by email
 * @param email Email to check
 * @returns Boolean indicating if user exists
 */
export async function checkUserExistsByEmail(email: string): Promise<boolean> {
  try {
    const supabaseAdmin = createAdminClient();
    // First try getting users - this will return all users, we'll filter by email
    const { data, error } = await supabaseAdmin.auth.admin.listUsers();

    if (error) {
      console.error('Error checking user by email:', error);
      return false;
    }
    
    // Check if any user has the matching email
    return data.users.some(user => user.email?.toLowerCase() === email.toLowerCase());
  } catch (error) {
    console.error('Exception in checkUserExistsByEmail:', error);
    return false;
  }
}

/**
 * Checks if a user exists in Auth system by ID
 * @param userId User ID to check
 * @returns Boolean indicating if user exists
 */
export async function checkUserExistsById(userId: string): Promise<boolean> {
  try {
    const supabaseAdmin = createAdminClient();
    const { data, error } = await supabaseAdmin.auth.admin.getUserById(userId);
    
    if (error?.status === 404) return false; // User not found
    if (error) {
      console.error('Error checking user by ID:', error);
      return false;
    }
    
    return !!data.user;
  } catch (error) {
    console.error('Exception in checkUserExistsById:', error);
    return false;
  }
}
