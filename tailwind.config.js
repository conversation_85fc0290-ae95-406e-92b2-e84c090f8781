const { fontFamily } = require('tailwindcss/defaultTheme');
import { createPreset } from 'fumadocs-ui/tailwind-plugin';

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    './dist/*.{html,js}',
    './node_modules/fumadocs-ui/dist/**/*.js' // Fumadocs
  ],
  prefix: '',
  presets: [
    createPreset({
      preset: 'dusk'
    })
  ],
  darkMode: ['class'],
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	containerHeader: {
  		center: true,
  		padding: '3rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	letterSpacing: {
  		tightest: '-.075em',
  		tighter: '-.05em',
  		tight: '-.025em',
  		normal: '0',
  		wide: '.025em',
  		wider: '.05em',
  		widest: '.25em'
  	},
  	extend: {
  		spacing: {
  			'128': '32rem'
  		},
  		backgroundImage: {
  			'pattern-12': 'url("/patterns/12.svg")'
  		},
  		colors: {
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			violetColor: 'rgb(33, 134, 234)',
  			foreground: 'hsl(var(--foreground))',
  			pricingBackground: 'rgb(39, 45, 52)',
  			customColor: 'rgb(76, 45, 235)',
  			statsColor: 'rgb(39, 45, 52)',
  			grayColor: 'rgb(149, 158, 169)',
  			blackColor: 'rgb(21, 24, 27)',
  			white: 'rgb(255, 255, 255)',
  			greenColor: 'rgb(95, 207, 192)',
  			darkestGreen: '#00382A',
  			neutralBlack: '#2F2F2F',
  			primary: {
  				DEFAULT: '#118073',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: '#36BA98',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			footer: 'hsl(var(--footer))',
  			ollabot: 'hsl(var(--ollabot))'
  		},
  		borderRadius: {
  			lg: '`var(--radius)`',
  			md: '`calc(var(--radius) - 2px)`',
  			sm: 'calc(var(--radius) - 4px)',
  			'4xl': '2rem',
  			'5xl': '2.5rem',
  			'6xl': '3rem'
  		},
  		fontFamily: {
  			sans: [
  				'var(--font-sans)',
                    ...fontFamily.sans
                ],
  			heading: [
  				'var(--font-heading)',
                    ...fontFamily.sans
                ]
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: 0
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: 0
  				}
  			},
  			ripple: {
  				'0%, 100%': {
  					transform: 'translate(-50%, -50%) scale(1)'
  				},
  				'50%': {
  					transform: 'translate(-50%, -50%) scale(0.9)'
  				}
  			},
  			gradient: {
  				to: {
  					backgroundPosition: 'var(--bg-size) 0'
  				}
  			},
  			shimmer: {
  				'0%, 90%, 100%': {
  					'background-position': 'calc(-100% - var(--shimmer-width)) 0'
  				},
  				'30%, 60%': {
  					'background-position': 'calc(100% + var(--shimmer-width)) 0'
  				}
  			},
  			marquee: {
  				from: {
  					transform: 'translateX(0)'
  				},
  				to: {
  					transform: 'translateX(calc(-100% - var(--gap)))'
  				}
  			},
  			'marquee-vertical': {
  				from: {
  					transform: 'translateY(0)'
  				},
  				to: {
  					transform: 'translateY(calc(-100% - var(--gap)))'
  				}
  			},
  			float: {
  				'0%, 100%': {
  					transform: 'translateY(0)'
  				},
  				'50%': {
  					transform: 'translateY(-10px)'
  				}
  			},
  			meteor: {
  				'0%': {
  					transform: 'rotate(var(--angle)) translateX(0)',
  					opacity: '1'
  				},
  				'70%': {
  					opacity: '1'
  				},
  				'100%': {
  					transform: 'rotate(var(--angle)) translateX(-500px)',
  					opacity: '0'
  				}
  			},
  			pulse: {
  				'0%, 100%': {
  					boxShadow: '0 0 0 0 var(--pulse-color)'
  				},
  				'50%': {
  					boxShadow: '0 0 0 8px var(--pulse-color)'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			ripple: 'ripple 3400ms ease infinite',
  			shimmer: 'shimmer 8s infinite',
  			marquee: 'marquee var(--duration) infinite linear',
  			'marquee-vertical': 'marquee-vertical var(--duration) linear infinite',
  			gradient: 'gradient 8s linear infinite',
  			'float-slow': 'float 6s ease-in-out infinite',
  			'float-delay-1': 'float 6s ease-in-out 1s infinite',
  			'float-delay-2': 'float 6s ease-in-out 2s infinite',
  			'float-delay-3': 'float 6s ease-in-out 3s infinite',
  			meteor: 'meteor 5s linear infinite',
  			pulse: 'pulse var(--duration) ease-out infinite'
  		}
  	}
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')]
};
