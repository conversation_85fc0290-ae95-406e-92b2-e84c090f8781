'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { OnboardingProvider } from './components/onboarding-provider';

interface OnboardingLayoutProps {
  children: React.ReactNode;
}

export default function OnboardingLayout({ children }: OnboardingLayoutProps) {
  const pathname = usePathname();
  const router = useRouter();

  // Define our onboarding steps in order
  const steps = [
    '/onboarding/candidate/personal-info',
    '/onboarding/candidate/job-preferences',
    '/onboarding/candidate/about-you',
    '/onboarding/candidate/complete'
  ];

  // Determine current step index
  const currentStepIndex = steps.findIndex((step) => pathname === step);

  // Redirect to the first step if trying to access a later step directly
  // without completing previous steps (simplified approach)
  useEffect(() => {
    // This is a simple example to ensure flow - you would use a more robust
    // approach with server-side or localStorage to track completion status
    if (pathname !== '/onboarding' && currentStepIndex === -1) {
      // If path is not valid, redirect to first step
      router.push(steps[0]);
    }
  }, [pathname, currentStepIndex, router]);

  return (
    <OnboardingProvider>
      <main className="min-h-screen bg-white">{children}</main>
    </OnboardingProvider>
  );
}
