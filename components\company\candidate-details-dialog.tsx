'use client';

import { useState } from 'react';
import type { StartupApplication } from '@/types/startups';
import type { User } from '@/types/user';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Briefcase,
  MapPin,
  Mail,
  Phone,
  FileText,
  Linkedin,
  Globe,
  Download
} from 'lucide-react';

interface CandidateDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  application: StartupApplication;
  candidate: User;
}

export function CandidateDetailsDialog({
  open,
  onOpenChange,
  application,
  candidate
}: CandidateDetailsDialogProps) {
  const [activeTab, setActiveTab] = useState('profile');

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((part) => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const candidateName =
    `${candidate.first_name || ''} ${candidate.last_name || ''}`.trim() ||
    'Unknown Candidate';
  const initials = getInitials(candidateName);

  const handleDownloadResume = () => {
    if (candidate.resume_url) {
      window.open(candidate.resume_url, '_blank');
    }
  };

  const handleDownloadCoverLetter = () => {
    if (application.cover_note_file) {
      window.open(application.cover_note_file, '_blank');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[700px]">
        <DialogHeader>
          <DialogTitle>Candidate Details</DialogTitle>
          <DialogDescription>
            View detailed information about this candidate
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <div className="mb-6 flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage
                src={candidate.avatar_url || undefined}
                alt={candidateName}
              />
              <AvatarFallback>{initials}</AvatarFallback>
            </Avatar>
            <div>
              <h2 className="text-2xl font-bold">{candidateName}</h2>
              <p className="text-muted-foreground">
                Applied for:{' '}
                <span className="font-medium text-foreground">
                  {application.job_title}
                </span>
              </p>
              <div className="mt-1 flex items-center gap-2">
                {candidate.location && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {candidate.location}
                  </Badge>
                )}
                {candidate.work_types && candidate.work_types.length > 0 && (
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Briefcase className="h-3 w-3" />
                    {candidate.work_types.join(', ')}
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <Tabs
            defaultValue="profile"
            value={activeTab}
            onValueChange={setActiveTab}
          >
            <TabsList className="mb-4 grid w-full grid-cols-3">
              <TabsTrigger value="profile">Profile</TabsTrigger>
              <TabsTrigger value="application">Application</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="profile" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <span>{candidate.email}</span>
                  </div>
                  {candidate.phone_number && (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span>{candidate.phone_number}</span>
                    </div>
                  )}
                  {candidate.linkedin_url && (
                    <div className="flex items-center gap-2">
                      <Linkedin className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={candidate.linkedin_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        LinkedIn Profile
                      </a>
                    </div>
                  )}
                  {candidate.portfolio && (
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={candidate.portfolio}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        Portfolio
                      </a>
                    </div>
                  )}
                </CardContent>
              </Card>

              {candidate.bio && (
                <Card>
                  <CardHeader>
                    <CardTitle>About</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>{candidate.bio}</p>
                  </CardContent>
                </Card>
              )}

              <Card>
                <CardHeader>
                  <CardTitle>Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {candidate.work_preferences &&
                    candidate.work_preferences.length > 0 && (
                      <div>
                        <h4 className="mb-1 font-medium">Work Preferences</h4>
                        <div className="flex flex-wrap gap-1">
                          {candidate.work_preferences.map((pref, i) => (
                            <Badge key={i} variant="secondary">
                              {pref}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  {candidate.work_types && candidate.work_types.length > 0 && (
                    <div>
                      <h4 className="mb-1 font-medium">Work Types</h4>
                      <div className="flex flex-wrap gap-1">
                        {candidate.work_types.map((type, i) => (
                          <Badge key={i} variant="secondary">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="mt-2 grid grid-cols-2 gap-2">
                    <div>
                      <h4 className="text-sm font-medium">Seeking Job</h4>
                      <p>{candidate.seeking_job ? 'Yes' : 'No'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Seeking Training</h4>
                      <p>{candidate.seeking_training ? 'Yes' : 'No'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">Need Sponsorship</h4>
                      <p>{candidate.need_sponsorship || 'No'}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium">
                        Open for Reverse Hiring
                      </h4>
                      <p>
                        {candidate.is_open_for_reverse_hiring ? 'Yes' : 'No'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="application" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Application Details</CardTitle>
                  <CardDescription>
                    Submitted on{' '}
                    {new Date(
                      application.creation_date || ''
                    ).toLocaleDateString()}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="mb-1 font-medium">Status</h4>
                    <Badge
                      className={
                        application.status === 'Accepted'
                          ? 'bg-green-500'
                          : application.status === 'Rejected'
                            ? 'bg-destructive'
                            : application.status === 'Applying'
                              ? 'bg-blue-500'
                              : 'bg-orange-500'
                      }
                    >
                      {application.status || 'Pending'}
                    </Badge>
                  </div>

                  {application.yoe_job && (
                    <div>
                      <h4 className="mb-1 font-medium">
                        Years of Experience (Job)
                      </h4>
                      <p>{application.yoe_job}</p>
                    </div>
                  )}

                  {application.yoe_internship && (
                    <div>
                      <h4 className="mb-1 font-medium">
                        Years of Experience (Internship)
                      </h4>
                      <p>{application.yoe_internship}</p>
                    </div>
                  )}

                  {application.additional_materials && (
                    <div>
                      <h4 className="mb-1 font-medium">
                        Additional Information
                      </h4>
                      <p>{application.additional_materials}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Documents</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {candidate.resume_url ? (
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-primary" />
                        <div>
                          <h4 className="font-medium">Resume</h4>
                          <p className="text-sm text-muted-foreground">
                            View or download the candidate's resume
                          </p>
                        </div>
                      </div>
                      <Button variant="outline" onClick={handleDownloadResume}>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  ) : (
                    <div className="rounded-lg border border-dashed p-4 text-center text-muted-foreground">
                      No resume available
                    </div>
                  )}

                  {application.cover_note_file ? (
                    <div className="flex items-center justify-between rounded-lg border p-4">
                      <div className="flex items-center gap-3">
                        <FileText className="h-8 w-8 text-primary" />
                        <div>
                          <h4 className="font-medium">Cover Letter</h4>
                          <p className="text-sm text-muted-foreground">
                            View or download the candidate's cover letter
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        onClick={handleDownloadCoverLetter}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </Button>
                    </div>
                  ) : (
                    <div className="rounded-lg border border-dashed p-4 text-center text-muted-foreground">
                      No cover letter available
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
