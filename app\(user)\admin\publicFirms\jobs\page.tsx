"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { Search, Plus, X, Edit } from "lucide-react"
import { getPublicFirmJobs, toggleJobLiveStatus, deletePublicFirmJob } from "@/actions/admin/public-firms/jobs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog"
import { JobForm } from "./job-form"
import { PublicFirmJob } from "@/types/public-firms"
import { ScrollArea } from "@/components/ui/scroll-area"

export default function PublicFirmJobsPage() {
  const [jobs, setJobs] = useState<PublicFirmJob[]>([])
  const [filteredJobs, setFilteredJobs] = useState<PublicFirmJob[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editJob, setEditJob] = useState<PublicFirmJob | null>(null)
  const itemsPerPage = 20

  useEffect(() => {
    fetchJobs()
  }, [currentPage]) // Load jobs on initial render and when currentPage changes

  const fetchJobs = async () => {
    setIsLoading(true)
    try {
      const { data, count } = await getPublicFirmJobs(currentPage, itemsPerPage)
      setJobs(data || [])
      setFilteredJobs(data || [])
      setTotalPages(Math.ceil((count || 0) / itemsPerPage))
    } catch (error) {
      console.error("Error fetching jobs:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = () => {
    if (!searchQuery.trim()) {
      setFilteredJobs(jobs)
      return
    }

    const filtered = jobs.filter(
      (job) =>
        job.job_title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.company?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        job.location?.toLowerCase().includes(searchQuery.toLowerCase()),
    )

    setFilteredJobs(filtered)
  }

  const handleReset = () => {
    setSearchQuery("")
    setFilteredJobs(jobs)
  }

  const handleToggleLive = async (id: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === "yes" ? "no" : "yes"
      await toggleJobLiveStatus(id, newStatus)

      // Update local state
      setJobs((prevJobs) => prevJobs.map((job) => (job.id === id ? { ...job, live: newStatus } : job)))
      setFilteredJobs((prevJobs) => prevJobs.map((job) => (job.id === id ? { ...job, live: newStatus } : job)))
    } catch (error) {
      console.error("Error toggling job status:", error)
    }
  }

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this job?")) {
      try {
        await deletePublicFirmJob(id)

        // Update local state
        setJobs((prevJobs) => prevJobs.filter((job) => job.id !== id))
        setFilteredJobs((prevJobs) => prevJobs.filter((job) => job.id !== id))
      } catch (error) {
        console.error("Error deleting job:", error)
      }
    }
  }

  const handleEditJob = (job: PublicFirmJob) => {
    setEditJob(job)
    setIsFormOpen(true)
  }

  const handleFormSuccess = () => {
    setIsFormOpen(false)
    setEditJob(null)
    fetchJobs()
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Public Firm Jobs</h1>
          <p className="text-gray-600">Here are all public firm jobs</p>
          {!isLoading && <p className="text-sm text-gray-500 mt-2">{filteredJobs.length} results</p>}
        </div>
        <Button onClick={() => setIsFormOpen(true)} className="bg-gray-900 hover:bg-gray-800">
          <Plus className="mr-2 h-4 w-4" /> New Public Firm Job
        </Button>
      </div>

      <div className="flex justify-end mb-4 gap-2">
        <Input
          type="text"
          placeholder="Search for any job..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
        <Button onClick={handleSearch} variant="secondary">
          <Search className="h-4 w-4 mr-2" /> Search
        </Button>
        <Button onClick={handleReset} variant="outline">
          Reset
        </Button>
      </div>

      {isLoading ? (
        <div className="text-center py-10">Loading jobs...</div>
      ) : (
        <>
          <ScrollArea className="h-[600px]"> {/* Adjust height as needed */}
            <div className="overflow-x-auto rounded-lg border">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Logo</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Company</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Job title</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Term</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Applications</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Expire Date</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Live</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Delete</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Edit</th>
                  </tr>
                </thead>
                <tbody className="divide-y">
                  {filteredJobs.map((job) => (
                    <tr key={job.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        {job.logo ? (
                          <Image
                            src={job.logo}
                            alt={`${job.company} logo`}
                            width={40}
                            height={40}
                            className="rounded-md"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-200 rounded-md flex items-center justify-center">
                            <span className="text-xs text-gray-500">{job.company?.charAt(0) || "?"}</span>
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3 font-medium">{job.company}</td>
                      <td className="px-4 py-3">{job.job_title}</td>
                      <td className="px-4 py-3">{job.term}</td>
                      <td className="px-4 py-3">{job.applied_user?.length || 0}</td>
                      <td className="px-4 py-3">
                        {job.expire_date ? new Date(job.expire_date).toLocaleDateString() : "N/A"}
                      </td>
                      <td className="px-4 py-3">
                        <button
                          onClick={() => handleToggleLive(job.id, job.live || "no")}
                          className={`w-12 h-6 rounded-full flex items-center transition-colors duration-300 ${
                            job.live === "yes" ? " bg-[#118073] justify-end" : "bg-gray-300 justify-start"
                          }`}
                        >
                          <span
                            className={`w-5 h-5 rounded-full mx-0.5 ${job.live === "yes" ? "bg-white" : "bg-gray-500"}`}
                          ></span>
                        </button>
                      </td>
                      <td className="px-4 py-3">
                        <button onClick={() => handleDelete(job.id)} className="text-red-500 hover:text-red-700">
                          <X className="h-5 w-5" />
                        </button>
                      </td>
                      <td className="px-4 py-3">
                        <button onClick={() => handleEditJob(job)} className="text-[#118073] hover:text-green-700">
                          <Edit className="h-5 w-5 " />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </ScrollArea>

          {totalPages > 1 && (
            <div className="flex justify-center mt-6 gap-2">
              <Button
                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                variant="outline"
              >
                Previous
              </Button>
              <span className="py-2 px-4 border rounded-md">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                variant="outline"
              >
                Next
              </Button>
            </div>
          )}
        </>
      )}

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{editJob ? "Edit Public Firm Job" : "Create A Public Firm Job"}</DialogTitle>
            <DialogClose />
          </DialogHeader>
          <JobForm
            onSuccess={handleFormSuccess}
            initialData={editJob}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}