'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Calendar } from 'lucide-react';

export default function PeerInterviewPage() {
  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Peer Mock Interviews
        </h1>
        <p className="text-muted-foreground">
          Practice your interview skills with peers and get valuable feedback
        </p>
      </div>

      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="rounded-full bg-[#118073]/10 p-6 mb-6">
          <Calendar className="h-12 w-12 text-[#118073]" />
        </div>
        <h2 className="text-2xl font-semibold mb-4">Ready to Practice?</h2>
        <p className="text-muted-foreground max-w-md mb-8">
          Schedule a mock interview with a peer to enhance your interview skills
          and get valuable feedback.
        </p>
        <Link
          href="https://docs.google.com/forms/d/e/1FAIpQLSch-ZqUCPZKRJy-zz2ECcQgmJjG7PAAFj4R3OdGw4cYMxVQpA/viewform"
          target="_blank"
        >
          <Button className="bg-[#118073] hover:bg-[#118073]/90 text-white px-8 py-6 text-lg">
            <Calendar className="h-5 w-5 mr-2" />
            Schedule New Interview
          </Button>
        </Link>
      </div>
    </div>
  );
}
