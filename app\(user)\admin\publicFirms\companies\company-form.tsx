'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { XCircleIcon } from 'lucide-react';
import {
  createPublicFirmCompany,
  updatePublicFirmCompany
} from '@/actions/admin/public-firms/companies';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { uploadImage } from '@/utils/supabase/storage/client';
import type { PublicFirmCompany } from '@/types/public-firms';

interface CompanyFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  company: PublicFirmCompany | null;
  onSave: () => void;
}

export default function CompanyFormDialog({
  open,
  onOpenChange,
  company,
  onSave
}: CompanyFormDialogProps) {
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    industry: '',
    location: '',
    description: '',
    logo: '',
    live: 'yes',
    insider: 'No'
  });

  // Populate form when editing
  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || '',
        industry: company.industry || '',
        location: company.location || '',
        description: company.description || '',
        logo: company.logo || '',
        live: company.live || 'yes',
        insider: company.insider || 'No'
      });

      if (company.logo) {
        setLogoPreview(company.logo);
      } else {
        setLogoPreview(null);
      }
    } else {
      resetForm();
    }
  }, [company]);

  const handleSaveCompany = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      let logoUrl = formData.logo;

      if (logoFile) {
        const folderName = formData.name.toLowerCase().replace(/\s+/g, '-');
        const { imageUrl, error } = await uploadImage({
          file: logoFile,
          bucket: 'public-firm',
          folder: `companies/${folderName}`
        });

        if (error) {
          throw new Error(`Failed to upload logo: ${error}`);
        }

        logoUrl = imageUrl;
      }

      const companyData = {
        ...formData,
        logo: logoUrl
      };

      if (company) {
        await updatePublicFirmCompany(company.id, companyData);
      } else {
        await createPublicFirmCompany(companyData);
      }

      onSave();
    } catch (error) {
      console.error(
        `Error ${company ? 'updating' : 'creating'} company:`,
        error
      );
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      industry: '',
      location: '',
      description: '',
      logo: '',
      live: 'yes',
      insider: 'No'
    });
    setLogoFile(null);
    setLogoPreview(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] px-10 max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {company ? 'Edit' : 'Create'} Public Firm Company
          </DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSaveCompany} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2 md:col-span-1">
              <label className="text-sm font-medium">Upload Logo</label>
              <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-4 h-32">
                {logoPreview ? (
                  <div className="relative w-full h-full">
                    <Image
                      src={logoPreview}
                      alt="Logo preview"
                      fill
                      className="object-contain"
                      unoptimized
                    />
                    <button
                      type="button"
                      onClick={() => {
                        setLogoFile(null);
                        setLogoPreview(null);
                        setFormData({ ...formData, logo: '' });
                      }}
                      className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1"
                    >
                      <XCircleIcon className="h-4 w-4" />
                    </button>
                  </div>
                ) : (
                  <>
                    <label
                      htmlFor="logo-upload"
                      className="cursor-pointer text-center"
                    >
                      <span className="text-sm text-gray-500">
                        Click to upload
                      </span>
                    </label>
                    <input
                      id="logo-upload"
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleLogoChange}
                    />
                  </>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Company Name</label>
              <Input
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Industry</label>
              <Input
                value={formData.industry}
                onChange={(e) =>
                  setFormData({ ...formData, industry: e.target.value })
                }
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <Select
                value={formData.location}
                onValueChange={(value) =>
                  setFormData({ ...formData, location: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Remote">Remote</SelectItem>
                  <SelectItem value="Hybrid">Hybrid</SelectItem>
                  <SelectItem value="On-site">On-site</SelectItem>
                  <SelectItem value="New York">New York</SelectItem>
                  <SelectItem value="San Francisco">San Francisco</SelectItem>
                  <SelectItem value="Seattle">Seattle</SelectItem>
                  <SelectItem value="Austin">Austin</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Insider Status</label>
              <Select
                value={formData.insider}
                onValueChange={(value) =>
                  setFormData({ ...formData, insider: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Insider status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Yes">Yes</SelectItem>
                  <SelectItem value="No">No</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Live Status</label>
              <Select
                value={formData.live}
                onValueChange={(value) =>
                  setFormData({ ...formData, live: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Live status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="yes">Yes</SelectItem>
                  <SelectItem value="no">No</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              rows={4}
            />
          </div>

          <div className="flex justify-end gap-2">
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit">{company ? 'Update' : 'Save'}</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
