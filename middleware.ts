import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/client';
import { updateSession } from '@/utils/supabase/middleware';

export async function middleware(request: NextRequest) {
  // First, update the session using the standard middleware
  const response = await updateSession(request);
  response.headers.set('x-current-path', request.nextUrl.pathname);
  
  const { pathname } = request.nextUrl;
  
  // Skip auth-related pages and static assets
  if (
    pathname.startsWith('/auth') ||
    pathname === '/signin' ||
    pathname === '/signup' ||
    pathname.startsWith('/migrate-option') ||
    pathname === '/'
  ) {
    return response;
  }
  
  try {
    // Get current user session
    const supabase = createClient();
    const { data: { session } } = await supabase.auth.getSession();
    
    // If no session, allow the response to continue (will likely redirect to login)
    if (!session) {
      return response;
    }
    
    const user = session.user;
    
    // Check if user logged in via magic link (email OTP) without a password
    // IMPORTANT: Never redirect OAuth users to set-password page
    const isOAuthUser = user.app_metadata.provider && user.app_metadata.provider !== 'email';
    const isMagicLinkUser = !user.app_metadata.provider || user.app_metadata.provider === 'email';

    if (
      isMagicLinkUser && // Only for magic link/email users, NOT OAuth
      !isOAuthUser && // Double check: never redirect OAuth users
      user.identities?.length === 1 && // Only has email identity
      !user.user_metadata.has_set_password && // Hasn't set password yet
      user.created_at === user.updated_at // First sign in (created = updated)
    ) {
      // Redirect to password setup page
      return NextResponse.redirect(new URL('/auth/set-password', request.url));
    }
    
    // Continue with standard response if no redirect needed
    return response;
  } catch (error) {
    console.error('Middleware error:', error);
    // Continue with standard response in case of error
    return response;
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images - .svg, .png, .jpg, .jpeg, .gif, .webp
     * - api routes
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$|api).*)'
  ]
};
