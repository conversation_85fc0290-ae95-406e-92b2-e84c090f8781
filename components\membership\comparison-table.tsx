"use client"

import { Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CheckoutButton } from "@/components/membership/checkout-button"
import { SUBSCRIPTION_PLANS } from "@/utils/stripe/config"

interface PricingPlan {
  name: string
  price: string
  period: "monthly" | "annual" | "semi-annual" | "one-time"
  description: string
  features: readonly string[]
  ctaText: string
  popular?: boolean
}

interface ComparisonTableProps {
  plans: readonly PricingPlan[]
  user: any
  loading: boolean
}

// Feature categories and their features
const featureCategories = [
  {
    name: "Job Search Tools",
    features: ["Daily Job Alerts", "Direct Apply Tools", "Curated Weekly Job Lists", "Exclusive Job Opportunities"],
  },
  {
    name: "Networking and Support",
    features: [
      "Graduate Career Bootcamp Events",
      "Insider Referrals",
      "LinkedIn Networking Enhancement",
      "Priority Job Referrals",
    ],
  },
  {
    name: "Professional Development",
    features: [
      "AI-Powered Career Coaching",
      "Capstone Projects",
      "Application Review Service",
      "Documented Coaching Sessions",
    ],
  },
  {
    name: "Technical and Resource Access",
    features: ["Interview Prep and Resources", "Support for Internships and OPT", "Enhanced Network Security"],
  },
]

// Feature availability by plan
const featureAvailability = {
  "Essential Access Pass": [
    // Job Search Tools
    true,
    true,
    false,
    false,
    // Networking and Support
    false,
    false,
    false,
    false,
    // Professional Development
    false,
    false,
    false,
    false,
    // Technical and Resource Access
    false,
    false,
    false,
  ],
  "Pro Monthly Pass": [
    // Job Search Tools
    true,
    true,
    true,
    false,
    // Networking and Support
    true,
    true,
    false,
    false,
    // Professional Development
    true,
    false,
    true,
    false,
    // Technical and Resource Access
    true,
    true,
    false,
  ],
  "Elite Semi-Annual Pass": [
    // Job Search Tools
    true,
    true,
    true,
    true,
    // Networking and Support
    true,
    true,
    true,
    false,
    // Professional Development
    true,
    true,
    false,
    false,
    // Technical and Resource Access
    true,
    true,
    false,
  ],
  "Ultimate Annual Pass": [
    // Job Search Tools
    true,
    true,
    true,
    true,
    // Networking and Support
    true,
    true,
    true,
    true,
    // Professional Development
    true,
    true,
    true,
    true,
    // Technical and Resource Access
    true,
    true,
    true,
  ],
}

export function ComparisonTable({ plans, user, loading }: ComparisonTableProps) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse">
        <thead>
          <tr className="border-b">
            <th className="p-4 text-left"></th>
            {plans.map((plan) => (
              <th key={plan.name} className="p-4 text-center">
                <div className="font-bold">{plan.name}</div>
                <div className="text-2xl font-bold mt-2">{plan.price}</div>
                <div className="text-sm text-gray-500 mb-4">
                  {plan.period === "monthly" && "billed monthly"}
                  {plan.period === "annual" && "billed yearly"}
                  {plan.period === "semi-annual" && "billed every 6 months"}
                  {plan.period === "one-time" && "one-time payment"}
                </div>
                {/* Match plan name with SUBSCRIPTION_PLANS, which might have different naming */}
                {SUBSCRIPTION_PLANS?.find((p) => p.name === plan.name) ? (
                  <CheckoutButton
                    priceId={SUBSCRIPTION_PLANS.find((p) => p.name === plan.name)?.id || ""}
                    ctaText="Get Started"
                    variant="outline"
                    className="w-full"
                    user={user}
                    loading={loading}
                  />
                ) : (
                  <Button variant="outline" className="w-full">
                    Get Started
                  </Button>
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {featureCategories.map((category, categoryIndex) => (
            <>
              <tr key={category.name} className="bg-gray-100">
                <td colSpan={plans.length + 1} className="p-4 font-bold">
                  {category.name}
                </td>
              </tr>
              {category.features.map((feature, featureIndex) => {
                // Calculate the absolute index for the feature
                let absoluteFeatureIndex = 0
                for (let i = 0; i < categoryIndex; i++) {
                  absoluteFeatureIndex += featureCategories[i].features.length
                }
                absoluteFeatureIndex += featureIndex

                return (
                  <tr key={feature} className="border-b">
                    <td className="p-4 flex items-center">
                      <span className="text-sm">{feature}</span>
                    </td>
                    {plans.map((plan) => {
                      // Safely check if the plan name exists in featureAvailability
                      const planFeatures = featureAvailability[plan.name as keyof typeof featureAvailability];
                      // Default to false if plan not found or feature index out of bounds
                      const isAvailable = planFeatures && absoluteFeatureIndex < planFeatures.length
                        ? planFeatures[absoluteFeatureIndex]
                        : false;
                        
                      return (
                        <td key={`${plan.name}-${feature}`} className="p-4 text-center">
                          {isAvailable ? (
                            <Check className="mx-auto h-5 w-5 text-green-500" />
                          ) : (
                            <X className="mx-auto h-5 w-5 text-gray-300" />
                          )}
                        </td>
                      )
                    })}
                  </tr>
                )
              })}
            </>
          ))}
        </tbody>
      </table>
    </div>
  )
}

