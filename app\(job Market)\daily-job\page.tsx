'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { format, isWithinInterval, startOfDay, endOfDay } from 'date-fns';
import { getDailyJobs } from '@/actions/admin/web-crawler';
import type { DailyJob } from '@/types/crawler';
import type { DateRange } from 'react-day-picker';
import {
  CalendarIcon,
  Eye,
  Loader2,
  X,
  ChevronDown,
  UserPlus,
  Crown,
  Sparkles,
  Check,
  FileText,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { useUser } from '@/hooks/useUser';

export default function DailyJobPage() {
  const router = useRouter();
  const { user, loading: userLoading } = useUser();
  const [dateRange, setDateRange] = useState<DateRange>({
    from: undefined,
    to: undefined
  });
  const [fileType, setFileType] = useState('all');
  const [jobs, setJobs] = useState<DailyJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [displayCount, setDisplayCount] = useState(20);

  // Sample data for non-VIP users
  const sampleJobs: DailyJob[] = [
    {
      id: 'sample1',
      file_type: 'Daily Jobs',
      posting_date: new Date(),
      crawler_file:
        'https://docs.google.com/spreadsheets/d/1UExo3skYgVCbPfM0AQq7_nuCO2b7qHTe/edit?gid=1516982664#gid=1516982664',
      downloader_vip: [],
      downloader_nonvip: [],
      notice: 'Daily Jobs'
    },
    {
      id: 'sample2',
      file_type: 'Smart Search',
      posting_date: new Date(Date.now() - 86400000),
      crawler_file:
        'https://docs.google.com/spreadsheets/d/1UExo3skYgVCbPfM0AQq7_nuCO2b7qHTe/edit?gid=1516982664#gid=1516982664',
      downloader_vip: [],
      downloader_nonvip: [],
      notice: 'Jobs in Small Towns'
    }
  ];

  useEffect(() => {
    const loadJobs = async () => {
      try {
        if (user) {
          const data = await getDailyJobs();
          setJobs(data);
        } else {
          // Use sample data for non-registered users
          setJobs(sampleJobs);
        }
      } catch (error) {
        console.error('Error loading jobs:', error);
        toast({
          title: 'Error',
          description: 'Failed to load jobs',
          variant: 'destructive'
        });
        // Fallback to sample data on error
        setJobs(sampleJobs);
      } finally {
        setLoading(false);
      }
    };

    loadJobs();
  }, [user]);

  const viewSample = () => {
    window.open(
      'https://docs.google.com/spreadsheets/d/1UExo3skYgVCbPfM0AQq7_nuCO2b7qHTe/edit?gid=1516982664#gid=1516982664',
      '_blank'
    );
  };

  const startTrial = () => {
    window.open('/membership', '_blank');
  };

  const redirectToSignup = () => {
    router.push('/signup');
  };

  // Get most recent jobs by type for non-VIP users
  const getMostRecentJobsByType = () => {
    if (!jobs.length) return [];

    // Create a map to store the most recent job of each type
    const mostRecentJobs = new Map();

    // Find the most recent job of each type
    jobs.forEach((job) => {
      const type = job.file_type || 'Daily Jobs';
      if (
        !mostRecentJobs.has(type) ||
        (job.posting_date &&
          mostRecentJobs.get(type).posting_date &&
          new Date(job.posting_date) >
            new Date(mostRecentJobs.get(type).posting_date))
      ) {
        mostRecentJobs.set(type, job);
      }
    });

    // Return only Daily Jobs and Smart Search types
    const result = [];
    if (mostRecentJobs.has('Daily Jobs')) {
      result.push(mostRecentJobs.get('Daily Jobs'));
    }
    if (mostRecentJobs.has('Smart Search')) {
      result.push(mostRecentJobs.get('Smart Search'));
    }

    return result;
  };

  // Filter jobs based on selected file type and date range
  const getFilteredJobs = () => {
    // For VIP users, show all jobs with filters applied
    if (user?.vip) {
      return jobs.filter((job) => {
        let matchesType = true;
        if (fileType !== 'all') {
          matchesType = job.file_type === fileType;
        }

        if (dateRange.from && job.posting_date) {
          const jobDate = startOfDay(new Date(job.posting_date));
          const fromDate = startOfDay(dateRange.from as Date);
          const toDate = dateRange.to
            ? endOfDay(dateRange.to as Date)
            : endOfDay(dateRange.from as Date);

          return (
            matchesType &&
            isWithinInterval(jobDate, { start: fromDate, end: toDate })
          );
        }

        return matchesType;
      });
    }

    // For non-VIP users, only show the most recent Daily Job and Smart Search
    if (user && !user.vip) {
      const recentJobs = getMostRecentJobsByType();

      // Apply file type filter
      if (fileType !== 'all') {
        return recentJobs.filter((job) => job.file_type === fileType);
      }

      // Apply date filter if it exists
      if (dateRange.from) {
        return recentJobs.filter((job) => {
          if (!job.posting_date) return false;

          const jobDate = startOfDay(new Date(job.posting_date));
          const fromDate = startOfDay(dateRange.from as Date);
          const toDate = dateRange.to
            ? endOfDay(dateRange.to as Date)
            : endOfDay(dateRange.from as Date);

          return isWithinInterval(jobDate, { start: fromDate, end: toDate });
        });
      }

      return recentJobs;
    }

    // For non-registered users, show sample jobs
    return sampleJobs;
  };

  const filteredJobs = getFilteredJobs();

  const formatDateRange = (range: DateRange) => {
    if (!range.from) return 'Pick a date';
    if (!range.to) return format(range.from, 'PPP');
    return `${format(range.from, 'PPP')} - ${format(range.to, 'PPP')}`;
  };

  const isJobAccessible = (job: DailyJob) => {
    if (user?.vip) return true;
    if (!user) return false;

    // Non-VIP users can only access the most recent Daily Job and Smart Search
    const recentJobs = getMostRecentJobsByType();
    return recentJobs.some((recentJob) => recentJob.id === job.id);
  };

  // Enhanced function to handle clicking on a job
  const handleJobClick = async (job: DailyJob) => {
    if (isJobAccessible(job)) {
      // Remove tracking from here - we will only track on the detail page
      // to avoid duplicate entries

      // Navigate to the job detail page
      router.push(`/daily-job/${job.id}`);
    } else {
      startTrial();
    }
  };

  if (loading || userLoading) {
    return (
      <div className="container mx-auto py-12 flex justify-center items-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Daily Job Board</h1>
        <p className="text-muted-foreground max-w-3xl">
          Access daily curated job listings from top companies. Our platform
          aggregates positions in Software Development, Data Science, Product
          Management, and more.
          {!user?.vip &&
            ' Upgrade to VIP for unlimited access to all historical job listings.'}
        </p>
      </div>

      {/* Information about job types */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-6">
          <Filter className="h-5 w-5 text-gray-500" />
          <h2 className="text-2xl font-bold text-gray-800">
            About Our Job Listings
          </h2>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {/* Daily Jobs Card */}
          <div className="bg-white rounded-lg border-l-4 border-red-400 border-t border-r border-b border-gray-100 p-8 hover:shadow-sm transition-all">
            <div className="w-12 h-12 bg-red-50 rounded-full flex items-center justify-center mb-5">
              <CalendarIcon className="h-6 w-6 text-red-500" />
            </div>

            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              Daily Jobs
            </h3>

            <p className="text-gray-600 mb-5">
              Fresh opportunities posted within the{' '}
              <span className="font-medium">last 24 hours</span>. We collect and
              organize these daily to ensure you're always seeing the newest
              listings.
            </p>

            <div className="mt-auto">
              <span className="inline-block px-3 py-1 rounded-md text-sm font-medium bg-red-50 text-red-500">
                Updated Daily
              </span>
            </div>
          </div>

          {/* Weekly Intern Card */}
          <div className="bg-white rounded-lg border-l-4 border-sky-400 border-t border-r border-b border-gray-100 p-8 hover:shadow-sm transition-all">
            <div className="w-12 h-12 bg-sky-50 rounded-full flex items-center justify-center mb-5">
              <CalendarIcon className="h-6 w-6 text-sky-500" />
            </div>

            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              Weekly Intern
            </h3>

            <p className="text-gray-600 mb-5">
              Internship positions from the{' '}
              <span className="font-medium">past week</span>. Curated for
              students looking for professional experience and career
              development opportunities.
            </p>

            <div className="mt-auto">
              <span className="inline-block px-3 py-1 rounded-md text-sm font-medium bg-sky-50 text-sky-500">
                Updated Saturdays
              </span>
            </div>
          </div>

          {/* Smart Search Card */}
          <div className="bg-white rounded-lg border-l-4 border-green-400 border-t border-r border-b border-gray-100 p-8 hover:shadow-sm transition-all">
            <div className="w-12 h-12 bg-green-50 rounded-full flex items-center justify-center mb-5">
              <CalendarIcon className="h-6 w-6 text-green-500" />
            </div>

            <h3 className="text-xl font-semibold text-gray-800 mb-3">
              Smart Search
            </h3>

            <p className="text-gray-600 mb-5">
              Specialized categories updated weekly including:{' '}
              <span className="font-medium">
                FA, Quant, Supply Chain, UIUX, Traditional Engineering Jobs,
                Jobs in Small Towns
              </span>
              . Targeted searches to find exactly what you're looking for.
            </p>

            <div className="mt-auto">
              <span className="inline-block px-3 py-1 rounded-md text-sm font-medium bg-green-50 text-green-500">
                Updated Fridays
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        {/* For all users: Sample view button */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">View Sample</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-2">
              Preview a sample job listings file
            </p>
            <Button variant="outline" className="w-full" onClick={viewSample}>
              View Sample
            </Button>
          </CardContent>
        </Card>

        {/* For VIP users: Status badge */}
        {user?.vip && (
          <Card className="bg-primary/5 border-primary/20 md:col-span-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">VIP Status</CardTitle>
              <Crown className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="default" className="bg-primary">
                  Active
                </Badge>
                <span className="text-sm text-muted-foreground">
                  Full access to all job listings
                </span>
              </div>
              <p className="text-sm text-muted-foreground">
                You have unlimited access to all {jobs.length} job listings
                including archives
              </p>
            </CardContent>
          </Card>
        )}

        {/* For non-VIP users: Upgrade card */}
        {user && !user.vip && (
          <Card className="bg-gradient-to-r from-sky-50 to-sky-100 border-sky-200 overflow-hidden relative md:col-span-2">
            <div className="absolute top-0 right-0 p-1.5">
              <span className="inline-flex items-center rounded-sm bg-sky-100 px-1.5 py-0.5 text-xs font-medium text-sky-800 ring-1 ring-inset ring-sky-200">
                Recommended
              </span>
            </div>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-sky-800">
                <Crown className="h-5 w-5 text-sky-500" />
                Upgrade to VIP for Full Access
              </CardTitle>
              <CardDescription className="text-sky-700">
                Get unlimited access to all job listings including historical
                data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-medium text-sky-900">
                    Free Account (Current)
                  </h4>
                  <ul className="space-y-1 text-sm text-sky-700">
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Access to newest Daily
                      Jobs only
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Most recent Smart
                      Search only
                    </li>
                    <li className="flex items-center opacity-50">
                      <X className="mr-2 h-4 w-4" /> No access to archives
                    </li>
                    <li className="flex items-center opacity-50">
                      <X className="mr-2 h-4 w-4" /> No Weekly Referral access
                    </li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-sky-900 flex items-center">
                    VIP Account{' '}
                  </h4>
                  <ul className="space-y-1 text-sm text-sky-700">
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Complete access to all
                      job lists
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Full archive access
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Weekly Referral job
                      listings
                    </li>
                    <li className="flex items-center">
                      <Check className="mr-2 h-4 w-4" /> Priority support
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col sm:flex-row items-center gap-4 sm:justify-between bg-gradient-to-r from-sky-100/50 to-sky-200/50 px-6 py-4">
              <div className="flex items-center">
                <Sparkles className="h-5 w-5 text-sky-500 mr-2" />
                <span className="text-sky-800 text-sm">
                  Unlock all {jobs.length} job listings
                </span>
              </div>
              <Button
                onClick={startTrial}
                className="bg-sky-600 hover:bg-sky-700 w-full sm:w-auto"
              >
                <Crown className="mr-2 h-4 w-4" />
                Upgrade to VIP
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-muted-foreground" />
            Job Listings
          </CardTitle>
          <CardDescription>
            Browse and download daily job listings
            {user && !user.vip && (
              <span className="block mt-1 text-sky-600 font-medium text-sm">
                Free accounts can only access the most recent Daily Job and
                Smart Search
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full justify-start text-left font-normal',
                      !dateRange.from && 'text-muted-foreground',
                      !user && 'opacity-50 cursor-not-allowed'
                    )}
                    disabled={!user}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {formatDateRange(dateRange)}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(value: DateRange | undefined) => {
                      setDateRange({
                        from: value?.from,
                        to: value?.to
                      });
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
              {(dateRange.from || dateRange.to) && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={() =>
                    setDateRange({ from: undefined, to: undefined })
                  }
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Clear date range</span>
                </Button>
              )}
            </div>
            <Select
              value={fileType}
              onValueChange={setFileType}
              disabled={!user}
            >
              <SelectTrigger
                className={cn(
                  'w-full md:w-[200px]',
                  !user && 'opacity-50 cursor-not-allowed'
                )}
              >
                <SelectValue placeholder="Select file type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="Daily Jobs">Daily Jobs</SelectItem>
                <SelectItem value="Smart Search">Smart Search</SelectItem>
                <SelectItem value="Weekly Intern">Weekly Intern</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {!user ? (
            <div className="rounded-md border bg-muted/30 p-6 relative">
              <div className="absolute inset-0 flex flex-col items-center justify-center bg-background/80 backdrop-blur-[1px] z-10">
                <div className="text-center max-w-md space-y-4 p-6">
                  <UserPlus className="h-10 w-10 text-primary mx-auto opacity-80" />
                  <h3 className="text-xl font-semibold">
                    Sign up to view jobs
                  </h3>
                  <p className="text-muted-foreground">
                    Create an account to browse our daily curated job listings
                    and access more features
                  </p>
                  <Button className="mt-2" onClick={redirectToSignup}>
                    Sign up now
                  </Button>
                </div>
              </div>
              <Table>
                <TableHeader>
                  <TableRow className="opacity-40">
                    <TableHead>File Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Access</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sampleJobs.map((job) => (
                    <TableRow key={job.id} className="opacity-40">
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={cn(
                            'font-normal',
                            job.file_type === 'Daily Jobs' &&
                              'border-red-200 text-red-700',
                            job.file_type === 'Weekly Intern' &&
                              'border-sky-200 text-sky-700',
                            job.file_type === 'Smart Search' &&
                              'border-green-200 text-green-700'
                          )}
                        >
                          {job.file_type === 'Smart Search' && job.notice
                            ? `Smart Search: ${job.notice}`
                            : job.file_type || 'Daily Jobs'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {job.posting_date
                          ? format(new Date(job.posting_date), 'PPP')
                          : 'N/A'}
                      </TableCell>
                      <TableCell>
                        <Badge variant="default" className="bg-primary">
                          Sample
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>File Type</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Access</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredJobs.length > 0 ? (
                    <>
                      {filteredJobs.slice(0, displayCount).map((job) => (
                        <TableRow
                          key={job.id}
                          onClick={() => handleJobClick(job)}
                          className={cn(
                            'cursor-pointer',
                            isJobAccessible(job)
                              ? 'hover:bg-muted/50'
                              : 'hover:bg-sky-50/30'
                          )}
                        >
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={cn(
                                'font-normal',
                                job.file_type === 'Daily Jobs' &&
                                  'border-red-200 text-red-700',
                                job.file_type === 'Weekly Intern' &&
                                  'border-sky-200 text-sky-700',
                                job.file_type === 'Smart Search' &&
                                  'border-green-200 text-green-700'
                              )}
                            >
                              {job.file_type === 'Smart Search'
                                ? job.notice
                                  ? `Smart Search: ${job.notice}`
                                  : 'Smart Search'
                                : job.file_type || 'Daily Jobs'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {job.posting_date
                              ? format(new Date(job.posting_date), 'PPP')
                              : 'N/A'}
                          </TableCell>
                          <TableCell>
                            {user?.vip ? (
                              <Badge variant="default" className="bg-primary">
                                VIP
                              </Badge>
                            ) : isJobAccessible(job) ? (
                              <Badge variant="default" className="bg-primary">
                                Free
                              </Badge>
                            ) : (
                              <Badge
                                variant="secondary"
                                className="flex items-center"
                              >
                                <Crown className="h-3 w-3 mr-1 text-sky-500" />
                                VIP Only
                              </Badge>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                      {filteredJobs.length > displayCount && (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center p-4">
                            <Button
                              variant="outline"
                              className="w-full max-w-sm"
                              onClick={() =>
                                setDisplayCount((prev) => prev + 20)
                              }
                            >
                              <ChevronDown className="h-4 w-4 mr-2" />
                              View More
                              <span className="ml-2 text-xs text-muted-foreground">
                                ({filteredJobs.length - displayCount} remaining)
                              </span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      )}
                    </>
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={3}
                        className="text-center py-6 text-muted-foreground"
                      >
                        No files found for the selected filters
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
