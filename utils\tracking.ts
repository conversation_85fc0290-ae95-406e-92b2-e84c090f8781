/**
 * Utility functions for tracking user actions
 */
import { trackFileView as serverTrackFileView } from '@/actions/admin/web-crawler';

/**
 * Track when a user views or downloads a file
 * @param fileId The ID of the file being viewed/downloaded
 * @param userId The ID of the user viewing/downloading the file
 * @param isVip Whether the user is a VIP
 * @returns A promise that resolves to a boolean indicating success
 */
export async function trackFileView(
  fileId: string,
  userId?: string,
  isVip?: boolean
): Promise<boolean> {
  try {
    // Skip tracking if no user
    if (!userId) {
      console.log('No user ID provided, skipping tracking');
      return false;
    }

    // Call the server action directly with user info
    const trackingResult = await serverTrackFileView(
      fileId,
      userId,
      !!isVip // Convert to boolean
    );

    if (!trackingResult.success) {
      console.error('Error tracking file view:', trackingResult.error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error tracking file view:', error);
    return false;
  }
}
