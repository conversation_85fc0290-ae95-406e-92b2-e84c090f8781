# **Migrating from Bubble Auth to Supabase Auth**

## ✅ Objective

Seamlessly transition from Bubble’s authentication system to Supabase Auth while ensuring data security, minimal disruption to users, and future scalability.

---

## 🔄 Migration Strategy Overview

Since all user information already exists in your Supabase database (excluding passwords), the focus is to migrate **authentication**, not profile data.

---

## 📌 Key Principles

- **User-First Communication**  
  Notify users proactively. Explain why the change is happening, what actions they need to take, and how it improves security.

- **No Password Migration**  
  Bubble password hashes likely cannot be reused. Users will reset passwords through Supabase’s secure flow.

- **Track Migration Status**  
  Add a `migration_status` field in the `profiles` table to track states like `requires_password_reset`, `completed`.

---

## 🛠️ Step-by-Step Migration Plan

### Phase 1: Preparation & Supabase Setup

1. **Design Database Schema**

   - Use `auth.users` for authentication.
   - Create a `profiles` table (formerly `users`) with a foreign key to `auth.users.id`.
   - Add `migration_status` field.

2. **Export from Bubble**
   - Extract user emails and IDs (no passwords).
   - Format for import into Supabase.

---

### Phase 2: Build Supabase Auth (In Parallel)

1. **Create Auth UI**

   - Build sign-in, sign-up, reset password, and profile components.
   - Use **Tailwind CSS**, **Shadcn/UI**, and **Radix UI**.

2. **Implement Auth Functions**

   - **Sign-Up**:  
     `supabase.auth.signUp()`

   - **Sign-In**:  
     `signInWithPassword()`  
     If login fails and `migration_status` is `requires_password_reset`, prompt user for a reset.

   - **Password Reset Flow**:  
     `supabase.auth.resetPasswordForEmail(email)`  
     After reset, update `migration_status` to `completed`.

   - **Sign-Out**:  
     `supabase.auth.signOut()`

   - **OAuth (Optional)**:  
     Use `signInWithOAuth()` and configure in Supabase dashboard.

3. **Manage Auth State**

   - Create a custom `SupabaseAuthProvider` using React context.
   - Use `onAuthStateChange` to monitor auth state.

4. **Protect Routes**
   - Implement middleware to guard API and page routes.
   - Redirect unauthenticated users as needed.

---

### Phase 3: Migrate User Accounts (No Passwords)

> ✅ Already completed — confirm for accuracy.

1. **Backend Script**
   - Use Node.js to:
     - Check if user exists in `auth.users`; create if missing.
     - Create corresponding entries in `profiles` with `migration_status: requires_password_reset`.

---

### Phase 4: Switch to Supabase Auth

1. **Final Testing**

   - Validate login, logout, sign-up, reset password, OAuth (if used), and session management.

2. **User Communication**

   - **Before Migration**: Send announcement email.
   - **On Login**: Display message:

     > _“We’ve upgraded our authentication system. Please set a new password to continue.”_

3. **Deploy**

   - Launch the Supabase-auth-enabled version of the Next.js app.

4. **Cut Over**
   - Remove Bubble Auth logic from frontend.
   - Update all routes to point to Supabase Auth flows.

---

### Phase 5: Post-Migration Monitoring

1. **Track Errors**

   - Monitor Supabase logs for auth issues.
   - Prepare support channels for password reset or login problems.

2. **Deprecate Bubble Auth**
   - Remove Bubble SDK and legacy auth code after most users migrate.

---

## ✅ Finalized Plan: Gradual Password Reset on Login

- **Import**: Emails and user IDs only.
- **Flag** users with `migration_status: requires_password_reset`.
- **On login**, check `migration_status`. If needed, prompt password reset.
- **Use** Supabase's built-in reset flow for secure password setup.
- **Update** `migration_status` to `completed` upon success.

---

## 🧪 Testing Checklist

- [ ] Email/password sign-up
- [ ] Email/password login (with migration prompt)
- [ ] Password reset flow
- [ ] OAuth login (if applicable)
- [ ] Auth state tracking via React context
- [ ] Route protection (authenticated vs. public)
- [ ] Session persistence across refresh

---
