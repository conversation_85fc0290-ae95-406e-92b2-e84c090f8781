'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import { useOnboarding } from '../components/onboarding-provider';

export default function CompletePage() {
  const router = useRouter();
  const { isStepCompleted } = useOnboarding();

  // Redirect if the company-info step is not completed
  useEffect(() => {
    if (!isStepCompleted('company-info')) {
      router.push('/onboarding/company/company-info');
    }
  }, [isStepCompleted, router]);

  // Auto-redirect to landing page after 5 seconds
  useEffect(() => {
    if (isStepCompleted('company-info')) {
      const timer = setTimeout(() => {
        router.push('/');
      }, 5000); // 5 second delay

      return () => clearTimeout(timer);
    }
  }, [isStepCompleted, router]);

  const handleGoToLandingPage = () => {
    router.push('/');
  };

  return (
    <div className="container max-w-3xl mx-auto py-16">
      <div className="text-center mb-10">
        <div className="flex justify-center mb-6">
          <div className="h-24 w-24 rounded-full bg-green-50 flex items-center justify-center">
            <CheckCircle className="h-12 w-12 text-[#36BA98]" />
          </div>
        </div>
        <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent mb-2">
          Onboarding Complete!
        </h1>
        <p className="text-gray-600 max-w-md mx-auto">
          Thank you for providing your company information. You're now ready to
          start connecting with talented candidates from around the world.
        </p>
        <p className="text-sm text-gray-500 mt-4">
          You will be redirected to the homepage in a few seconds...
        </p>
      </div>

      <div className="bg-white rounded-lg shadow p-8 mb-10">
        <h2 className="text-xl font-semibold mb-6">What's Next?</h2>
        <div className="space-y-4">
          <div className="flex items-start gap-4">
            <div className="bg-[#36BA98] bg-opacity-10 rounded-full p-2 mt-1">
              <span className="text-[#36BA98] font-bold">1</span>
            </div>
            <div>
              <h3 className="font-medium">Create a Job Posting</h3>
              <p className="text-gray-600 text-sm">
                Start attracting candidates by creating a job posting with
                detailed requirements and expectations.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="bg-[#36BA98] bg-opacity-10 rounded-full p-2 mt-1">
              <span className="text-[#36BA98] font-bold">2</span>
            </div>
            <div>
              <h3 className="font-medium">Browse Candidate Profiles</h3>
              <p className="text-gray-600 text-sm">
                Explore our talent pool to find qualified candidates that match
                your company's needs.
              </p>
            </div>
          </div>
          <div className="flex items-start gap-4">
            <div className="bg-[#36BA98] bg-opacity-10 rounded-full p-2 mt-1">
              <span className="text-[#36BA98] font-bold">3</span>
            </div>
            <div>
              <h3 className="font-medium">Connect with Candidates</h3>
              <p className="text-gray-600 text-sm">
                Reach out to potential candidates and start the interview
                process.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="text-center">
        <Button
          onClick={handleGoToLandingPage}
          className="px-8 py-6 bg-[#36BA98] hover:bg-[#2da888] text-white"
        >
          Go to Home Page
        </Button>
      </div>
    </div>
  );
}
