'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2, AlertCircle } from "lucide-react";
import { createClient } from '@/utils/supabase/client';
import { bubbleClient } from "@/utils/bubble/client";

export default function AuthCallbackHandler() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState<string>('Initializing authentication...');
  const supabase = createClient();

  useEffect(() => {
    console.log('[Client] Auth callback handler initialized');
    
    const handleAuthentication = async () => {
      try {
        setStatus('Processing authentication data...');
        
        // Get the user type from search params or localStorage (saved during OAuth initiation)
        let userType = searchParams.get('user_type');
        
        // Fallback to localStorage if not in URL params
        if (!userType) {
          userType = localStorage.getItem('oauth_user_type') || '';
        }
        
        // Set default user type to "candidate" if not specified
        const finalUserType = userType || 'Candidate';
        console.log('[Client] Using user type:', finalUserType);
        
        // Get the authentication flow (signin/signup)
        const flow = searchParams.get('flow') || 'signup';

        // Exchange the code for a session directly from the client
        const { data, error: sessionError } = await supabase.auth.getUser();
        console.log('[Client] User data:', data);
        
        if (sessionError || !data?.user) {
          console.error("Session exchange error:", sessionError);
          throw new Error(sessionError?.message || "Could not sign you in. Please try again.");
        }
        
        const email = data.user.email;
        if (!email) {
          throw new Error("We couldn't get your email address. Please try another sign-in method.");
        }
        
        setStatus('Verifying user information...');
        
        // Check if the user already exists in Supabase by email
        const { data: existingUser, error: findError } = await supabase
          .from('users')
          .select('id, email, user_type')
          .eq('email', email)
          .single();
          
        let bubbleUserId: string;
        let bubbleToken: string = '';
        
        if (existingUser) {
          // User exists - use their existing bubble_user_id and user_type
          bubbleUserId = existingUser.id;
          
          // If the user is already an admin, keep them as admin regardless of the OAuth flow
          if (existingUser.user_type && existingUser.user_type.toLowerCase() === 'admin') {
            // Admin users keep their type
          } else if (existingUser.user_type) {
            // Use the user's existing type if available
          } else {
            // If existing user has no type, set it to candidate as default
            existingUser.user_type = 'candidate';
          }
          
          // Generate a new Bubble token client-side
          bubbleToken = `oauth-${Date.now()}-${bubbleUserId}-${Math.random().toString(36).substring(2)}`;
          
        } else {
          // User doesn't exist - create them in Bubble first (sign-up flow)
          setStatus('Creating new user account...');
          
          try {
            // Generate a random secure password for Bubble
            const randomPassword = Array(20)
              .fill('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*')
              .map(x => x[Math.floor(Math.random() * x.length)])
              .join('');
            
            // Create the user in Bubble with finalUserType (which is either the passed value or 'candidate')
            const bubbleResponse = await bubbleClient.signUp(email, randomPassword, finalUserType);
            
            if (bubbleResponse && bubbleResponse.status !== 'success') {
              throw new Error('Failed to create user in Bubble');
            }
            
            bubbleUserId = bubbleResponse && bubbleResponse.response ? bubbleResponse.response.user_id : '';
            bubbleToken = bubbleResponse && bubbleResponse.response ? bubbleResponse.response.token : '';
            
            if (!bubbleUserId) {
              throw new Error('Failed to get user ID from Bubble');
            }
          } catch (error: any) {
            console.error('Error creating Bubble user:', error);
            throw new Error("Could not complete your registration. Please try again.");
          }
        }
        
        // Update the user in the users table with the appropriate user_type
        setStatus('Updating user information...');
        
        // Use the finalUserType if it's a new user, otherwise respect the existing user_type
        const userTypeToSave = existingUser?.user_type || finalUserType;
        
        const { error: upsertError } = await supabase
          .from('users')
          .upsert({
            id: bubbleUserId,
            email: email,
            user_type: userTypeToSave,
            avatar_url: data.user.user_metadata?.avatar_url || '',
            verified: true, // OAuth users are pre-verified
            migration_status: 'migrated', // OAuth users are automatically migrated
            updated_at: new Date().toISOString(),
          }, { onConflict: 'id' });
        
        if (upsertError) {
          console.error('Error upserting user in Supabase:', upsertError);
          // Continue despite upsert error
        }
        
        // Determine the redirect path based on user type
        let redirectPath: string;
        
        switch (userTypeToSave.toLowerCase()) {
          case 'admin':
            redirectPath = '/admin';
            break;
          case 'insider':
            redirectPath = '/insider';
            break;
          case 'company':
            redirectPath = '/company';
            break;
          case 'candidate':
          default:
            redirectPath = '/candidate';
        }
        
        setStatus(`Saving authentication data...`);
        
        // Store authentication data in localStorage
        try {
          localStorage.setItem('bubble_auth_token', bubbleToken);
          localStorage.setItem('bubble_user_id', bubbleUserId);
          localStorage.setItem('user_type', userTypeToSave);
          console.log('[Client] Auth data stored in localStorage');
          
          // Clean up the temporary OAuth user type
          localStorage.removeItem('oauth_user_type');
        } catch (err) {
          console.error('[Client] Failed to store in localStorage:', err);
          throw new Error('Failed to save authentication data');
        }
        
        setStatus(`Redirecting to ${redirectPath}...`);
        console.log(`[Client] Redirecting to ${redirectPath}`);
        
        // Use a small delay to ensure the UI updates before redirecting
        setTimeout(() => {
          window.location.href = redirectPath;
        }, 500);
        
      } catch (err: any) {
        console.error('[Client] Authentication error:', err);
        setError(err.message || 'Authentication failed. Please try signing in again.');
        setTimeout(() => router.replace('/signin'), 2000);
      }
    };
    
    handleAuthentication();
    
    // Debug panel code remains the same
    const debugDiv = document.createElement('div');
    debugDiv.id = 'debug-info';
    debugDiv.style.position = 'fixed';
    debugDiv.style.top = '0';
    debugDiv.style.right = '0';
    debugDiv.style.padding = '10px';
    debugDiv.style.background = 'rgba(0,0,0,0.7)';
    debugDiv.style.color = 'white';
    debugDiv.style.zIndex = '9999';
    debugDiv.style.fontSize = '12px';
    debugDiv.style.display = 'none'; // Hidden by default
    
    const updateDebugInfo = () => {
      debugDiv.innerHTML = `
        <p>Debug Mode (Press D to toggle)</p>
        <p>URL params:</p>
        <ul>
          <li>code: ${searchParams.get('code') || 'null'}</li>
          <li>user_type: ${searchParams.get('user_type') || 'null'}</li>
          <li>flow: ${searchParams.get('flow') || 'null'}</li>
        </ul>
        <p>LocalStorage:</p>
        <ul>
          <li>oauth_user_type: ${localStorage.getItem('oauth_user_type') || 'null'}</li>
          <li>bubble_auth_token: ${localStorage.getItem('bubble_auth_token') ? `${localStorage.getItem('bubble_auth_token')?.substring(0, 10)}...` : 'null'}</li>
          <li>bubble_user_id: ${localStorage.getItem('bubble_user_id') || 'null'}</li>
          <li>user_type: ${localStorage.getItem('user_type') || 'null'}</li>
        </ul>
        <p>Status: ${status}</p>
        <p>Error: ${error || 'none'}</p>
      `;
    };
    
    document.body.appendChild(debugDiv);
    updateDebugInfo();
    
    const debugInterval = setInterval(updateDebugInfo, 1000);
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key.toLowerCase() === 'd') {
        debugDiv.style.display = debugDiv.style.display === 'none' ? 'block' : 'none';
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      clearInterval(debugInterval);
      if (document.body.contains(debugDiv)) {
        document.body.removeChild(debugDiv);
      }
    };
  }, [router, searchParams, supabase]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-8">
        {error ? (
          <>
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-2xl font-bold mb-4">Authentication Error</h2>
              <p className="text-red-600 mb-4">{error}</p>
              <button 
                onClick={() => router.replace('/signin')}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
              >
                Return to Sign In
              </button>
            </div>
          </>
        ) : (
          <>
            <div className="text-center">
              <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
            </div>
          </>
        )}
      </div>
    </div>
  );
}