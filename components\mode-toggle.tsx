'use client';

import { useTheme } from 'next-themes';
import * as React from 'react';
import { Icons } from '@/components/icons';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

export function ModeToggle() {
  const { setTheme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="size-8 px-0" size="sm" variant="ghost">
          <Icons.sun className="rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Icons.moon className="absolute rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme('light')}>
          <Icons.sun className="mr-2 size-4" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme('dark')}>
          <Icons.moon className="mr-2 size-4" />
          <span>Dark</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 'use client'

// import { useTheme } from 'next-themes'
// import * as React from 'react'
// import { Icons } from '@/components/icons'
// import { Button } from '@/components/ui/button'

// export function ModeToggle() {
//   const { theme, setTheme } = useTheme()

//   const toggleTheme = () => {
//     setTheme(theme === 'light' ? 'dark' : 'light')
//   }

//   return (
//     <Button className="size-8 px-0" size="sm" variant="ghost" onClick={toggleTheme}>
//       {theme === 'light' ? (
//         <Icons.sun className="rotate-0 scale-100 transition-all" />
//       ) : (
//         <Icons.moon className="rotate-0 scale-100 transition-all" />
//       )}
//       <span className="sr-only">Toggle theme</span>
//     </Button>
//   )
// }
