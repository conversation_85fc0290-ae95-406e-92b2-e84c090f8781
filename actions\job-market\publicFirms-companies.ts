'use server';

import { createClient as createServerClient } from '@/utils/supabase/server';

interface PublicFirmCompany {
  id: string;
  name: string;
}

/**
 * Get all unique public firm companies
 * @returns Array of unique companies with their IDs and names
 */
export async function getReferralPublicFirmCompanies(): Promise<
  PublicFirmCompany[]
> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('public_firm_companies')
      .select('id, name')
      .eq('live', 'yes')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching public firm companies:', error);
      throw new Error('Failed to fetch public firm companies');
    }

    // Remove duplicates based on company name
    const uniqueCompanies = data.reduce((acc: PublicFirmCompany[], current) => {
      const exists = acc.find((company) => company.name === current.name);
      if (!exists) {
        acc.push({
          id: current.id,
          name: current.name
        });
      }
      return acc;
    }, []);

    return uniqueCompanies;
  } catch (error) {
    console.error('Error in getUniquePublicFirmCompanies:', error);
    throw new Error('Failed to fetch public firm companies');
  }
}

/**
 * Get a single public firm company by ID
 * @param companyId - The ID of the company to retrieve
 * @returns The company information or null if it doesn't exist
 */
export async function getPublicFirmCompanyById(
  companyId: string
): Promise<PublicFirmCompany | null> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('public_firm_companies')
      .select('id, name')
      .eq('id', companyId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Record not found
        return null;
      }
      console.error('Error fetching public firm company:', error);
      throw new Error('Failed to fetch public firm company');
    }

    return data;
  } catch (error) {
    console.error('Error in getPublicFirmCompanyById:', error);
    throw new Error('Failed to fetch public firm company');
  }
}

/**
 * Search public firm companies by name
 * @param searchTerm - The search term to filter companies by name
 * @returns Array of matching companies
 */
export async function searchPublicFirmCompanies(
  searchTerm: string
): Promise<PublicFirmCompany[]> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('public_firm_companies')
      .select('id, name')
      .ilike('name', `%${searchTerm}%`)
      .order('name', { ascending: true });

    if (error) {
      console.error('Error searching public firm companies:', error);
      throw new Error('Failed to search public firm companies');
    }

    // Remove duplicates based on company name
    const uniqueCompanies = data.reduce((acc: PublicFirmCompany[], current) => {
      const exists = acc.find((company) => company.name === current.name);
      if (!exists) {
        acc.push({
          id: current.id,
          name: current.name
        });
      }
      return acc;
    }, []);

    return uniqueCompanies;
  } catch (error) {
    console.error('Error in searchPublicFirmCompanies:', error);
    throw new Error('Failed to search public firm companies');
  }
}
