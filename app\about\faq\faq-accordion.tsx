'use client';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';

interface FaqItem {
  question: string;
  answer: string;
}

interface FaqAccordionProps {
  items: FaqItem[];
}

export default function FaqAccordion({ items }: FaqAccordionProps) {
  return (
    <Accordion type="single" collapsible className="w-full space-y-4">
      {items.map((item, index) => (
        <AccordionItem
          key={index}
          value={`item-${index}`}
          className="bg-white rounded-lg border border-gray-100 px-6 py-4"
        >
          <AccordionTrigger className="text-left text-lg font-medium text-gray-900 hover:text-[#118073]">
            {item.question}
          </AccordionTrigger>
          <AccordionContent>
            <p className="text-gray-700 opacity-90 pt-2">{item.answer}</p>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
}
