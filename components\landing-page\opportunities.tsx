'use client';
import Image from 'next/image';
import { allLogos, scatteredLogos } from '@/config/logos';

export default function Opportunities() {
  return (
    <section className="relative overflow-hidden h-screen w-full flex flex-col items-center justify-center">
      {/* Desktop: Scattered logos with animation - hidden on mobile */}
      <div className="hidden md:block w-full h-full absolute inset-0">
        {scatteredLogos.map((logo) => (
          <div 
            key={logo.alt}
            className={`absolute ${logo.animation} z-0`}
            style={{ 
              width: logo.desktopSize,
              height: Math.floor(logo.desktopSize * 0.4), // maintain aspect ratio
              top: logo.top,
              left: logo.left
            }}
          >
            <Image
              src={logo.src}
              alt={logo.alt}
              width={logo.desktopSize}
              height={Math.floor(logo.desktopSize * 0.4)}
              className="object-contain opacity-80 hover:opacity-100 transition-opacity duration-300"
            />
          </div>
        ))}
      </div>

      {/* Center content */}
      <div className="relative z-10 text-center px-4 md:px-0 max-w-[90%] w-full md:max-w-2xl lg:max-w-3xl mx-auto">
        <div className="p-6 md:p-10">
          <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-2 md:mb-4 text-[#2F2F2F]">
            We Connect You
          </h2>
          <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-2 md:mb-4 text-[#2F2F2F]">
            With The Right
          </h2>
          <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6 md:mb-8 text-[#2F2F2F]">
            Opportunities
          </h2>
          <p className="text-base md:text-lg max-w-xs md:max-w-xl mx-auto text-[#2F2F2F] mb-8 md:mb-0">
            Find jobs at top startups and companies that support international
            talent with visa sponsorship. Focus on roles that fit you, faster
          </p>
        </div>
      </div>

      {/* Mobile: Horizontal grid of logos below the text - hidden on desktop */}
      <div className="md:hidden w-full mt-8 px-4">
        <div className="grid grid-cols-3 gap-x-6 gap-y-8">
          {allLogos.map((logo) => (
            <div 
              key={logo.alt}
              className="flex items-center justify-center"
            >
              <Image
                src={logo.src}
                alt={logo.alt}
                width={65}
                height={26}
                className="object-contain opacity-80 hover:opacity-100 transition-opacity duration-300 w-[65px] h-auto"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
