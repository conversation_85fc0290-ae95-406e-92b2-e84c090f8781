'use server';

import { stripe } from '@/utils/stripe/config';
import { createClient } from '@/utils/supabase/server';

/**
 * Create a Stripe Connect account link for onboarding
 */
export async function createAccountLink(
  accountId: string,
  refreshUrl: string,
  returnUrl: string
): Promise<string> {
  try {
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return accountLink.url;
  } catch (error) {
    console.error('Error creating account link:', error);
    throw new Error('Failed to create account link');
  }
}

/**
 * Get a Stripe Connect account details
 */
export async function getConnectAccount(accountId: string) {
  try {
    const account = await stripe.accounts.retrieve(accountId);
    return account;
  } catch (error) {
    console.error('Error retrieving Stripe account:', error);
    throw new Error('Failed to retrieve Stripe account');
  }
}

/**
 * Check if a Stripe Connect account is fully onboarded
 */
export async function isAccountOnboarded(accountId: string): Promise<boolean> {
  try {
    const account = await stripe.accounts.retrieve(accountId);
    
    // Check if the account is fully onboarded
    return (
      account.details_submitted &&
      account.payouts_enabled &&
      account.capabilities?.transfers === 'active'
    );
  } catch (error) {
    console.error('Error checking account status:', error);
    return false;
  }
}

/**
 * Update a Stripe Connect account status in our database
 */
export async function updateAccountStatus(
  insiderId: string,
  accountId: string
): Promise<void> {
  try {
    console.log(`Updating account status for insider ${insiderId} with Stripe account ${accountId}`);
    const account = await stripe.accounts.retrieve(accountId);
    const supabase = createClient();
    
    let status = 'pending';
    
    if (account.details_submitted && account.payouts_enabled) {
      status = 'active';
      console.log(`Account ${accountId} is fully active with payouts enabled`);
    } else if (account.details_submitted) {
      status = 'restricted';
      console.log(`Account ${accountId} has details submitted but payouts not enabled`);
    } else {
      console.log(`Account ${accountId} is pending, details not yet submitted`);
    }
    
    // First, check if the record exists
    const { data: existingAccount } = await supabase
      .from('insider_payment_accounts')
      .select('id, account_status')
      .eq('id', insiderId)
      .eq('stripe_account_id', accountId)
      .single();
    
    if (existingAccount) {
      console.log(`Found existing payment account record, current status: ${existingAccount.account_status}`);
      
      // Only update if status has changed
      if (existingAccount.account_status !== status) {
        const { error } = await supabase
          .from('insider_payment_accounts')
          .update({
            account_status: status,
            updated_at: new Date().toISOString()
          })
          .eq('id', insiderId)
          .eq('stripe_account_id', accountId);
          
        if (error) {
          console.error('Error updating insider_payment_accounts:', error);
          throw new Error(`Failed to update account status: ${error.message}`);
        }
        
        console.log(`Successfully updated account status to ${status}`);
      } else {
        console.log(`Account status already set to ${status}, no update needed`);
      }
    } else {
      console.log(`No existing payment account record found, creating new one`);
      
      // Create new record
      const { error } = await supabase
        .from('insider_payment_accounts')
        .upsert({
          id: insiderId,
          stripe_account_id: accountId,
          account_status: status,
          payment_method: 'stripe',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        
      if (error) {
        console.error('Error creating insider_payment_accounts record:', error);
        throw new Error(`Failed to create account record: ${error.message}`);
      }
      
      console.log(`Successfully created new payment account record with status ${status}`);
    }
  } catch (error) {
    console.error('Error updating account status:', error);
    throw new Error('Failed to update account status');
  }
}

/**
 * Create a login link for a Stripe Connect account
 */
export async function createLoginLink(accountId: string): Promise<string> {
  try {
    const loginLink = await stripe.accounts.createLoginLink(accountId);
    return loginLink.url;
  } catch (error) {
    console.error('Error creating login link:', error);
    throw new Error('Failed to create login link');
  }
}
