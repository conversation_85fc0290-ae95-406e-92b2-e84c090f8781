"use client"

import { useUser } from "@/hooks/useUser"
import { PricingCard } from "@/components/membership/pricing-card"
import { SubscriptionFaq } from "@/components/membership/subscription-faq"
import { Button } from "@/components/ui/button"
import { ComparisonTable } from "@/components/membership/comparison-table"
import { useRouter } from "next/navigation"

interface PricingPlan {
  name: string
  price: string
  period: "monthly" | "annual" | "semi-annual" | "one-time"
  description: string
  features: readonly string[]
  ctaText: string
  popular?: boolean
}

const pricingPlans: readonly PricingPlan[] = [
  {
    name: "Essential Access Pass",
    price: "$59.99",
    period: "semi-annual",
    description: "For active job seekers",
    features: [
      "Job alerts from 100+ platforms",
      "Expert career tips from 6+ years experience",
      "Direct apply—90% faster",
      "H1B-focused job filters",
    ],
    ctaText: "Subscribe",
  },
  {
    name: "Pro Monthly Pass",
    price: "$39.99",
    period: "monthly",
    description: "For ambitious professionals",
    features: [
      "Application guidance",
      "Priority referrals",
      "Internship & OPT support",
      "All Semi-Annual Pass features",
    ],
    ctaText: "Subscribe",
  },
  {
    name: "Elite Semi-Annual Pass",
    price: "$129.99",
    period: "semi-annual",
    description: "For growth-focused professionals",
    features: [
      "FAANG and startup referrals",
      "Weekly curated job lists for diverse roles",
      "AI-powered career tools",
      "Daily jobs from 100+ platforms",
    ],
    ctaText: "Subscribe",
  },
  {
    name: "Ultimate Annual Pass",
    price: "$199.99",
    period: "annual",
    description: "For committed career builders",
    features: [
      "All Monthly Pass features",
      "Boost LinkedIn connections",
      "Real-world capstone projects",
      "Exclusive startup jobs",
    ],
    ctaText: "Subscribe",
  },
] as const

export default function MembershipPage() {
  const { user, loading } = useUser()
  const router = useRouter()

  // Optional: Redirect if user is not logged in
  // Uncomment this if you want to enforce login before viewing the page
  /*
  useEffect(() => {
    if (!loading && !user) {
      router.push('/signin');
    }
  }, [user, loading, router]);
  */

  return (
    <main className="min-h-screen bg-white">
      <section className="relative py-20 overflow-hidden bg-primary/5">
        <div className="container px-4 mx-auto text-center md:px-6">
          <div className="max-w-3xl mx-auto">
            <h1 className="mb-6 text-4xl font-bold tracking-tight md:text-5xl lg:text-6xl">
              Take Charge of Your Career—Start for Free
            </h1>
            <p className="mb-8 text-lg text-gray-600">
              Access interview prep materials, join quarterly career events, and get daily FAANG job opportunities—all
              at no cost.
            </p>
            <Button size="lg" className="px-8 py-6 text-lg">
              Start For Free
            </Button>
          </div>
        </div>

        <div className="container px-4 mx-auto mt-16 text-center md:px-6">
          <h2 className="mb-4 text-3xl font-bold">Choose The Perfect Plan</h2>
          <p className="max-w-2xl mx-auto text-lg text-gray-600">
            Explore our membership options and find the one that fits your career goals. From job alerts to expert
            guidance, we've got a plan tailored for you.
          </p>
        </div>
      </section>

      <section className="py-20">
        <div className="container px-4 mx-auto md:px-6">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {pricingPlans.map((plan) => (
              <PricingCard key={plan.name} {...plan} user={user} loading={loading} />
            ))}
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-50">
        <div className="container px-4 mx-auto md:px-6">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="mb-4 text-3xl font-bold">See What Each Plan Offers</h2>
            <p className="text-gray-600">A closer look at the benefits included in every membership.</p>
          </div>
          <ComparisonTable plans={pricingPlans} user={user} loading={loading} />
        </div>
      </section>

      <SubscriptionFaq />
    </main>
  )
}

