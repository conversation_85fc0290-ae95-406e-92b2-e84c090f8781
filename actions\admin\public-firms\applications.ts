// Applications
"use server"

import { createClient } from "@/utils/supabase/server"
import { v4 as uuidv4 } from "uuid"
import { PublicFirmApplication } from "@/types/public-firms"

export async function getPublicFirmApplications(page = 1, limit = 20): Promise<{ data: PublicFirmApplication[], count: number | null }> {
    const supabase = createClient()
    const offset = (page - 1) * limit
  
    const { data, error, count } = await supabase
      .from("public_firm_applications")
      .select("*", { count: "exact" })
      .order("creation_date", { ascending: false })
      .range(offset, offset + limit - 1)
  
    if (error) {
      console.error("Error fetching public firm applications:", error)
      throw new Error(`Failed to fetch applications: ${error.message}`)
    }
  
    return { data, count }
  }
  
  export async function getPublicFirmApplication(id: string): Promise<PublicFirmApplication> {
    const supabase = createClient()
  
    const { data, error } = await supabase.from("public_firm_applications").select("*").eq("id", id).single()
  
    if (error) {
      console.error("Error fetching public firm application:", error)
      throw new Error(`Failed to fetch application: ${error.message}`)
    }
  
    return data
  }
  
  export async function createPublicFirmApplication(applicationData: Omit<PublicFirmApplication, "id" | "creation_date" | "modified_date">): Promise<PublicFirmApplication> {
    const supabase = createClient()
  
    const newApplication: PublicFirmApplication = {
      id: uuidv4(),
      ...applicationData,
      incomplete: "no",
      status: "Pending",
      creation_date: new Date().toISOString(),
      modified_date: new Date().toISOString(),
    }
  
    const { data, error } = await supabase.from("public_firm_applications").insert([newApplication]).select()
  
    if (error) {
      console.error("Error creating public firm application:", error)
      throw new Error(`Failed to create application: ${error.message}`)
    }
  
    return data[0]
  }
  
  export async function updateApplicationStatus(id: string, status: string): Promise<PublicFirmApplication> {
    const supabase = createClient()
  
    const { data, error } = await supabase
      .from("public_firm_applications")
      .update({
        status,
        modified_date: new Date().toISOString(),
      })
      .eq("id", id)
      .select()
  
    if (error) {
      console.error("Error updating application status:", error)
      throw new Error(`Failed to update application status: ${error.message}`)
    }
  
    return data[0]
  }
  
  export async function deletePublicFirmApplication(id: string): Promise<{ success: boolean }> {
    const supabase = createClient()
  
    const { error } = await supabase.from("public_firm_applications").delete().eq("id", id)
  
    if (error) {
      console.error("Error deleting public firm application:", error)
      throw new Error(`Failed to delete application: ${error.message}`)
    }
  
    return { success: true }
  }