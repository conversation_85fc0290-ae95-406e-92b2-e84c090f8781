import { createClient as createServerClient } from '@/utils/supabase/server';
import { Insider } from '@/types/insider';

/**
 * Get all insiders
 */
export async function getInsiders() {
  const supabase = createServerClient();
  const { data, error } = await supabase
    .from('insiders')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

/**
 * Get insider by id
 */
export async function getInsiderById(id: string) {
  const supabase = createServerClient();
  const { data, error } = await supabase
    .from('insiders')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}

/**
 * Create insider
 */
export async function createInsider(insider: Insider) {
  const supabase = createServerClient();
  const { data, error } = await supabase
    .from('insiders')
    .insert(insider)
    .select()
    .single();

  if (error) {
    throw new Error(error.message);
  }

  return data;
}
