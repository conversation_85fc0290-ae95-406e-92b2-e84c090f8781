# InternUp Website Documentation

## Table of Contents

- [InternUp Website Documentation](#internup-website-documentation)
  - [Table of Contents](#table-of-contents)
  - [Introduction](#introduction)
  - [Tech Stack](#tech-stack)
    - [Core Technologies](#core-technologies)
    - [UI and Styling](#ui-and-styling)
    - [Data Management](#data-management)
    - [Payment Processing](#payment-processing)
    - [Documentation](#documentation)
    - [Form Handling](#form-handling)
    - [Development Tools](#development-tools)
  - [Project Structure](#project-structure)
  - [Routing Architecture](#routing-architecture)
    - [Route Groups](#route-groups)
  - [Components Organization](#components-organization)
  - [Authentication](#authentication)
  - [State Management](#state-management)
  - [Styling](#styling)
  - [Third-party Integrations](#third-party-integrations)
  - [Deployment](#deployment)

## Introduction

InternUp is a platform designed to help international students find quality internships and jobs in their dream companies. The website provides various features including job listings, company profiles, user profiles, and administrative functionalities.

## Tech Stack

The InternUp website is built using the following technologies:

### Core Technologies

- **Next.js 14**: Using the App Router architecture for server-side rendering and routing
- **React 18**: For building the user interface
- **TypeScript**: For type-safe code
- **Supabase**: For database, authentication, and storage

### UI and Styling

- **Tailwind CSS**: For utility-first styling
- **Radix UI**: For accessible UI components
- **Framer Motion**: For animations
- **Lucide React**: For icons
- **Tailwind Merge & Class Variance Authority**: For managing conditional classes
- **Embla Carousel**: For carousel components

### Data Management

- **React Query (Tanstack Query)**: For data fetching and caching
- **Zod**: For schema validation
- **SWR**: For data fetching, caching, and revalidation

### Payment Processing

- **Stripe**: For payment processing and subscription management

### Documentation

- **Fumadocs**: For documentation UI and MDX support

### Form Handling

- **React Hook Form**: For form state management and validation

### Development Tools

- **ESLint**: For code linting
- **Prettier**: For code formatting

## Project Structure

The project follows Next.js 14 App Router structure with the following main directories:

```
/
├── actions/                # Server actions for data mutations
│   ├── admin/              # Admin-specific actions
│   ├── company/            # Company-specific actions
│   └── job-market/         # Job market-related actions
├── app/                    # Next.js App Router directory
│   ├── (auth_forms)/       # Authentication-related routes
│   ├── (dashboard)/        # Dashboard routes
│   ├── (job Market)/       # Job market routes
│   ├── (marketing)/        # Marketing/public pages
│   ├── (user)/             # User-specific routes
│   ├── about/              # About pages
│   ├── api/                # API routes
│   ├── auth/               # Auth-related API routes
│   ├── blogs/              # Blog pages
│   ├── lib/                # App-specific libraries
│   └── membership/         # Membership-related pages
├── assets/                 # Static assets
│   ├── fonts/              # Custom fonts
│   └── svg/                # SVG assets
├── components/             # Reusable React components
│   ├── admin/              # Admin-specific components
│   ├── company/            # Company-specific components
│   ├── docs/               # Documentation components
│   ├── insider-portal/     # Insider portal components
│   ├── job-market/         # Job market components
│   ├── landing-page/       # Landing page components
│   ├── magicui/            # UI utility components
│   ├── membership/         # Membership components
│   ├── sidebar/            # Sidebar components
│   ├── subscription/       # Subscription components
│   └── ui/                 # Shared UI components
├── config/                 # Configuration files
├── content/                # Content files (blogs, docs)
│   ├── blog/               # Blog content
│   └── docs/               # Documentation content
├── hooks/                  # Custom React hooks
├── lib/                    # Utility libraries
├── middleware/             # Next.js middleware
├── public/                 # Public assets
├── server/                 # Server-side code
│   └── api/                # Server API handlers
├── styles/                 # Global styles
├── supabase/               # Supabase configuration
│   └── migrations/         # Database migrations
├── trpc/                   # tRPC setup
├── types/                  # TypeScript type definitions
└── utils/                  # Utility functions
    ├── auth-helpers/       # Authentication helpers
    ├── bubble/             # Bubble UI helpers
    ├── db/                 # Database helpers
    └── stripe/             # Stripe integration helpers
```

## Routing Architecture

The website uses Next.js 14 App Router with route groups for organizing different sections of the application:

### Route Groups

- **`(auth_forms)`**: Authentication-related pages

  - `/signin`: Sign-in page
  - `/signup`: Sign-up page
  - `/forgot-password`: Password recovery
  - `/magic_link`: Magic link authentication
  - `/onboarding`: User onboarding flows
    - `/onboarding/candidate`: Candidate onboarding
    - `/onboarding/company`: Company onboarding

- **`(dashboard)`**: Dashboard pages

  - `/dashboard`: Main dashboard
  - `/dashboard/account`: Account settings
  - `/dashboard/settings`: User settings

- **`(job Market)`**: Job market pages

  - `/capstone`: Capstone projects
  - `/daily-job`: Daily job listings
  - `/public-firms`: Public firms listings
  - `/startups`: Startup listings

- **`(marketing)`**: Public marketing pages

  - `/`: Home page
  - Other marketing pages

- **`(user)`**: User-specific pages

  - `/admin`: Admin portal
  - `/candidate`: Candidate portal
  - `/company`: Company portal
  - `/insider`: Insider portal

- **`about`**: About pages

  - `/about/community`: Community information
  - `/about/contact`: Contact information
  - `/about/faq`: Frequently asked questions
  - `/about/mission`: Mission statement

- **`api`**: API endpoints

  - `/api/admin`: Admin API endpoints
  - `/api/auth`: Authentication endpoints
  - `/api/cron`: Scheduled tasks
  - `/api/insider`: Insider-related endpoints
  - `/api/search`: Search functionality
  - `/api/webhooks`: Webhook handlers

- **`blogs`**: Blog pages

  - `/blogs/[id]`: Individual blog posts

- **`membership`**: Membership-related pages

## Components Organization

Components are organized by feature and functionality:

- **`admin`**: Admin-specific components
- **`company`**: Company-specific components
- **`docs`**: Documentation components
- **`insider-portal`**: Insider portal components
- **`job-market`**: Job market components
- **`landing-page`**: Landing page components
- **`magicui`**: UI utility components
- **`membership`**: Membership components
- **`sidebar`**: Sidebar components
- **`subscription`**: Subscription components
- **`ui`**: Shared UI components (buttons, inputs, modals, etc.)

## Authentication

Authentication is handled through Supabase Auth with the following features:

- Email/password authentication
- Magic link authentication
- OAuth providers (if configured)
- Password reset functionality
- User onboarding flows for different user types

## State Management

The application uses a combination of state management approaches:

- **React Query/SWR**: For server state management
- **React Context**: For global UI state
- **React Hook Form**: For form state
- **tRPC**: For type-safe API calls

## Styling

The website uses a combination of styling approaches:

- **Tailwind CSS**: For utility-first styling
- **CSS Modules**: For component-specific styling
- **Radix UI**: For accessible UI components
- **Tailwind Merge & Class Variance Authority**: For conditional class management

## Third-party Integrations

The website integrates with several third-party services:

- **Stripe**: For payment processing and subscription management
- **Supabase**: For database, authentication, and storage
- **Vercel Analytics**: For website analytics
- **Chatbot**: For user support

## Deployment

The application is designed to be deployed on Vercel, which provides:

- Continuous deployment
- Serverless functions
- Edge functions
- Preview deployments
- Analytics
