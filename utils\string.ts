import { User } from '@/types/user';

export function getUserInitials(user: User): string {
  const firstName = user.first_name || '';
  const lastName = user.last_name || '';
  const preferredName = user.preferred_name || '';

  if (preferredName) {
    return preferredName.charAt(0).toUpperCase();
  }

  if (firstName && lastName) {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  }

  if (firstName) {
    return firstName.charAt(0).toUpperCase();
  }

  if (lastName) {
    return lastName.charAt(0).toUpperCase();
  }

  return 'U';
}
