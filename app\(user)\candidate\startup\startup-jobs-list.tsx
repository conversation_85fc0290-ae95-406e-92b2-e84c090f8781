'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import {
  getAllStartupJobs,
  getStartupFilterOptions
} from '@/actions/job-market/startups';
import type { StartupJob, JobFilters } from '@/types/startups';
import JobCard from '@/components/job-market/job-card';
import JobFiltersComponent from '@/components/job-market/job-filters';
import Pagination from '@/components/job-market/pagination';
import JobListSkeleton from '@/components/job-market/job-list-skeleton';
import { useUser } from '@/hooks/useUser';
import { getApplicationsForUser } from '@/actions/job-market/startup-applications';

interface StartupJobsListProps {
  currentPage: number;
  pageSize: number;
  dateFilter: string;
  filters: JobFilters;
  searchParams: Record<string, string | undefined>;
}

export default function StartupJobsList({
  currentPage,
  pageSize,
  dateFilter,
  filters,
  searchParams
}: StartupJobsListProps) {
  const [jobs, setJobs] = useState<StartupJob[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [loading, setLoading] = useState(true);
  const [filterOptions, setFilterOptions] = useState({
    locations: [] as string[],
    sponsorships: [] as string[],
    terms: [] as string[]
  });
  const { toast } = useToast();
  const { user, loading: userLoading } = useUser();
  const [appliedJobIds, setAppliedJobIds] = useState<Set<string>>(new Set());

  // Fetch filter options
  useEffect(() => {
    const fetchFilterOptions = async () => {
      try {
        const options = await getStartupFilterOptions();
        setFilterOptions({
          locations: options.locations || [],
          sponsorships: options.sponsorships || [],
          terms: options.terms || []
        });
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load filter options. Please try again.',
          variant: 'destructive'
        });
      }
    };

    fetchFilterOptions();
  }, [toast]);

  // Get all applications for the user
  useEffect(() => {
    const fetchApplications = async () => {
      if (user?.email) {
        try {
          const applications = await getApplicationsForUser(user.email);
          // Store just the Set of applied job IDs
          setAppliedJobIds(
            new Set(
              applications
                .map((app) => app.job)
                .filter((job): job is string => Boolean(job))
            )
          );
        } catch (error) {
          console.error('Error fetching user applications:', error);
        }
      }
    };

    if (!userLoading && user) {
      fetchApplications();
    }
  }, [user, userLoading]);

  // Fetch jobs and apply the application status
  useEffect(() => {
    const fetchJobs = async () => {
      setLoading(true);
      try {
        // Only include non-empty filter values
        const cleanedFilters = Object.fromEntries(
          Object.entries(filters).filter(
            ([_, value]) => value && value !== 'all'
          )
        );

        const jobsData = await getAllStartupJobs(
          currentPage,
          pageSize,
          dateFilter,
          cleanedFilters
        );

        // Apply the application status to jobs
        const jobsWithApplicationStatus = jobsData.data.map((job) => ({
          ...job,
          hasApplied: appliedJobIds.has(job.id)
        }));

        setJobs(jobsWithApplicationStatus);
        setTotalCount(jobsData.count || 0);
        setTotalPages(jobsData.totalPages);
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Failed to load jobs. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    if (!userLoading) {
      fetchJobs();
    }
  }, [
    currentPage,
    pageSize,
    dateFilter,
    filters,
    toast,
    userLoading,
    appliedJobIds // Only depends on the Set of IDs, not the full jobs array
  ]);

  if (loading || userLoading) {
    return <JobListSkeleton count={5} />;
  }

  return (
    <>
      <Card className="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl font-semibold text-gray-900">
            Available Positions
          </h2>
          <span className="text-gray-600 font-medium">
            {totalCount} jobs found
          </span>
        </div>

        <JobFiltersComponent
          showSearch={true}
          searchPlaceholder="Search job title..."
          filterOptions={{
            terms: filterOptions.terms || [],
            locations: filterOptions.locations || [],
            sponsorships: filterOptions.sponsorships || []
          }}
          initialValues={{
            search: searchParams.title, // This maps the 'title' URL param to the 'search' field
            term: searchParams.term,
            location: searchParams.location,
            sponsorship: searchParams.sponsorship
          }}
          baseUrl="/candidate/startup"
        />
      </Card>

      {jobs.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow">
          <p className="text-gray-600">No jobs found matching your criteria.</p>
        </div>
      ) : (
        <>
          <div className="space-y-6">
            {jobs.map((job) => (
              <JobCard
                key={job.id}
                id={job.id}
                title={job.title || ''}
                companyName={job.startup_company?.name || ''}
                companyLogo={job.startup_company?.logo_url || null}
                location={job.location}
                term={job.work_type}
                salary={job.salary}
                sponsorship={job.sponsorship}
                createdAt={job.created_at || new Date().toISOString()}
                detailsLink={`/candidate/startup/${job.id}`}
                applyLink={job.job_link || ''}
                showBookmark={true}
                company={job.startup_company?.id}
                hasApplied={job.hasApplied || false}
                jobType="startup"
                hideEasyApply={true}
              />
            ))}
          </div>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            baseUrl="/candidate/startup"
            filters={{
              title: searchParams.title,
              location: searchParams.location,
              sponsorship: searchParams.sponsorship,
              term: searchParams.term,
              date: searchParams.date
            }}
          />
        </>
      )}
    </>
  );
}
