'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ProgressIndicator } from '../components/progress-indicator';
import { useOnboarding } from '../components/onboarding-provider';

export default function CompletePage() {
  const router = useRouter();
  const [countdown, setCountdown] = useState(5);
  const { markStepAsCompleted } = useOnboarding();

  // Define steps for progress indicator
  const steps = [
    '/onboarding/candidate/personal-info',
    '/onboarding/candidate/job-preferences',
    '/onboarding/candidate/about-you',
    '/onboarding/candidate/complete'
  ];

  useEffect(() => {
    // Mark this step as completed
    markStepAsCompleted('/onboarding/candidate/complete');

    // Set a timer to redirect to home page
    const timer = setTimeout(() => {
      router.push('/');
    }, 5000);

    // Countdown timer
    const countdownInterval = setInterval(() => {
      setCountdown((prev) => (prev > 1 ? prev - 1 : 1));
    }, 1000);

    // Clean up timers
    return () => {
      clearTimeout(timer);
      clearInterval(countdownInterval);
    };
  }, [router, markStepAsCompleted]);

  const handleGoToHome = () => {
    router.push('/');
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header with progress indicator */}
      <ProgressIndicator steps={steps} />

      <div className="flex flex-1 p-6 md:p-12">
        {/* Left section with logo */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center pr-12">
          <div className="flex flex-col items-center gap-8">
            <div className="flex items-center space-x-2">
              <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
                <Image
                  src="/favicon_new.png"
                  alt="logo"
                  width={25}
                  height={25}
                />
              </div>
              <span className="text-lg md:text-xl font-extrabold tracking-tightest">
                InternUp
              </span>
            </div>
            <div className="flex flex-col items-center gap-2 text-center">
              <h2 className="text-xl font-semibold">Connecting</h2>
              <h2 className="text-xl font-semibold">
                International <span className="text-[#36BA98]">Talent</span>
              </h2>
              <h2 className="text-xl font-semibold">
                with <span className="text-[#36BA98]">Opportunities</span>
              </h2>
            </div>
          </div>
        </div>

        {/* Right section with completion message */}
        <div className="w-full lg:w-1/2 flex items-center justify-center">
          <div className="max-w-md mx-auto text-center">
            <div className="flex justify-center mb-8">
              <div className="h-24 w-24 bg-green-100 rounded-full flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-12 w-12 text-green-600"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>

            <h1 className="text-3xl font-bold mb-4">
              Your Profile is Complete!
            </h1>
            <p className="text-gray-700 mb-8">
              Thank you for completing your profile. You're now ready to explore
              job opportunities and connect with employers.
            </p>

            <div className="space-y-4">
              <div className="text-sm text-gray-500">
                Redirecting to home page in{' '}
                <span className="font-semibold">{countdown}</span> seconds...
              </div>
              <Button
                onClick={handleGoToHome}
                className="bg-[#36BA98] hover:bg-[#2da888] text-white px-12 py-2 rounded-xl"
              >
                Go to Home Page Now
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
