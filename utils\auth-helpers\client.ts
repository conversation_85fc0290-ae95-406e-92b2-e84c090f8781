'use client';

import { createClient } from '@/utils/supabase/client';
import { type Provider } from '@supabase/supabase-js';
import { getURL } from '@/utils/helpers';
import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';

interface RequestResult {
  type: string;
  title: string;
  description: string;
  redirectPath?: string;
}

type RequestFunction = (formData: FormData) => Promise<RequestResult>;

export async function handleRequest(
  e: React.FormEvent<HTMLFormElement>,
  requestFunc: RequestFunction,
  router: AppRouterInstance | null = null
): Promise<{
  success: boolean;
  result?: RequestResult;
  error?: Error;
}> {
  e.preventDefault();
  const formData = new FormData(e.currentTarget);

  try {
    const result = await requestFunc(formData);
    
    // Handle redirection if a path is provided and router exists
    if (result.redirectPath && router) {
      router.push(result.redirectPath);
    }

    return {
      success: true,
      result
    };
  } catch (error) {
    console.error('Error during request:', error);
    return {
      success: false,
      error: error instanceof Error ? error : new Error('An unknown error occurred')
    };
  }
}
export async function signInWithOAuth(e: React.FormEvent<HTMLFormElement>) {
  console.log('Signing in with Oauth');
  // Prevent default form submission refresh
  e.preventDefault();
  const formData = new FormData(e.currentTarget);
  const provider = String(formData.get('provider')).trim() as Provider;

  console.log('signinwithoauth Form Data', formData);

  console.log('this is the provider');

  // Create client-side supabase client and call signInWithOAuth
  const supabase = createClient();
  const redirectURL = getURL('/auth/callback');
  await supabase.auth.signInWithOAuth({
    provider: provider,
    options: {
      redirectTo: redirectURL
    }
  });
}
