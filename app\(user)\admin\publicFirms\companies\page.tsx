'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { PencilIcon, XCircleIcon, SearchIcon } from 'lucide-react';
import {
  getPublicFirmCompanies,
  deletePublicFirmCompany,
  toggleCompanyLiveStatus
} from '@/actions/admin/public-firms/companies';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { PublicFirmCompany } from '@/types/public-firms';
import CompanyFormDialog from './company-form';

export default function PublicFirmCompaniesPage() {
  const [companies, setCompanies] = useState<PublicFirmCompany[]>([]);
  const [filteredCompanies, setFilteredCompanies] = useState<
    PublicFirmCompany[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editCompany, setEditCompany] = useState<PublicFirmCompany | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState('');
  const pageSize = 100;

  useEffect(() => {
    fetchCompanies();
  }, [currentPage]);

  const fetchCompanies = async () => {
    setIsLoading(true);
    try {
      const { data, count } = await getPublicFirmCompanies(
        currentPage,
        pageSize
      );
      setCompanies(data || []);
      setFilteredCompanies(data || []);
      setTotalCount(count || 0);
      setTotalPages(Math.ceil((count || 0) / pageSize));
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    if (!searchTerm.trim()) {
      setFilteredCompanies(companies);
      return;
    }

    const filtered = companies.filter(
      (company) =>
        company.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.industry?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.location?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    setFilteredCompanies(filtered);
  };

  const handleDeleteCompany = async (id: string) => {
    if (confirm('Are you sure you want to delete this company?')) {
      try {
        await deletePublicFirmCompany(id);
        // Update local state
        setCompanies(companies.filter((company) => company.id !== id));
        setFilteredCompanies(
          filteredCompanies.filter((company) => company.id !== id)
        );
      } catch (error) {
        console.error('Error deleting company:', error);
      }
    }
  };

  const handleToggleLive = async (id: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'yes' ? 'no' : 'yes';
      await toggleCompanyLiveStatus(id, newStatus);

      // Update local state
      const updateCompanyStatus = (companyList: PublicFirmCompany[]) =>
        companyList.map((company) =>
          company.id === id ? { ...company, live: newStatus } : company
        );

      setCompanies(updateCompanyStatus(companies));
      setFilteredCompanies(updateCompanyStatus(filteredCompanies));
    } catch (error) {
      console.error('Error toggling company status:', error);
    }
  };

  const handleEditCompany = (company: PublicFirmCompany) => {
    setEditCompany(company);
    setIsDialogOpen(true);
  };

  const handleCompanySaved = () => {
    setIsDialogOpen(false);
    setEditCompany(null);
    fetchCompanies();
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-2">Public Firm Companies</h1>
      <p className="text-gray-600 mb-6">
        Here are all posted public firm companies
      </p>

      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-gray-500">
          Showing {filteredCompanies.length} of {totalCount} companies
        </p>

        <div className="flex gap-4">
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="Search company"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-xs"
            />
            <Button onClick={handleSearch} variant="outline">
              <SearchIcon className="h-4 w-4 mr-2" /> Search
            </Button>
          </div>
          <Button
            onClick={() => {
              setEditCompany(null);
              setIsDialogOpen(true);
            }}
          >
            Create
          </Button>
        </div>
      </div>

      <ScrollArea className="h-[600px] w-full rounded-md border">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50 sticky top-0">
              <th className="p-3 text-left font-medium text-gray-500">
                Avatar
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Company
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Industry
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Location
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Insider
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Applied
              </th>
              <th className="p-3 text-left font-medium text-gray-500">
                Posted
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Live</th>
              <th className="p-3 text-left font-medium text-gray-500">
                Delete
              </th>
              <th className="p-3 text-left font-medium text-gray-500">Edit</th>
            </tr>
          </thead>
          <tbody className="divide-y">
            {isLoading ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : filteredCompanies.length === 0 ? (
              <tr>
                <td colSpan={10} className="p-4 text-center">
                  No companies found
                </td>
              </tr>
            ) : (
              filteredCompanies.map((company) => (
                <tr key={company.id} className="border-b">
                  <td className="p-3">
                    {company.logo ? (
                      <div className="w-10 h-10 relative">
                        <Image
                          src={company.logo}
                          alt={company.name || 'Company logo'}
                          width={40}
                          height={40}
                          className="object-contain rounded"
                          unoptimized
                        />
                      </div>
                    ) : (
                      <div className="w-10 h-10 bg-gray-200 rounded flex items-center justify-center">
                        <span className="text-xs text-gray-500">
                          {company.name?.charAt(0) || '?'}
                        </span>
                      </div>
                    )}
                  </td>
                  <td className="p-3">{company.name || 'N/A'}</td>
                  <td className="p-3">{company.industry || 'N/A'}</td>
                  <td className="p-3">{company.location || 'N/A'}</td>
                  <td className="p-3">{company.insider || 'No'}</td>
                  <td className="p-3">{company.applied?.length || 0}</td>
                  <td className="p-3">
                    {company.creation_date
                      ? new Date(company.creation_date).toLocaleDateString(
                          'en-US',
                          {
                            month: 'short',
                            day: '2-digit',
                            year: '2-digit'
                          }
                        )
                      : 'N/A'}
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() =>
                        handleToggleLive(company.id, company.live || 'no')
                      }
                      className={`w-12 h-6 rounded-full flex items-center transition-colors duration-300 ${
                        company.live === 'yes'
                          ? 'bg-[#118073] justify-end'
                          : 'bg-gray-300 justify-start'
                      }`}
                    >
                      <span
                        className={`w-5 h-5 rounded-full mx-0.5 ${
                          company.live === 'yes' ? 'bg-white' : 'bg-gray-500'
                        }`}
                      ></span>
                    </button>
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleDeleteCompany(company.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <XCircleIcon className="h-5 w-5" />
                    </button>
                  </td>
                  <td className="p-3">
                    <button
                      onClick={() => handleEditCompany(company)}
                      className="text-[#118073] hover:text-green-700"
                    >
                      <PencilIcon className="h-5 w-5" />
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </ScrollArea>

      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1 || isLoading}
            >
              Previous
            </Button>
            <span className="flex items-center px-4">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages || isLoading}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      <CompanyFormDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        company={editCompany}
        onSave={handleCompanySaved}
      />
    </div>
  );
}
