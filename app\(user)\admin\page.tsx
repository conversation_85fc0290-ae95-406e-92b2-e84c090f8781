'use client';
import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// import { MagicButton } from '@/components/magicui/';

export default function AdminPage() {
  const supabase = createClient();
  const [data, setData] = useState({
    startupJobs: 0,
    publicJobs: 0,
    startupApplications: 0,
    publicApplications: 0,
    startupCompanies: 0,
    publicCompanies: 0
  });

  useEffect(() => {
    const fetchData = async () => {
      let { count: startupJobs } = await supabase
        .from('startup_jobs')
        .select('*', { count: 'exact' });

      let { count: publicJobs } = await supabase
        .from('public_firm_jobs')
        .select('*', { count: 'exact' });

      let { count: startupApplications } = await supabase
        .from('startup_applications')
        .select('*', { count: 'exact' });

      let { count: publicApplications } = await supabase
        .from('public_firm_applications')
        .select('*', { count: 'exact' });

      let { count: startupCompanies } = await supabase
        .from('startup_companies')
        .select('*', { count: 'exact' });

      let { count: publicCompanies } = await supabase
        .from('public_firm_companies')
        .select('*', { count: 'exact' });

      setData({
        startupJobs: startupJobs || 0,
        publicJobs: publicJobs || 0,
        startupApplications: startupApplications || 0,
        publicApplications: publicApplications || 0,
        startupCompanies: startupCompanies || 0,
        publicCompanies: publicCompanies || 0
      });
    };

    fetchData();
  }, []);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-4">Admin Dashboard</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Jobs</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Startup Jobs: {data.startupJobs}</p>
            <p>Public Jobs: {data.publicJobs}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Applications</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Startup Applications: {data.startupApplications}</p>
            <p>Public Applications: {data.publicApplications}</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>Companies</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Startup Companies: {data.startupCompanies}</p>
            <p>Public Companies: {data.publicCompanies}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
