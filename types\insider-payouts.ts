export interface InsiderPaymentAccount {
  id: string;
  stripe_account_id: string | null;
  account_status: string;
  payment_method: string;
  created_at: string;
  updated_at: string;
}

export interface InsiderPayoutSettings {
  id: string;
  payout_method: 'manual' | 'automatic';
  payout_threshold: number;
  payout_frequency: 'weekly' | 'monthly' | 'quarterly';
  created_at: string;
  updated_at: string;
}

export interface InsiderPayout {
  id: string;
  insider_id: string;
  amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  payout_method: string;
  stripe_payout_id: string | null;
  stripe_transfer_id: string | null;
  expected_arrival_date: string | null;
  arrival_date: string | null;
  description: string | null;
  metadata: any | null;
  created_at: string;
  updated_at: string;
}

export interface InsiderPayoutFormData {
  insider_id: string;
  amount: number;
  payout_method: string;
  description?: string;
}

export interface InsiderBulkPayoutFormData {
  payout_method: string;
  description?: string;
}

export interface InsiderPaymentAccountFormData {
  payment_method: string;
  stripe_account_details?: any;
}

export interface InsiderPayoutSettingsFormData {
  payout_method: 'manual' | 'automatic';
  payout_threshold: number;
  payout_frequency: 'weekly' | 'monthly' | 'quarterly';
}

export interface InsiderBalanceAdjustmentFormData {
  insider_id: string;
  amount: number;
  description?: string;
}

export interface InsiderWithPayoutInfo extends InsiderPaymentAccount {
  first_name: string | null;
  last_name: string | null;
  user_email: string | null;
  earnings_available: number;
  earnings_paid: number;
  earnings_pending: number;
}
