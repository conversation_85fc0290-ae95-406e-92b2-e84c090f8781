// app/(user)/insider/layout.tsx
'use client';

import { DynamicSidebar } from '@/components/sidebar/dynamic-sidebar';
import { UserNavigation } from '@/components/user-navigation';
import { useUser } from '@/hooks/useUser';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';

export default function InsiderLayout({
  children
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useUser();
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    if (!loading && user && user.user_type.toLowerCase() !== "insider") {
      toast({
        title: "Access denied",
        description: "You don't have access to the insider area",
        variant: "destructive",
      });
      router.push("/signin");
    }
  }, [user, loading, router, toast]);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (!user || user.user_type.toLowerCase() !== "insider") {
    return null;
  }

  return (
    <div className="flex min-h-screen">
      <DynamicSidebar role="insider" />
      <div className="flex-1 flex flex-col ml-[270px]">
        <UserNavigation 
          userType="insider" 
          userName={user?.first_name || 'Insider'} 
          userImage={user?.avatar_url} 
        />
        <main className="flex-1 overflow-y-auto p-8">{children}</main>
      </div>
    </div>
  );
}
