import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/utils/stripe/config';

export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const body = await req.json();
    const { accountId } = body;

    if (!accountId) {
      return NextResponse.json({ error: 'Missing account ID' }, { status: 400 });
    }

    // Create login link
    const loginLink = await stripe.accounts.createLoginLink(accountId);

    return NextResponse.json({ url: loginLink.url });
  } catch (error) {
    console.error('Error creating Stripe login link:', error);
    return NextResponse.json(
      { error: 'Failed to create Stripe login link' },
      { status: 500 }
    );
  }
}
