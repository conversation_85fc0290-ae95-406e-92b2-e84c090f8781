/**
 * Utility functions to handle type conversions between null and undefined
 * This helps resolve TypeScript issues when database fields are nullable
 * but components expect undefined for optional props
 */

/**
 * Converts null to undefined, leaves other values unchanged
 * Useful for converting database nullable fields to component-friendly types
 */
export function nullToUndefined<T>(value: T | null): T | undefined {
  return value === null ? undefined : value;
}

/**
 * Converts undefined to null, leaves other values unchanged
 * Useful for converting component values back to database-friendly types
 */
export function undefinedToNull<T>(value: T | undefined): T | null {
  return value === undefined ? null : value;
}

/**
 * Type-safe way to handle nullable string fields for component props
 * Returns undefined if value is null or empty string
 */
export function safeString(value: string | null | undefined): string | undefined {
  return value === null || value === undefined || value === '' ? undefined : value;
}

/**
 * Type-safe way to handle nullable array fields for component props
 * Returns undefined if value is null
 */
export function safeArray<T>(value: T[] | null | undefined): T[] | undefined {
  return value === null || value === undefined ? undefined : value;
}
