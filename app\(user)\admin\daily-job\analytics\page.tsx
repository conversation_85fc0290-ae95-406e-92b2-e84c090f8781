import { getStatisticsEnabledJobs } from '@/actions/admin/web-crawler';
import AnalyticsDashboard from './analytics-dashboard';

export const metadata = {
  title: 'Web Crawler Analytics',
  description: 'Download statistics for daily job files'
};

export default async function AnalyticsPage() {
  // Fetch only jobs that have statistics enabled (created on or after May 11th, 2025)
  const statisticsEnabledJobs = await getStatisticsEnabledJobs();

  return (
    <div className="container mx-auto py-8 px-4 overflow-hidden">
      <h1 className="text-3xl font-bold mb-2">Web Crawler Analytics</h1>
      <p className="mb-6">Download statistics for daily job files</p>
      <div className="text-sm text-gray-500 mb-6">
        Note: Statistics are only available for files created on or after May
        11th, 2025
      </div>

      <AnalyticsDashboard initialJobs={statisticsEnabledJobs} />
    </div>
  );
}
