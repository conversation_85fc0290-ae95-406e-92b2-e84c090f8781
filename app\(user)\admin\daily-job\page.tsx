import { getDailyJobs } from '@/actions/admin/web-crawler';
import { DailyJobsList } from './daily-jobs-list';

export const metadata = {
  title: 'Web Crawler Update Management',
  description: 'Manage web crawler updates'
};

export default async function WebCrawlerPage() {
  const dailyJobs = await getDailyJobs();

  return (
    <div className="container mx-auto py-8 px-4 overflow-hidden">
      <h1 className="text-3xl font-bold mb-2">Web Crawler Update Management</h1>
      <p className="mb-6">Here are all daily excels</p>
      <DailyJobsList initialJobs={dailyJobs} />
    </div>
  );
}
