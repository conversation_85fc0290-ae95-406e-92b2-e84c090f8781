import { type JobDetailRow } from '@/types/google-sheets';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { ExternalLink } from 'lucide-react';
import { cn } from '@/lib/utils';
import { isInternPosition } from './JobDetailDialog';
import { useUser } from '@/hooks/useUser';
import { trackJobClick } from '@/utils/job-tracking';

type JobListingTableProps = {
  columns: Array<{ key: keyof JobDetailRow; header: string }>;
  rows: JobDetailRow[];
  onRowClick: (job: JobDetailRow) => void;
  fileId?: string; // Add fileId prop to track which file the jobs belong to
};

export const JobListingTable = ({
  columns,
  rows,
  onRowClick,
  fileId
}: JobListingTableProps) => {
  const { user } = useUser();

  // Handle direct apply link click with tracking
  const handleApplyClick = async (e: React.MouseEvent, job: JobDetailRow) => {
    e.stopPropagation(); // Prevent row click when clicking the link

    if (user && fileId) {
      try {
        // Track the direct application click
        await trackJobClick(fileId, user.id, {
          keyword: job.Keyword,
          jobTitle: job.Job_title,
          company: job.Company
        });
      } catch (error) {
        console.error('Error tracking direct apply click:', error);
        // Continue with the application even if tracking fails
      }
    }

    // Open the apply link in a new tab
    if (
      job.Apply_links &&
      typeof job.Apply_links === 'string' &&
      job.Apply_links.startsWith('http')
    ) {
      window.open(job.Apply_links, '_blank');
    }
  };

  return (
    <div className="rounded-md border overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader className="sticky top-0 bg-background z-10">
            <TableRow>
              {columns.map((col) => (
                <TableHead key={col.key} className="whitespace-nowrap">
                  {col.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.length > 0 ? (
              rows.map((row, rowIndex) => (
                <TableRow
                  key={`row-${rowIndex}`}
                  className={cn(
                    isInternPosition(row.Job_title) &&
                      'bg-blue-50 hover:bg-blue-100',
                    'cursor-pointer transition-colors'
                  )}
                  onClick={() => onRowClick(row)}
                >
                  {columns.map((col) => (
                    <TableCell
                      key={`${col.key}-${rowIndex}`}
                      className="max-w-md truncate"
                    >
                      {/* Render Apply_links as a link if it's a URL */}
                      {col.key === 'Apply_links' &&
                      typeof row[col.key] === 'string' &&
                      row[col.key]?.startsWith('http') ? (
                        <button
                          type="button"
                          onClick={(e) => handleApplyClick(e, row)}
                          className="text-primary underline hover:text-primary/80 flex items-center gap-1"
                        >
                          Apply <ExternalLink className="h-3 w-3" />
                        </button>
                      ) : col.key === 'Job_title' &&
                        isInternPosition(row.Job_title) ? (
                        <span
                          title={(row[col.key] as string) || '-'}
                          className="font-medium text-blue-700"
                        >
                          {row[col.key] || '-'}
                        </span>
                      ) : (
                        <span title={(row[col.key] as string) || '-'}>
                          {row[col.key] || '-'}
                        </span>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
