// app/auth/callback/page.tsx
'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { Loader2 } from 'lucide-react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleCallback = async () => {
      const supabase = createClient();

      try {
        // Get the user data
        const {
          data: { user },
          error
        } = await supabase.auth.getUser();

        if (error || !user) {
          throw error || new Error('No user found');
        }
        
        // Check if this is a migrating user that needs to set password
        // A user needs to set password if they are:
        // 1. Using email provider (not OAuth)
        // 2. Don't have a password set
        
        // Check the auth method - current session will show if user signed in with email
        const { data: sessionData } = await supabase.auth.getSession();
        const session = sessionData?.session;
        const isEmailProvider = session?.user?.app_metadata?.provider === 'email';
        
        // Check if user has set a password previously
        // If they don't have a password set, they need to create one during migration
        // Factors are typically stored in user.factors for new Supabase accounts
        const hasPasswordFactors = !!user.factors || 
                                   user.app_metadata?.has_password === true;
        
        // If user has signed in with email but doesn't have password factor,
        // redirect them to set a password as part of migration flow
        if (isEmailProvider && !hasPasswordFactors && user.email) {
          console.log('[Client] User needs to set password during migration');
          router.push(`/auth/set-password?email=${encodeURIComponent(user.email)}`);
          return;
        }

        // Single query to check if user exists and get user_type
        const { data: existingUser, error: userQueryError } = await supabase
          .from('users')
          .select('id, user_type')
          .eq('email', user.email)
          .maybeSingle();
          
        if (userQueryError) {
          console.error('Error querying user:', userQueryError);
          throw userQueryError;
        }

        let userType = 'candidate';
        
        if (!existingUser) {
          // Create new user record with default user_type from metadata
          const defaultUserType = user.user_metadata?.user_type?.toLowerCase() || 'candidate';
          
          await supabase.from('users').upsert({
            id: user.id,
            email: user.email,
            user_type: defaultUserType,
            verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });
          
          // Use the default user type for redirection
          userType = defaultUserType;
        } else {
          // Use existing user's user_type from the database
          userType = (existingUser.user_type || 'candidate').toLowerCase();
        }
        
        console.log('[Client] User data:', user);
        console.log('[Client] User type from database:', userType);
        
        // Determine redirect path based on user type
        let redirectPath = '/dashboard';
        switch (userType) {
          case 'candidate':
            redirectPath = '/candidate';
            break;
          case 'company':
            redirectPath = '/company';
            break;
          case 'insider':
            redirectPath = '/insider';
            break;
          case 'admin':
            redirectPath = '/admin';
            break;
        }

        // Redirect to the appropriate dashboard
        router.push(redirectPath);
      } catch (error) {
        console.error('Error in auth callback:', error);
        router.push('/signin?error=Could not authenticate user');
      }
    };

    handleCallback();
  }, [router]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
    </div>
  );
}