// app/auth/callback/page.tsx
'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { Loader2 } from 'lucide-react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleCallback = async () => {
      const supabase = createClient();

      try {
        // Get the user data
        const {
          data: { user },
          error
        } = await supabase.auth.getUser();

        if (error || !user) {
          throw error || new Error('No user found');
        }
        
        // Check if this is a migrating user that needs to set password
        // IMPORTANT: Only redirect to set-password for magic link users, NEVER OAuth users

        // Get current session to check the authentication method for THIS login
        const { data: sessionData } = await supabase.auth.getSession();
        const session = sessionData?.session;

        // Check the current login method by examining the session's authentication
        // OAuth users will have identities with providers like 'google', 'linkedin', etc.
        // Magic link users will have 'email' as their identity provider
        const currentIdentity = session?.user?.identities?.[0];
        const currentProvider = currentIdentity?.provider;

        console.log('[Client] Current login provider:', currentProvider);
        console.log('[Client] User identities:', session?.user?.identities);

        // Only check for password setup if user logged in via email/magic link
        // NEVER redirect OAuth users (google, linkedin, etc.) to set-password
        const isCurrentlyUsingEmail = currentProvider === 'email';
        const isOAuthLogin = currentProvider && ['google', 'linkedin', 'github', 'facebook'].includes(currentProvider);

        if (isCurrentlyUsingEmail && !isOAuthLogin) {
          // Check if user has set a password previously for email logins
          const hasPasswordFactors = !!user.factors ||
                                     user.app_metadata?.has_password === true ||
                                     user.user_metadata?.has_set_password === true;

          // Only redirect to set password if they used email and don't have a password
          if (!hasPasswordFactors && user.email) {
            console.log('[Client] Email user needs to set password during migration');
            router.push(`/auth/set-password?email=${encodeURIComponent(user.email)}`);
            return;
          }
        } else if (isOAuthLogin) {
          console.log('[Client] OAuth user - skipping password setup');
        }

        // Single query to check if user exists and get user_type
        const { data: existingUser, error: userQueryError } = await supabase
          .from('users')
          .select('id, user_type')
          .eq('email', user.email)
          .maybeSingle();

        if (userQueryError) {
          console.error('Error querying user:', userQueryError);
          throw userQueryError;
        }

        let userType = 'candidate';
        
        if (!existingUser) {
          // Create new user record with default user_type from metadata
          const defaultUserType = user.user_metadata?.user_type?.toLowerCase() || 'candidate';

          await supabase.from('users').upsert({
            id: user.id,
            email: user.email,
            user_type: defaultUserType,
            verified: true,
            migration_status: 'migrated', // All new users are considered migrated
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

          // Use the default user type for redirection
          userType = defaultUserType;
        } else {
          // Use existing user's user_type from the database
          userType = (existingUser.user_type || 'candidate').toLowerCase();

          // If this is an OAuth login for an existing user, mark them as migrated
          if (isOAuthLogin) {
            await supabase.from('users').update({
              migration_status: 'migrated',
              updated_at: new Date().toISOString()
            }).eq('id', user.id);
          }
        }
        
        console.log('[Client] User data:', user);
        console.log('[Client] User type from database:', userType);
        
        // Determine redirect path based on user type
        let redirectPath = '/dashboard';
        switch (userType) {
          case 'candidate':
            redirectPath = '/candidate';
            break;
          case 'company':
            redirectPath = '/company';
            break;
          case 'insider':
            redirectPath = '/insider';
            break;
          case 'admin':
            redirectPath = '/admin';
            break;
        }

        // Redirect to the appropriate dashboard
        router.push(redirectPath);
      } catch (error) {
        console.error('Error in auth callback:', error);
        router.push('/signin?error=Could not authenticate user');
      }
    };

    handleCallback();
  }, [router]);

  return (
    <div className="flex min-h-screen items-center justify-center">
      <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
    </div>
  );
}