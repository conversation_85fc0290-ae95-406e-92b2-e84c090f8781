import { Metadata } from 'next';

// Metadata configuration for membership pages
export const metadata: Metadata = {
  title: {
    default: 'InternUp Membership - Unlock Premium Features',
    template: '%s | InternUp Membership'
  },
  description:
    'Join InternUp Membership to access premium features, exclusive job opportunities, and personalized career guidance. Take your career journey to the next level with our comprehensive membership benefits.',
  keywords: [
    'membership',
    'premium features',
    'career guidance',
    'exclusive opportunities',
    'job search premium',
    'career development',
    'professional networking',
    'career coaching',
    'premium resources',
    'membership benefits'
  ],
  openGraph: {
    type: 'website',
    locale: 'en_US',
    // url: siteConfig.url, // Uncomment when siteConfig is available
    title: 'InternUp Membership',
    description:
      'Premium features and exclusive opportunities for international students',
    siteName: 'InternUp'
    // images: [
    //   {
    //     url: '/membership-og.jpg',
    //     width: 1200,
    //     height: 630,
    //     alt: 'InternUp Membership Platform'
    //   }
    // ]
  },
  twitter: {
    card: 'summary_large_image',
    title: 'InternUp Membership',
    description:
      'Premium features and exclusive opportunities for international students'
    // images: ['/membership-twitter.jpg']
  },
  alternates: {
    // canonical: `${siteConfig.url}/membership` // Uncomment when siteConfig is available
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-snippet': -1,
      'max-image-preview': 'large',
      'max-video-preview': -1
    }
  },
  category: 'membership',
  classification: 'premium services'
};
