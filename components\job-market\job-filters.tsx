'use client';

import type React from 'react';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export interface FilterOption {
  value: string;
  label: string;
}

export interface JobFiltersProps {
  searchPlaceholder?: string;
  showSearch?: boolean;
  showActiveToggle?: boolean;
  filterOptions: {
    terms?: FilterOption[] | string[];
    locations?: FilterOption[] | string[];
    sponsorships?: FilterOption[] | string[];
    [key: string]: FilterOption[] | string[] | undefined;
  };
  initialValues?: {
    search?: string;
    term?: string;
    location?: string;
    sponsorship?: string;
    active?: boolean;
    [key: string]: string | boolean | undefined;
  };
  onFilter?: (filters: Record<string, any>) => void;
  baseUrl: string;
}

export default function JobFilters({
  searchPlaceholder = 'Search jobs...',
  showSearch = true,
  showActiveToggle = false,
  filterOptions,
  initialValues = {},
  onFilter,
  baseUrl
}: JobFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [filters, setFilters] = useState({
    search: initialValues.search || '',
    term: initialValues.term || '',
    location: initialValues.location || '',
    sponsorship: initialValues.sponsorship || '',
    active: initialValues.active || false
  });

  // Format filter options to ensure they're in the correct format
  const formatOptions = (
    options: FilterOption[] | string[] | undefined
  ): FilterOption[] => {
    if (!options) return [];

    return options.map((option) => {
      if (typeof option === 'string') {
        return { value: option, label: option };
      }
      return option;
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters({ ...filters, [e.target.name]: e.target.value });
  };

  const handleSelectChange = (name: string, value: string) => {
    setFilters({
      ...filters,
      [name]: value === 'all' ? '' : value
    });
  };

  const handleToggleChange = (checked: boolean) => {
    setFilters({ ...filters, active: checked });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (onFilter) {
      onFilter(filters);
      return;
    }

    // Build URL with filters
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value && key !== 'active') {
        // Use 'search' parameter directly, don't map to 'title'
        params.set(key, value.toString());
      }
    });

    params.set('active', filters.active ? 'true' : 'false');
    router.push(`${baseUrl}?${params.toString()}`);
  };

  const handleClear = () => {
    setFilters({
      search: '',
      term: '',
      location: '',
      sponsorship: '',
      active: false
    });

    router.push(baseUrl);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        {showSearch && (
          <Input
            name="search"
            placeholder={searchPlaceholder}
            value={filters.search}
            onChange={handleInputChange}
            className="flex-1"
          />
        )}

        {filterOptions.terms && filterOptions.terms.length > 0 && (
          <Select
            value={filters.term || 'all'}
            onValueChange={(value) => handleSelectChange('term', value)}
          >
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="Job Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Job Types</SelectItem>
              {formatOptions(filterOptions.terms).map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {filterOptions.locations && filterOptions.locations.length > 0 && (
          <Select
            value={filters.location}
            onValueChange={(value) => handleSelectChange('location', value)}
          >
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="Location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Locations</SelectItem>
              {formatOptions(filterOptions.locations).map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}

        {filterOptions.sponsorships &&
          filterOptions.sponsorships.length > 0 && (
            <Select
              value={filters.sponsorship}
              onValueChange={(value) =>
                handleSelectChange('sponsorship', value)
              }
            >
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Sponsorship" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sponsorships</SelectItem>
                {formatOptions(filterOptions.sponsorships).map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
      </div>

      <div className="flex items-center justify-between">
        {showActiveToggle && (
          <div className="flex items-center gap-2">
            <Label htmlFor="active-toggle" className="text-sm font-medium">
              Active Only
            </Label>
            <Switch
              id="active-toggle"
              checked={filters.active}
              onCheckedChange={handleToggleChange}
            />
          </div>
        )}

        <div className="flex gap-2 ml-auto">
          <Button type="button" variant="outline" onClick={handleClear}>
            Clear
          </Button>
          <Button type="submit">Apply Filters</Button>
        </div>
      </div>
    </form>
  );
}
