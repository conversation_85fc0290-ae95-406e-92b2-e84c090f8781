'use client';

import React, { useState, useEffect, useRef } from 'react';
import { createJob, updateJob } from '@/actions/admin/startup/jobs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/components/ui/use-toast';
import type {
  StartupJob,
  StartupCompany,
  StartupJobFormData
} from '@/types/startups';
import {
  TERM_OPTIONS,
  SPONSORSHIP_OPTIONS,
  LOCATION_OPTIONS,
  MIN_INTERNSHIP_DURATION_OPTIONS,
  MIN_WEEKLY_HOURS_OPTIONS,
  WORK_TYPE_OPTIONS
} from '@/config/startup-job';

import { getCompanies } from '@/actions/admin/startup/companies';
import { v4 as uuidv4 } from 'uuid';
interface JobFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  job: StartupJob | null;
  onSave: () => void;
}

export default function JobFormDialog({
  open,
  onOpenChange,
  job,
  onSave
}: JobFormDialogProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState<StartupJobFormData>({
    title: '',
    company_id: '',
    description: '',
    location: '',
    work_type: '',
    term: '',
    salary: '',
    sponsorship: '',
    min_internship_duration: '',
    min_weekly_hours: '',
    job_link: '',
    payment_negotiable: false,
    other_compensation: '',
    in_other_market: false,
    provide_linkedin_endorsement: false,
    live: true,
    required_skills: []
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [companies, setCompanies] = useState<StartupCompany[]>([]);
  const [companySearchTerm, setCompanySearchTerm] = useState('');
  const [companyPage, setCompanyPage] = useState(1);
  const [loadingMoreCompanies, setLoadingMoreCompanies] = useState(false);
  const [hasMoreCompanies, setHasMoreCompanies] = useState(true);
  const companySearchTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (open) {
      fetchCompanies(1, true);
    }
  }, [open]);

  useEffect(() => {
    if (companySearchTimeout.current) {
      clearTimeout(companySearchTimeout.current);
    }

    companySearchTimeout.current = setTimeout(() => {
      fetchCompanies(1, true);
    }, 300);

    return () => {
      if (companySearchTimeout.current) {
        clearTimeout(companySearchTimeout.current);
      }
    };
  }, [companySearchTerm]);

  const fetchCompanies = async (page: number, reset: boolean = false) => {
    try {
      setLoadingMoreCompanies(true);
      const { data, totalPages } = await getCompanies({
        page,
        pageSize: 30,
        searchTerm: companySearchTerm,
        sortBy: 'name',
        sortOrder: 'asc'
      });

      if (reset) {
        setCompanies(data || []);
      } else {
        setCompanies((prev) => [...prev, ...(data || [])]);
      }

      setCompanyPage(page);
      setHasMoreCompanies(page < (totalPages || 1));
    } catch (error) {
      console.error('Error fetching companies:', error);
    } finally {
      setLoadingMoreCompanies(false);
    }
  };

  const handleLoadMoreCompanies = () => {
    if (!loadingMoreCompanies && hasMoreCompanies) {
      fetchCompanies(companyPage + 1);
    }
  };

  // Reset form when dialog opens/closes or job changes
  useEffect(() => {
    if (job) {
      // Populate form with job data for editing
      setFormData({
        title: job.title || '',
        company_id: job.company_id || '',
        description: job.description || '',
        location: job.location || '',
        work_type: job.work_type || '',
        term: job.term || '',
        salary: job.salary || '',
        sponsorship: job.sponsorship || '',
        min_internship_duration: job.min_internship_duration || '',
        min_weekly_hours: job.min_weekly_hours || '',
        job_link: job.job_link || '',
        payment_negotiable: job.payment_negotiable || false,
        other_compensation: job.other_compensation || '',
        in_other_market: job.in_other_market || false,
        provide_linkedin_endorsement: job.provide_linkedin_endorsement || false,
        live: job.live,
        required_skills: job.required_skills || []
      });
    } else {
      // Reset form for new job
      resetForm();
    }
  }, [job, open]);

  const resetForm = () => {
    setFormData({
      id: uuidv4(),
      title: '',
      company_id: '',
      description: '',
      location: '',
      work_type: '',
      term: '',
      salary: '',
      sponsorship: '',
      min_internship_duration: '',
      min_weekly_hours: '',
      job_link: '',
      payment_negotiable: false,
      other_compensation: '',
      in_other_market: false,
      provide_linkedin_endorsement: false,
      live: true,
      required_skills: []
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate required fields
      if (!formData.title || !formData.company_id || !formData.term) {
        console.error('Missing required fields:', {
          title: !formData.title,
          company_id: !formData.company_id,
          term: !formData.term
        });
        toast({
          variant: 'destructive',
          title: 'Error',
          description:
            'Please fill in all required fields: title, company, and term.'
        });
        throw new Error('Please fill in all required fields');
      }

      if (job) {
        // Update existing job
        await updateJob(job.id, formData);
      } else {
        // Create new job
        await createJob(formData);
      }
      toast({
        title: 'Success',
        description: job
          ? 'Job updated successfully'
          : 'Job created successfully'
      });
      onSave();
    } catch (error) {
      console.error('Error saving job:', error);
      // Log more detailed error information
      if (error instanceof Error) {
        console.error('Error details:', error.message);
        toast({
          variant: 'destructive',
          title: 'Error saving job',
          description: error.message || 'An unexpected error occurred'
        });
      } else {
        toast({
          variant: 'destructive',
          title: 'Error saving job',
          description: 'An unexpected error occurred'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{job ? 'Edit' : 'Create'} A Startup Job</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">
                Company <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.company_id}
                onValueChange={(value) =>
                  setFormData({ ...formData, company_id: value })
                }
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select company" />
                </SelectTrigger>
                <SelectContent>
                  <div className="p-2">
                    <Input
                      placeholder="Search companies..."
                      value={companySearchTerm}
                      onChange={(e) => setCompanySearchTerm(e.target.value)}
                      className="mb-2"
                    />
                  </div>
                  <ScrollArea className="h-[200px]">
                    <SelectGroup>
                      {companies.length === 0 && !loadingMoreCompanies && (
                        <div className="p-2 text-center text-sm text-gray-500">
                          No companies found
                        </div>
                      )}
                      {companies.map((company) => (
                        <SelectItem key={company.id} value={company.id}>
                          {company.name || ''}
                        </SelectItem>
                      ))}
                      {loadingMoreCompanies && (
                        <div className="p-2 text-center text-sm text-gray-500">
                          Loading...
                        </div>
                      )}
                      {hasMoreCompanies && !loadingMoreCompanies && (
                        <div
                          className="p-2 text-center text-sm text-blue-500 cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.preventDefault();
                            handleLoadMoreCompanies();
                          }}
                        >
                          Load more...
                        </div>
                      )}
                    </SelectGroup>
                  </ScrollArea>
                </SelectContent>
              </Select>
              <p className="text-sm text-gray-500">
                New company?{' '}
                <a href="/admin/startup/companies" className="text-blue-500">
                  Click Here
                </a>
              </p>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Job Title <span className="text-red-500">*</span>
              </label>
              <Input
                value={formData.title}
                onChange={(e) =>
                  setFormData({ ...formData, title: e.target.value })
                }
                required
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Term <span className="text-red-500">*</span>
              </label>
              <Select
                value={formData.term}
                onValueChange={(value) =>
                  setFormData({ ...formData, term: value })
                }
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select term" />
                </SelectTrigger>
                <SelectContent>
                  {TERM_OPTIONS.map((term) => (
                    <SelectItem key={term} value={term}>
                      {term}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Job link</label>
              <Input
                value={formData.job_link}
                onChange={(e) =>
                  setFormData({ ...formData, job_link: e.target.value })
                }
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Location</label>
              <Select
                value={formData.location}
                onValueChange={(value) =>
                  setFormData({ ...formData, location: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  {LOCATION_OPTIONS.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Work Type</label>
              <Select
                value={formData.work_type}
                onValueChange={(value) =>
                  setFormData({ ...formData, work_type: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select work type" />
                </SelectTrigger>
                <SelectContent>
                  {WORK_TYPE_OPTIONS.map((workType) => (
                    <SelectItem key={workType} value={workType}>
                      {workType}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Sponsorship</label>
              <Select
                value={formData.sponsorship}
                onValueChange={(value) =>
                  setFormData({ ...formData, sponsorship: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select sponsorship" />
                </SelectTrigger>
                <SelectContent>
                  {SPONSORSHIP_OPTIONS.map((sponsorship) => (
                    <SelectItem key={sponsorship} value={sponsorship}>
                      {sponsorship}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Weekly Hours(Intern)
              </label>
              <Select
                value={formData.min_weekly_hours}
                onValueChange={(value) =>
                  setFormData({ ...formData, min_weekly_hours: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select hours" />
                </SelectTrigger>
                <SelectContent>
                  {MIN_WEEKLY_HOURS_OPTIONS.map((hours) => (
                    <SelectItem key={hours} value={hours}>
                      {hours}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Minimum Duration(Intern)
              </label>
              <Select
                value={formData.min_internship_duration}
                onValueChange={(value) =>
                  setFormData({ ...formData, min_internship_duration: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  {MIN_INTERNSHIP_DURATION_OPTIONS.map((duration) => (
                    <SelectItem key={duration} value={duration}>
                      {duration}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Salary</label>
              <Input
                value={formData.salary}
                onChange={(e) =>
                  setFormData({ ...formData, salary: e.target.value })
                }
                placeholder="e.g. $60,000 - $80,000"
              />
            </div>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Description</label>
            <Textarea
              value={formData.description}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              rows={6}
            />
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Checkbox
                id="payment_negotiable"
                checked={formData.payment_negotiable}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    payment_negotiable: checked as boolean
                  })
                }
              />
              <label htmlFor="payment_negotiable" className="text-sm">
                Payment negotiable
              </label>
            </div>

            <div className="flex items-center gap-2">
              <Checkbox
                id="other_compensation"
                checked={!!formData.other_compensation}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    other_compensation: checked ? 'Yes' : ''
                  })
                }
              />
              <label htmlFor="other_compensation" className="text-sm">
                Other monetary compensation
              </label>
            </div>

            <div className="flex items-center gap-2">
              <Checkbox
                id="provide_linkedin_endorsement"
                checked={formData.provide_linkedin_endorsement}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    provide_linkedin_endorsement: checked as boolean
                  })
                }
              />
              <label htmlFor="provide_linkedin_endorsement" className="text-sm">
                Provide LinkedIn Endorsement/Recommendation
              </label>
            </div>

            <div className="flex items-center gap-2">
              <Checkbox
                id="in_other_market"
                checked={formData.in_other_market}
                onCheckedChange={(checked) =>
                  setFormData({
                    ...formData,
                    in_other_market: checked as boolean
                  })
                }
              />
              <label htmlFor="in_other_market" className="text-sm">
                Available in other markets
              </label>
            </div>

            <div className="flex items-center gap-2 mt-4">
              <label htmlFor="live" className="text-sm font-medium">
                Active Job Posting
              </label>
              <Switch
                id="live"
                checked={formData.live}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, live: checked })
                }
              />
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
