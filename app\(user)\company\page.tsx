'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@/hooks/useUser';
import { useToast } from '@/components/ui/use-toast';
import {
  getCompanyDetails,
  updateCompanyDetails,
  createCompanyProfile
} from '@/actions/company/account';
import { CompanyDetailsForm } from '@/components/company/company-details-form';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Pencil } from 'lucide-react';
import type { StartupCompany } from '@/types/startups';
import Image from 'next/image';
import { v4 as uuidv4 } from 'uuid';

export default function CompanyAccountPage() {
  const { user } = useUser();
  const { toast } = useToast();
  const [companyDetails, setCompanyDetails] = useState<StartupCompany | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const fetchCompanyDetails = async () => {
      if (user?.id) {
        try {
          const details = await getCompanyDetails(user.id);
          setCompanyDetails(details);
        } catch (error) {
          console.error('Error fetching company details:', error);
          toast({
            title: 'Error',
            description: 'Failed to load company details',
            variant: 'destructive'
          });
        } finally {
          setLoading(false);
        }
      }
    };

    if (user) {
      fetchCompanyDetails();
    }
  }, [user, toast]);

  const handleUpdateCompany = async (formData: Partial<StartupCompany>) => {
    try {
      if (!user?.id) {
        toast({
          title: 'Error',
          description: 'User not authenticated. Please log in again.',
          variant: 'destructive'
        });
        return;
      }

      const currentCompanyId = companyDetails?.id;

      if (currentCompanyId) {
        // This is an UPDATE operation - use updateCompanyDetails
        await updateCompanyDetails(currentCompanyId, formData);
        toast({
          title: 'Success',
          description: 'Company details updated successfully.'
        });
      } else {
        // This is a CREATE operation - use createCompanyProfile
        // For new companies, we need to generate a UUID
        const companyWithId = {
          ...formData,
          id: uuidv4()
        };

        await createCompanyProfile(user.id, companyWithId);
        toast({
          title: 'Success',
          description: 'Company profile created successfully!'
        });
      }

      // Refresh company details from the server
      const refreshedDetails = await getCompanyDetails(user.id);
      setCompanyDetails(refreshedDetails);
      setIsEditing(false); // Exit edit mode after successful save
    } catch (error: any) {
      const isUpdateAttempt = !!companyDetails?.id; // This helps determine if the error occurred during an update attempt
      console.error(
        `Error ${isUpdateAttempt ? 'updating' : 'creating'} company details:`,
        error
      );
      toast({
        title: 'Error',
        description: `Failed to ${isUpdateAttempt ? 'update' : 'create'} 
          company details. ${error.message || 'Please try again.'}`,
        variant: 'destructive'
      });
    }
  };

  const CompanyDetailsView = ({ company }: { company: StartupCompany }) => {
    return (
      <Card className="overflow-hidden">
        {/* Header Section */}
        <div className="border-b bg-muted/20 p-6">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-semibold">Company Information</h2>
            <Button
              onClick={() => setIsEditing(true)}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
            >
              <Pencil className="h-4 w-4" />
              Edit Details
            </Button>
          </div>
        </div>

        {/* Logo and Basic Info Section */}
        <div className="p-6 border-b">
          <div className="flex gap-6 items-start">
            <div className="relative w-24 h-24 rounded-lg border bg-muted/20 overflow-hidden flex-shrink-0">
              {company.logo_url ? (
                <Image
                  src={company.logo_url}
                  alt={company.name || 'Company Logo'}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-3xl font-bold text-muted">
                  {company.name?.[0] || '?'}
                </div>
              )}
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-semibold mb-2">
                {company.name || 'Not provided'}
              </h3>
              <p className="text-muted-foreground">
                {company.description || 'No company description provided.'}
              </p>
            </div>
          </div>
        </div>

        {/* Main Details Section */}
        <div className="p-6 grid gap-6">
          {/* Basic Information */}
          <div className="grid gap-6">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <InfoItem label="Industry" value={company.industry} />
              <InfoItem
                label="Year of Incorporation"
                value={company.year_of_incorporation}
              />
              <InfoItem
                label="Legal Status"
                value={
                  company.legally_incorporated
                    ? 'Incorporated'
                    : 'Not incorporated'
                }
              />
              <InfoItem label="Funding Status" value={company.funding} />
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid gap-6">
            <h3 className="text-lg font-semibold">Contact Information</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <InfoItem label="Contact Email" value={company.contact_email} />
              <InfoItem label="Internal Email" value={company.internal_email} />
            </div>
          </div>

          {/* Online Presence */}
          <div className="grid gap-6">
            <h3 className="text-lg font-semibold">Online Presence</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <InfoItem
                label="Company Website"
                value={company.company_website}
                isLink
              />
              <InfoItem
                label="LinkedIn"
                value={company.company_linkedin}
                isLink
              />
              <InfoItem
                label="Crunchbase"
                value={company.company_crunchbase}
                isLink
              />
              <InfoItem
                label="Founder LinkedIn"
                value={company.founder_linkedin}
                isLink
              />
            </div>
          </div>

          {/* Additional Settings */}
          <div className="grid gap-6">
            <h3 className="text-lg font-semibold">Additional Settings</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <InfoItem
                label="Resume Parsing"
                value={company.parse_resumes_to_csv ? 'Enabled' : 'Disabled'}
                type="status"
              />
              <InfoItem
                label="Reverse Hiring"
                value={company.open_for_reverse_hiring ? 'Open' : 'Closed'}
                type="status"
              />
              <InfoItem
                label="Sponsorship"
                value={company.sponsorship}
                type="status"
              />
            </div>
          </div>
        </div>
      </Card>
    );
  };

  // Helper component for displaying information items
  const InfoItem = ({
    label,
    value,
    isLink = false,
    type = 'text'
  }: {
    label: string;
    value?: string | null;
    isLink?: boolean;
    type?: 'text' | 'status';
  }) => {
    if (!value) {
      return (
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{label}</p>
          <p className="text-sm text-muted">Not provided</p>
        </div>
      );
    }

    if (isLink) {
      return (
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{label}</p>
          <a
            href={value.startsWith('http') ? value : `https://${value}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-sm text-primary hover:underline"
          >
            {value}
          </a>
        </div>
      );
    }

    if (type === 'status') {
      return (
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{label}</p>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                value === 'Enabled' || value === 'Open' || value === 'yes'
                  ? 'bg-green-500'
                  : value === 'case_by_case'
                    ? 'bg-yellow-500'
                    : 'bg-red-500'
              }`}
            />
            <p className="text-sm">{value}</p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-1">
        <p className="text-sm font-medium text-muted-foreground">{label}</p>
        <p className="text-sm">{value}</p>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-4xl">
      <h1 className="mb-2 text-3xl font-bold">Account</h1>
      <p className="mb-8 text-muted-foreground">Manage your company details.</p>

      {companyDetails ? (
        isEditing ? (
          <Card className="p-6">
            <CompanyDetailsForm
              initialData={companyDetails}
              onSubmit={handleUpdateCompany}
              onCancel={() => setIsEditing(false)}
            />
          </Card>
        ) : (
          <CompanyDetailsView company={companyDetails} />
        )
      ) : (
        <Card className="p-6">
          <h2 className="mb-4 text-xl font-semibold">
            Please add your company details
          </h2>
          <CompanyDetailsForm
            initialData={{} as StartupCompany}
            onSubmit={handleUpdateCompany}
            isNewCompany={true}
          />
        </Card>
      )}
    </div>
  );
}
