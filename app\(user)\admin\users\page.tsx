"use client"

import * as React from "react"
import { Search } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { getUsers, updateUserStatus } from "@/actions/admin/users"
import type { User, UserFilters } from "@/types/user"
import { useState, useCallback, useEffect } from "react"
import { EditSubscriptionDialog } from "@/components/admin/edit-subscription-dialog"
import { Badge } from "@/components/ui/badge"

export default function UserTable() {
  const [users, setUsers] = useState<User[]>([])
  const [filters, setFilters] = useState<UserFilters>({})
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [searchTerm, setSearchTerm] = useState("")
  const [showExpiredVip, setShowExpiredVip] = useState(false)

  // Debounce function to delay search
  const debounce = (func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout
    return function(...args: any[]) {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true)
      const { data, count } = await getUsers(filters, page)
      setUsers(data)
      setTotalUsers(count || 0)
    } catch (error) {
      console.error("Failed to fetch users:", error)
    } finally {
      setLoading(false)
    }
  }, [filters, page])

  // Apply search term to filters with debounce
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setFilters((prev) => ({ ...prev, searchQuery: value }))
      setPage(1) // Reset to first page when searching
    }, 500),
    []
  )

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    debouncedSearch(value)
  }

  const handleExpiredVipFilter = () => {
    const newValue = !showExpiredVip
    setShowExpiredVip(newValue)
    setFilters(prev => ({ ...prev, expiredVipSubscriptions: newValue }))
    setPage(1) // Reset to first page when applying filter
  }

  const handleStatusUpdate = async (
    userId: string,
    updates: { verified?: boolean; vip?: boolean; has_suspended_capstone?: boolean },
  ) => {
    try {
      await updateUserStatus(userId, updates)
      fetchUsers() // Refresh the list
    } catch (error) {
      console.error("Failed to update user:", error)
    }
  }

  const totalPages = Math.ceil(totalUsers / 20)

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  return (
    <div className="w-full max-w-6xl mx-auto p-6 h-screen flex flex-col">
      <div className="space-y-1">
        <h1 className="text-2xl font-semibold">Users</h1>
        {/* <p className="text-sm">
          Here are some <span className="text-red-500">Club Members</span> need to verify. Total :{totalUsers}
        </p> */}
      </div>

      <div className="mt-8 flex-grow ">
        {/* <p className="text-sm text-muted-foreground mb-4">Here are your latest sign ups</p> */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex gap-4 items-center">
            <p className="text-sm text-muted-foreground">{totalUsers} results</p>
            <Select onValueChange={(value) => setFilters((prev) => ({ ...prev, userType: value }))}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select user type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Insider">Insider</SelectItem>
                <SelectItem value="Candidate">Candidate</SelectItem>
                <SelectItem value="Company">Company</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              variant={showExpiredVip ? "default" : "outline"}
              className={showExpiredVip ? "bg-[#118073] hover:bg-[#0d665b]" : ""}
              onClick={handleExpiredVipFilter}
            >
              <span className="text-xs sm:text-sm">Expired VIP Subscriptions</span>
            </Button>
          </div>
          <div className="flex gap-2">
            <Input
              placeholder="Search for an user"
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-[240px]"
            />
            <Button variant="default" className="bg-[#36BA98]" onClick={fetchUsers}>
              <Search className="h-4 w-4" />
              <span className="ml-2">Search</span>
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <div className="max-h-[600px] overflow-y-auto border-2">
            <Table>
              <TableHeader className="sticky top-0 bg-background z-10">
                <TableRow>
                  <TableHead>Avatar</TableHead>
                  <TableHead>User type</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Email address</TableHead>
                  <TableHead>Verified</TableHead>
                  <TableHead>VIP</TableHead>
                  <TableHead>Suspended</TableHead>
                  <TableHead>Subscription</TableHead>
                  <TableHead>Signed up</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Avatar>
                        <AvatarImage src={user.avatar_url || ""} alt={`${user.first_name} ${user.last_name}`} />
                        <AvatarFallback>{user.first_name?.[0] || user.email[0].toUpperCase()}</AvatarFallback>
                      </Avatar>
                    </TableCell>
                    <TableCell>
                      <Select
                        defaultValue={user.user_type || "Candidate"}
                        onValueChange={(value) =>
                          handleStatusUpdate(user.id, { verified: value === "Insider" || value === "Admin" })
                        }
                      >
                        <SelectTrigger className="w-[120px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Admin">Admin</SelectItem>
                          <SelectItem value="Insider">Insider</SelectItem>
                          <SelectItem value="Candidate">Candidate</SelectItem>
                          <SelectItem value="Company">Company</SelectItem>
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{`${user.first_name || ""} ${user.last_name || ""}`}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Switch
                        checked={user.verified}
                        className="data-[state=checked]:bg-[#118073]"
                        onCheckedChange={(checked) => handleStatusUpdate(user.id, { verified: checked })}
                      />
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={user.vip}
                        className="data-[state=checked]:bg-[#118073]"
                        onCheckedChange={(checked) => handleStatusUpdate(user.id, { vip: checked })}
                      />
                    </TableCell>
                    <TableCell>
                      <Switch
                        checked={user.has_suspended_capstone}
                        className="data-[state=checked]:bg-[#118073]"
                        onCheckedChange={(checked) => handleStatusUpdate(user.id, { has_suspended_capstone: checked })}
                      />
                    </TableCell>
                    <TableCell className="flex items-center gap-2">
                      {user.subscription_type ? (
                        <div className="flex flex-col">
                          <Badge variant="outline" className="mb-1">
                            {user.subscription_type}
                          </Badge>
                          {user.subscription_expiry_date && (
                            <span className="text-xs text-muted-foreground">
                              Expires: {new Date(user.subscription_expiry_date).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">No active subscription</span>
                      )}
                      <EditSubscriptionDialog 
                        userId={user.id}
                        currentSubscriptionType={user.subscription_type}
                        currentExpiryDate={user.subscription_expiry_date}
                        onSuccess={fetchUsers}
                      />
                    </TableCell>
                    <TableCell>{new Date(user.created_at || "").toLocaleDateString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        <div className="flex justify-between  items-center mt-4">
          <Button
            variant="default"
            className="bg-[#36BA98]"
            disabled={page === 1}
            onClick={() => setPage((prev) => Math.max(prev - 1, 1))}
          >
            Previous
          </Button>
          <span className="text-sm">
            Page {page} of {totalPages}
          </span>
          <Button
            variant="default"
            className="bg-[#36BA98]"
            disabled={page === totalPages || totalPages === 0}
            onClick={() => setPage((prev) => Math.min(prev + 1, totalPages))}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
