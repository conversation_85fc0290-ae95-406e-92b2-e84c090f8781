'use client';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';
import Particles from '@/components/magicui/particles';

export default function HeroSection() {

  return (
    <section className="relative w-full overflow-hidden min-h-[90vh]  flex items-center justify-center">
      <div className="absolute inset-0 z-0">
        <Particles
          className="absolute inset-0"
          quantity={1000}
          size={0.5}
          ease={1}
          color={'#04AF87'}
          refresh
        />
      </div>
      <div className="container mt-6 mx-auto px-4 py-12 md:py-16 lg:py-48">
        <div className="relative z-10 flex max-w-[64rem] flex-col items-center gap-4 text-center mx-auto">
          <h1 className="font-heading tracking-tight font-bold text-2xl sm:text-4xl md:text-5xl lg:text-7xl">
          Your Career, Our Mission
          </h1>
          <div className="max-w-[42rem] tracking-tight text-black sm:text-xl sm:leading-8 rounded-full p-2">
          Match visa-sponsored roles, get tailored referrals, and connect with top employers. InternUp makes landing your dream job faster and smarter.
          </div>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/signin" className={cn(buttonVariants({ size: 'xl' }), 'rounded-xl border-2 border-primary bg-primary dark:border-white text-bold text-white hover:border-secondary hover:bg-secondary')}>
              Join Community
            </Link>
            <Link
              href='/membership'
              target="_blank"
              rel="noreferrer"
              className={cn(buttonVariants({ variant: 'outline', size: 'xl' }), 'rounded-xl border-2 border-primary dark:border-white text-bold hover:text-white hover:bg-secondary hover:border-secondary')}
            >
              Become a Member
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
