'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { useToast } from '@/components/ui/use-toast';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Link from 'next/link';
import { ArrowLeft, Loader2 } from 'lucide-react';
import Image from 'next/image';
import Particles from '@/components/magicui/particles';

// Token form schema
const tokenFormSchema = z.object({
  token: z
    .string()
    .length(6, 'Token must be 6 digits')
    .regex(/^\d+$/, 'Token must contain only numbers')
});

// Password form schema
const passwordFormSchema = z.object({
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[0-9]/, 'Must contain a number')
    .regex(/[A-Z]/, 'Must contain an uppercase letter')
});

type TokenFormValues = z.infer<typeof tokenFormSchema>;
type PasswordFormValues = z.infer<typeof passwordFormSchema>;

// Token verification form component
function TokenVerificationForm({
  email,
  onVerified
}: {
  email: string;
  onVerified: () => void;
}) {
  const [isVerifyingToken, setIsVerifyingToken] = useState(false);
  const { toast } = useToast();
  const supabase = createClient();
  const searchParams = useSearchParams();

  const form = useForm<TokenFormValues>({
    resolver: zodResolver(tokenFormSchema),
    defaultValues: {
      token: searchParams.get('token') || ''
    }
  });

  const onSubmit = async (values: TokenFormValues) => {
    if (isVerifyingToken) return;
    setIsVerifyingToken(true);

    try {
      const { error } = await supabase.auth.verifyOtp({
        email,
        token: values.token,
        type: 'recovery'
      });

      if (error) {
        toast({
          variant: 'destructive',
          title: 'Verification Failed',
          description: error.message
        });
      } else {
        onVerified();
        toast({
          title: 'Verified!',
          description: 'You can now set a new password.'
        });
      }
    } catch (error) {
      console.error('Verification error:', error);
      toast({
        variant: 'destructive',
        title: 'Verification Failed',
        description: 'Please check your token and try again.'
      });
    } finally {
      setIsVerifyingToken(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="token"
          render={({ field }) => (
            <FormItem>
              <FormLabel>6-Digit Code</FormLabel>
              <FormControl>
                <Input
                  placeholder="000000"
                  maxLength={6}
                  disabled={isVerifyingToken}
                  className="text-center text-2xl tracking-widest border-muted-foreground/20 focus-visible:ring-[#118073]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full bg-[#118073] hover:bg-[#16a38a] text-white"
          disabled={isVerifyingToken}
        >
          {isVerifyingToken ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Verifying...
            </>
          ) : (
            'Verify Code'
          )}
        </Button>
      </form>
    </Form>
  );
}

// Password reset form component
function PasswordResetForm() {
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const supabase = createClient();

  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      password: ''
    }
  });

  const onSubmit = async (values: PasswordFormValues) => {
    setIsUpdatingPassword(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password: values.password
      });

      if (error) throw error;

      toast({
        title: 'Password Updated',
        description: 'You can now sign in with your new password.'
      });
      router.push('/signin');
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Update Failed',
        description:
          error instanceof Error ? error.message : 'Password update failed.'
      });
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>New Password</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="Enter your new password"
                  autoComplete="new-password"
                  disabled={isUpdatingPassword}
                  className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                  {...field}
                />
              </FormControl>
              <div className="text-sm mt-2 space-y-1 text-muted-foreground">
                <p className={field.value.length >= 6 ? 'text-green-500' : ''}>
                  • At least 6 characters
                </p>
                <p
                  className={/[0-9]/.test(field.value) ? 'text-green-500' : ''}
                >
                  • At least one number
                </p>
                <p
                  className={/[A-Z]/.test(field.value) ? 'text-green-500' : ''}
                >
                  • At least one capital letter
                </p>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full bg-[#118073] hover:bg-[#16a38a] text-white"
          disabled={isUpdatingPassword}
        >
          {isUpdatingPassword ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Updating...
            </>
          ) : (
            'Update Password'
          )}
        </Button>
      </form>
    </Form>
  );
}

// Main page component
export default function ResetPasswordPage() {
  const router = useRouter();
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const searchParams = useSearchParams();
  const email = searchParams.get('email');
  const type = searchParams.get('type');

  if (!email || type !== 'recovery') {
    router.push('/forgot-password');
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex min-h-[100dvh] items-center justify-center">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-[#118073]" />
          <p className="mt-2 text-sm text-muted-foreground">Processing...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-[100dvh] bg-background">
      {/* Left illustration */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Reset Your Password</h1>
            <p className="text-xl">
              {isVerified
                ? 'Create a new password for your account.'
                : 'Enter the 6-digit code from your email.'}
            </p>
          </div>
        </div>

        <div className="relative z-10 text-sm opacity-80">
          {new Date().getFullYear()} InternUp. All rights reserved.
        </div>

        <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40"></div>
        <Particles
          className="absolute inset-0"
          quantity={300}
          size={1}
          ease={1}
          refresh
        />
      </div>

      {/* Right form section */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto">
        <div className="flex items-center justify-between mb-8">
          <Link
            href="/signin"
            className="rounded-md p-2 transition-colors hover:bg-muted"
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back</span>
          </Link>
        </div>

        <div className="max-w-md mx-auto">
          <div className="space-y-6 text-center mb-8">
            <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent">
              {isVerified ? 'Set New Password' : 'Enter Reset Code'}
            </h1>
            <p className="text-muted-foreground">
              {isVerified
                ? 'Create a strong password for your account'
                : 'Check your email for the 6-digit reset code'}
            </p>
          </div>

          {!isVerified ? (
            <TokenVerificationForm
              email={email}
              onVerified={() => setIsVerified(true)}
            />
          ) : (
            <PasswordResetForm />
          )}

          <div className="text-center mt-8">
            <Link
              href="/signin"
              className="text-[#118073] hover:text-[#16a38a] text-sm font-medium hover:underline"
            >
              Back to Sign in
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
