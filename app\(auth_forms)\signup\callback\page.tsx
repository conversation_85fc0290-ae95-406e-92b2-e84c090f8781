'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const next = searchParams.get('next') || '/dashboard';

  useEffect(() => {
    const supabase = createClient();
    supabase.auth
      .getUser()
      .then(({ data: { user } }) => {
        if (!user) {
          router.push('/signin');
          return;
        }

        const userType = user.user_metadata?.user_type?.toLowerCase();
        switch (userType) {
          case 'candidate':
            router.push('/candidate');
            break;
          case 'company':
            router.push('/company');
            break;
          case 'insider':
            router.push('/insider');
            break;
          case 'admin':
            router.push('/admin');
            break;
          default:
            router.push(next);
        }
      })
      .catch(() => router.push('/signin'));
  }, [router, next]);

  return <p className="text-center mt-10">Completing sign-up…</p>;
}
