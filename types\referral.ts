export interface Referral {
  id: string;
  created_at: string;
  requested_company: string;
  job_title: string;
  job_listing_url: string;
  job_id: string;
  candidate_id: string;
  candidate_linkedin: string;
  candidate_phone_number: string;
  candidate_resume: string;
  insider_id?: string;
  status: string;
  notes?: string;
  self_introduction: string;
}

// Optional: Add a type for creating a new referral
export interface CreateReferralInput {
  id?: string;
  requested_company: string;
  job_title: string;
  job_listing_url: string;
  job_id: string;
  candidate_id: string;
  candidate_linkedin: string;
  candidate_phone_number: string;
  candidate_resume: string;
  insider_id?: string;
  status: string;
  self_introduction: string;
}

// Optional: Add a type for updating a referral
export interface UpdateReferralInput extends Partial<CreateReferralInput> {
  id: string;
}

// Add this interface for the API response
export interface ReferralResponse {
  id: string;
  candidate_id: string;
  candidate_name: string;
  candidate_email: string;
  requested_company: string;
  job_title: string;
  job_listing_url: string;
  candidate_resume: string;
  candidate_linkedin: string;
  creation_date: string;
  status: 'pending' | 'assigned' | 'reviewed' | 'accepted' | 'rejected';
  insider_id?: string;
  insider_name?: string;
  insider_email?: string;
  self_introduction?: string;
}

// Add this interface for insider referral management
export interface InsiderReferralResponse {
  id: string;
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  companyName: string;
  jobTitle: string;
  location?: string;
  date: string;
  status: string;
  jobListingUrl: string;
  candidateResume: string;
  candidateLinkedin: string;
  notes?: string;
  candidatePhone?: string;
  selfIntroduction?: string;
}
