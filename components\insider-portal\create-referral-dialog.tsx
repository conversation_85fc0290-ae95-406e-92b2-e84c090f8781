'use client';

import * as React from 'react';
import { Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';

export function CreateReferralDialog() {
  const [files, setFiles] = React.useState<{
    resume?: File;
    additional?: File;
    coverLetter?: File;
  }>({});

  const handleFileChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    type: 'resume' | 'additional' | 'coverLetter'
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setFiles((prev) => ({
        ...prev,
        [type]: file
      }));
    }
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    // Handle form submission here
  };

  const FileUpload = ({
    type,
    label
  }: {
    type: 'resume' | 'additional' | 'coverLetter';
    label: string;
  }) => (
    <div className="space-y-2">
      <Label htmlFor={type}>{label}</Label>
      <Card className="relative">
        <CardContent className="flex items-center justify-between p-3">
          <Input
            id={type}
            type="file"
            className="max-w-[200px]"
            onChange={(e) => handleFileChange(e, type)}
          />
          {files[type] && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => {
                // Handle file preview
              }}
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">Preview {label}</span>
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Create New Referral</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Job Details</DialogTitle>
          <DialogDescription>
            Fill in the details for your referral. Make sure to include all
            required documents.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input
                id="jobTitle"
                placeholder="e.g. Software Engineer"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="jobLink">Job Link</Label>
              <Input id="jobLink" type="url" placeholder="https://..." />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="applicantName">Applicant Name</Label>
              <Input id="applicantName" placeholder="Full name" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="companyName">Company Name</Label>
              <Input id="companyName" placeholder="Company name" required />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input id="location" placeholder="City, Country or Remote" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">Date</Label>
              <Input id="date" type="date" required />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <FileUpload type="resume" label="Resume" />
            <FileUpload type="additional" label="Additional Materials" />
            <FileUpload type="coverLetter" label="Cover Letter" />
          </div>

          <div className="flex justify-end gap-3">
            <DialogTrigger asChild>
              <Button variant="outline">Cancel</Button>
            </DialogTrigger>
            <Button type="submit">Submit Referral</Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
