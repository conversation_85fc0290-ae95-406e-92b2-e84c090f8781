import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { XCircleIcon } from 'lucide-react';
import {
  createCompany,
  updateCompany
} from '@/actions/admin/startup/companies';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { uploadImage } from '@/utils/supabase/storage/client';
import type { StartupCompany, StartupCompanyFormData } from '@/types/startups';

interface CompanyFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  company: StartupCompany | null;
  onSave: () => void;
}

export default function CompanyFormDialog({
  open,
  onOpenChange,
  company,
  onSave
}: CompanyFormDialogProps) {
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [formData, setFormData] = useState<StartupCompanyFormData>({
    name: '',
    description: '',
    industry: '',
    founder_school: '',
    contact_email: '',
    internal_email: '',
    founder_linkedin: '',
    company_crunchbase: '',
    company_linkedin: '',
    company_website: '',
    funding: '',
    legally_incorporated: '',
    logo_url: ''
  });

  // Populate form when editing
  useEffect(() => {
    if (company) {
      setFormData({
        name: company.name || '',
        description: company.description || '',
        industry: company.industry || '',
        founder_school: company.founder_school || '',
        contact_email: company.contact_email || '',
        internal_email: company.internal_email || '',
        founder_linkedin: company.founder_linkedin || '',
        company_crunchbase: company.company_crunchbase || '',
        company_linkedin: company.company_linkedin || '',
        company_website: company.company_website || '',
        funding: company.funding || '',
        legally_incorporated: company.legally_incorporated || '',
        logo_url: company.logo_url || ''
      });

      if (company.logo_url) {
        setLogoPreview(company.logo_url);
      } else {
        setLogoPreview(null);
      }
    } else {
      resetForm();
    }
  }, [company]);

  const handleSaveCompany = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      let logoUrl = formData.logo_url;

      if (logoFile) {
        const folderName = formData.name.toLowerCase().replace(/\s+/g, '-');
        const { imageUrl, error } = await uploadImage({
          file: logoFile,
          bucket: 'company-logos',
          folder: folderName
        });

        if (error) {
          throw new Error(`Failed to upload logo: ${error}`);
        }

        logoUrl = imageUrl;
      }

      const companyData = {
        ...formData,
        logo_url: logoUrl
      };

      if (company) {
        await updateCompany(company.id, companyData);
      } else {
        await createCompany(companyData);
      }

      onSave();
    } catch (error) {
      console.error(
        `Error ${company ? 'updating' : 'creating'} company:`,
        error
      );
    }
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      industry: '',
      founder_school: '',
      contact_email: '',
      internal_email: '',
      founder_linkedin: '',
      company_crunchbase: '',
      company_linkedin: '',
      company_website: '',
      funding: '',
      legally_incorporated: '',
      logo_url: ''
    });
    setLogoFile(null);
    setLogoPreview(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] px-6 max-h-[90vh] overflow-y-auto flex flex-col">
        <DialogHeader className="px-4 pt-4">
          <DialogTitle>
            {company ? 'Edit' : 'Create'} Startup Company
          </DialogTitle>
        </DialogHeader>

        <div className="px-4 overflow-y-auto flex-grow">
          <form
            id="company-form"
            onSubmit={handleSaveCompany}
            className="space-y-4 pb-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2 md:col-span-1">
                <label className="text-sm font-medium">Upload Logo</label>
                <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-4 h-32">
                  {logoPreview ? (
                    <div className="relative w-full h-full">
                      <Image
                        src={logoPreview || '/placeholder.svg'}
                        alt="Logo preview"
                        fill
                        className="object-contain"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setLogoFile(null);
                          setLogoPreview(null);
                          setFormData({ ...formData, logo_url: '' });
                        }}
                        className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1"
                      >
                        <XCircleIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <>
                      <label
                        htmlFor="logo-upload"
                        className="cursor-pointer text-center"
                      >
                        <span className="text-sm text-gray-500">
                          Click to upload
                        </span>
                      </label>
                      <input
                        id="logo-upload"
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleLogoChange}
                      />
                    </>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Company Name</label>
                <Input
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Industry</label>
                <Input
                  value={formData.industry}
                  onChange={(e) =>
                    setFormData({ ...formData, industry: e.target.value })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Founder School</label>
                <Input
                  value={formData.founder_school}
                  onChange={(e) =>
                    setFormData({ ...formData, founder_school: e.target.value })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Contact Email</label>
                <Input
                  value={formData.contact_email}
                  onChange={(e) =>
                    setFormData({ ...formData, contact_email: e.target.value })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Internal Email</label>
                <Input
                  value={formData.internal_email}
                  onChange={(e) =>
                    setFormData({ ...formData, internal_email: e.target.value })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Founder LinkedIn</label>
                <Input
                  value={formData.founder_linkedin}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      founder_linkedin: e.target.value
                    })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Company Crunchbase
                </label>
                <Input
                  value={formData.company_crunchbase}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      company_crunchbase: e.target.value
                    })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Company LinkedIn</label>
                <Input
                  value={formData.company_linkedin}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      company_linkedin: e.target.value
                    })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Company Website</label>
                <Input
                  value={formData.company_website}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      company_website: e.target.value
                    })
                  }
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Whether fundraising
                </label>
                <Select
                  value={formData.funding}
                  onValueChange={(value) =>
                    setFormData({ ...formData, funding: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Yes">Yes</SelectItem>
                    <SelectItem value="No">No</SelectItem>
                    <SelectItem value="Not disclosed">Not disclosed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Legally Incorporated
                </label>
                <Select
                  value={formData.legally_incorporated}
                  onValueChange={(value) =>
                    setFormData({ ...formData, legally_incorporated: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose an option..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Yes">Yes</SelectItem>
                    <SelectItem value="No">No</SelectItem>
                    <SelectItem value="In progress">In progress</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Description</label>
              <Textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                rows={4}
              />
            </div>
          </form>
        </div>

        <div className="flex justify-end gap-2 py-4 px-4 border-t sticky bottom-0 bg-background mt-2">
          <DialogClose asChild>
            <Button type="button" variant="outline">
              Cancel
            </Button>
          </DialogClose>
          <Button form="company-form" type="submit">
            {company ? 'Update' : 'Save'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
