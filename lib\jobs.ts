// lib/jobs.ts
import { createClient } from '@/utils/supabase/client';

export interface StartupJob {
  id: string;
  title: string;
  description: string;
  work_type?: string;
  sponsorship: string;
  required_skills: string[];
  salary: string;
  location: string;
  created_at: string;
  company_description: string;
  live: boolean;
  work_mode: string;
  company_id: string;
  requirements?: string[];
  duties?: string[];
  industry?: string;
  term: string;
  job_link: string;
  application_url?: string;
  linkedin_required?: boolean;
  other_compensation?: boolean;
  startup_company?: {
    id: string;
    name: string;
    logo_url: string;
    description: string;
    company_linkedin?: string;
    company_website?: string;
    legally_incorporated?: boolean;
    funding?: string;
    internal_email?: string;
    industry?: string;
  };
}

export interface JobFilters {
  title?: string;
  location?: string;
  sponsorship?: string;
  term?: string;
}

export class JobsAPI {
  private static supabase = createClient();

  static async getJobsCount(filters?: JobFilters) {
    try {
      let query = this.supabase
        .from('startup_jobs')
        .select('*', { count: 'exact', head: true })
        .eq('live', true);

      // Apply filters
      if (filters) {
        if (filters.title) {
          query = query.ilike('title', `%${filters.title}%`);
        }
        if (filters.location) {
          query = query.eq('location', filters.location);
        }
        if (filters.sponsorship) {
          query = query.eq('sponsorship', filters.sponsorship);
        }
        if (filters.term) {
          query = query.eq('term', filters.term);
        }
      }

      const { count, error } = await query;
      if (error) throw error;
      return count || 0;
    } catch (error) {
      console.error('Error getting jobs count:', error);
      throw error;
    }
  }
  static async getAllStartupJobs(
    page: number = 1,
    pageSize: number = 10,
    dateFilter?: string,
    filters?: JobFilters
  ) {
    try {
      const totalCount = await this.getJobsCount(filters);

      const start = (page - 1) * pageSize;
      const end = start + pageSize - 1;

      let query = this.supabase
        .from('startup_jobs')
        .select(
          `
          *,
          startup_company:startup_companies!company_id(
            id,
            name,
            logo_url,
            description,
            company_linkedin,
            company_website,
            legally_incorporated,
            funding,
            internal_email,
            industry
          )
        `
        )
        .eq('live', true)
        .order('created_at', { ascending: false })
        .range(start, end);

      // Apply filters
      if (filters) {
        if (filters.title) {
          query = query.ilike('title', `%${filters.title}%`);
        }
        if (filters.location) {
          query = query.eq('location', filters.location);
        }
        if (filters.sponsorship) {
          query = query.eq('sponsorship', filters.sponsorship);
        }
        if (filters.term) {
          query = query.eq('term', filters.term);
        }
      }

      if (dateFilter) {
        const filterDate = new Date(dateFilter);
        query = query.gte('created_at', filterDate.toISOString());
      }

      const { data: jobs, error } = await query;

      if (error) throw error;

      // Process jobs but maintain the company data that was successfully joined
      const processedJobs = jobs?.map((job) => ({
        ...job,
        startup_company: job.startup_company || {
          id: job.company_id,
          name: job.company_description || 'Unknown Company',
          logo_url: null,
          description: job.company_description || null,
          company_linkedin: null,
          company_website: null,
          legally_incorporated: null,
          funding: null,
          internal_email: null,
          industry: null
        }
      }));
      return {
        jobs: processedJobs || [],
        totalCount,
        currentPage: page,
        totalPages: Math.ceil(totalCount / pageSize)
      };
    } catch (error) {
      console.error('Error fetching startup jobs:', error);
      throw error;
    }
  }

  static async getFilterOptions() {
    try {
      const { data: jobs, error } = await this.supabase
        .from('startup_jobs')
        .select('location, sponsorship, term')
        .eq('live', true);

      if (error) throw error;

      // Get unique values for each filter
      const locations = Array.from(
        new Set(jobs?.map((job) => job.location).filter(Boolean))
      );
      const sponsorships = Array.from(
        new Set(jobs?.map((job) => job.sponsorship).filter(Boolean))
      );
      const workTypes = Array.from(
        new Set(jobs?.map((job) => job.term).filter(Boolean))
      );

      return {
        locations,
        sponsorships,
        workTypes
      };
    } catch (error) {
      console.error('Error fetching filter options:', error);
      throw error;
    }
  }
  static async getStartupJobById(id: string) {
    try {
      // Using the same join structure as getAllStartupJobs
      const { data: job, error } = await this.supabase
        .from('startup_jobs')
        .select(
          `
          *,
          startup_company:startup_companies!company_id(
            id,
            name,
            logo_url,
            description,
            company_linkedin,
            company_website,
            legally_incorporated,
            funding,
            internal_email,
            industry
          )
        `
        )
        .eq('id', id)
        .eq('live', true)
        .maybeSingle(); // Using maybeSingle() instead of single() to handle null cases better

      if (error) {
        console.error('Database query error:', error);
        throw error;
      }

      if (!job) {
        throw new Error('Job not found');
      }

      // Process and return the job with proper fallback
      const processedJob = {
        ...job,
        startup_company: job.startup_company || {
          id: job.company_id,
          name: job.company_description || 'Unknown Company',
          logo_url: null,
          description: job.company_description || null,
          company_linkedin: null,
          company_website: null,
          legally_incorporated: null,
          funding: null,
          internal_email: null,
          industry: null
        }
      };

      return processedJob as StartupJob;
    } catch (error) {
      console.error('Error in getStartupJobById:', error);
      throw error;
    }
  }

  static async getSimilarJobs(jobId: string, limit: number = 3) {
    try {
      const job = await this.getStartupJobById(jobId);

      const { data: similarJobs, error } = await this.supabase
        .from('startup_jobs')
        .select(
          `
          *,
          startup_company:startup_companies!company_id (
            id,
            name,
            logo_url
          )
        `
        )
        .eq('live', true)
        .eq('term', job.term)
        .neq('id', jobId)
        .limit(limit);

      if (error) throw error;
      return similarJobs as StartupJob[];
    } catch (error) {
      console.error('Error fetching similar jobs:', error);
      return [];
    }
  }

  static async getRecentJobsByCompany(
    companyId: string,
    currentJobId: string,
    limit: number = 2
  ) {
    try {
      const { data: jobs, error } = await this.supabase
        .from('startup_jobs')
        .select('*')
        .eq('company_id', companyId)
        .eq('live', true)
        .neq('id', currentJobId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return jobs as StartupJob[];
    } catch (error) {
      console.error('Error fetching company jobs:', error);
      return [];
    }
  }
}
