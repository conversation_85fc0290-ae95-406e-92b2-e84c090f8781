export type User = {
  id: string;
  email: string;
  verified: boolean;
  user_type: 'Admin' | 'Insider' | 'Candidate' | 'Company' | null;
  created_at: string | null;
  updated_at: string | null;
  unique_identifier: string | null;
  vip: boolean;
  has_ddplan: boolean;
  first_name: string | null;
  last_name: string | null;
  seeking_job: boolean;
  work_preferences: string[] | null;
  work_types: string[] | null;
  verification_code: string | null;
  verification_expiration: string | null;
  location: string | null;
  nationality: string | null;
  preferred_name: string | null;
  avatar_url: string | null;
  resume_url: string | null;
  linkedin_url: string | null;
  mbti: string | null;
  seeking_training: boolean;
  bio: string | null;
  email_linkedin: string | null;
  is_subscription_active: boolean;
  portfolio: string | null;
  applied_public_firm_company: string[] | null;
  applied_public_firm_jobs: string[] | null;
  applied_startup_companies: string[] | null;
  bookmarked_pf_jobs: string[] | null;
  bookmarked_startup_jobs: string[] | null;
  startup_company: string | null;
  webcrawler_records_nonvip: any[] | null;
  webcrawler_records_vip: any[] | null;
  firm_name: string | null;
  firm_url: string | null;
  loved_posts: string[] | null;
  loved_reactions: string[] | null;
  phone_number: string | null;
  modified_vip_date: string | null;
  is_open_for_reverse_hiring: boolean;
  need_sponsorship: string | null;
  has_suspended_capstone: boolean;
  is_insider: boolean;
  subscription_id: string | null;
  subscription_type: 'Daily Jobs Access' | 'VIP 1-Month Pass' | 'VIP 6-Month Pass' | 'VIP Full-Year Pass' | null;
  subscription_expiry_date: string | null;
};

export type UserFilters = {
  userType?: string;
  searchQuery?: string;
  verified?: boolean;
  vip?: boolean;
  isInsider?: boolean;
  seekingJob?: boolean;
  seekingTraining?: boolean;
  isOpenForReverseHiring?: boolean;
  location?: string;
  nationality?: string;
  workTypes?: string[];
  workPreferences?: string[];
};

export type UserFilters = {
  userType?: string;
  searchQuery?: string;
  verified?: boolean;
  vip?: boolean;
};

// Type for updating candidate profile
export type CandidateUpdateData = {
  first_name?: string | null;
  last_name?: string | null;
  email?: string;
  preferred_name?: string | null;
  nationality?: string | null;
  phone_number?: string | null;
  location?: string | null;
  need_sponsorship?: string | null;
  mbti?: string | null;
  bio?: string | null;
  resume_url?: string | null;
  portfolio?: string | null;
  linkedin_url?: string | null;
  is_open_for_reverse_hiring?: boolean;
  modified_at?: string;
  avatar_url?: string | null;
};

export type CompanyUpdateData = {
  startup_company?: string | null;
};

// Type for updating candidate preferences
export type CandidatePreferencesUpdateData = {
  work_preferences: string[];
  work_types: string[];
  seeking_job: boolean;
  seeking_training: boolean;
  modified_at?: string;
};

export type CandidateQueryParams = {
  page?: number;
  searchTerm?: string;
  location?: string;
  workType?: string;
  sponsorship?: string;
};
