// export interface BubbleAuthResponse {
//     response: {
//       id: string;
//       token: string;
//       user_id:string;
//       otp?: string,
//       // Add other user fields as needed
//     };
//     status: 'success' | 'error';
//     message?: string;
//   }
  
  export interface BubbleUser {
    id: string;
    email: string;
    // Add other user fields as needed
  }
  export interface BubbleAuthResponse {
    status: 'success' | 'error';
    message?: string;
    response: {
      token: string;
      user_id: string;
      id: string;
      otp?: string;
    };
  }