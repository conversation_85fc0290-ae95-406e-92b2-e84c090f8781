'use server';

import { createClient as createServerClient } from '@/utils/supabase/server';
import { PublicFirmApplication } from '@/types/public-firms';

/**
 * Create a new public firm application
 * @param applicationData - The application data to create
 * @returns The created application
 */
export async function createPublicFirmApplication(
  applicationData: Partial<PublicFirmApplication>
): Promise<{ applicationId: string }> {
  const supabase = createServerClient();

  try {
    // Add validation to ensure required fields are present
    if (!applicationData.job_id) {
      throw new Error('Job ID is required');
    }

    if (!applicationData.candidate && !applicationData.creator) {
      throw new Error('Candidate or creator email is required');
    }

    const { data, error } = await supabase
      .from('public_firm_applications')
      .insert(applicationData)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return { applicationId: data.id };
  } catch (error) {
    console.error('Supabase error creating public firm application:', error);
    throw new Error('Failed to create public firm application');
  }
}

/**
 * Get a public firm application by ID
 * @param jobId - The ID of the job to get the application for
 * @returns The application or null if it doesn't exist
 */
export async function getPublicFirmApplication(
  jobId: string
): Promise<PublicFirmApplication | null> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('public_firm_applications')
      .select('*')
      .eq('job_id', jobId)
      .single();

    if (error) {
      console.error('Supabase error fetching public firm application:', error);
      throw new Error('Failed to fetch public firm application');
    }

    return data || null;
  } catch (error) {
    console.error('Supabase error fetching public firm application:', error);
    throw new Error('Failed to fetch public firm application');
  }
}

/**
 * Get all applications for a user
 * @param userEmail - The email of the user to get applications for
 * @returns The applications or an empty array if there are no applications
 */
export async function getApplicationsForUser(
  userEmail: string
): Promise<PublicFirmApplication[]> {
  const supabase = createServerClient();

  try {
    const { data, error } = await supabase
      .from('public_firm_applications')
      .select('*')
      .eq('candidate', userEmail);

    if (error) {
      console.error('Error fetching applications for user:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Supabase error fetching applications for user:', error);
    throw new Error('Failed to fetch applications for user');
  }
}
