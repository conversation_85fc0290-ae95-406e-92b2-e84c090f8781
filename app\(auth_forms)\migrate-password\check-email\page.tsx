'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { Mail, ArrowRight } from 'lucide-react';
import { resendVerificationCode } from '@/utils/supabase/auth';

export default function CheckEmailPage() {
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const [email, setEmail] = useState<string | null>(null);
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    const emailParam = searchParams.get('email');
    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  const handleResendEmail = async () => {
    if (!email || isResending) return;

    setIsResending(true);
    try {
      const result = await resendVerificationCode(email);

      if (result.type === 'success') {
        toast({
          title: result.title,
          description: result.description,
          variant: 'default'
        });
      } else {
        throw new Error(result.description);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to resend verification email',
        variant: 'destructive'
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
        <div className="mb-6">
          <div className="mx-auto w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-4">
            <Mail className="h-6 w-6 text-blue-500" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Check your email
          </h1>
          <p className="text-gray-600">
            We've sent a verification link to{' '}
            <span className="font-medium text-gray-900">{email}</span>
          </p>
        </div>

        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-700">
              Click the link in the email to verify your account and complete
              the migration process. The link will expire in 24 hours.
            </p>
          </div>

          <Button
            onClick={handleResendEmail}
            variant="outline"
            className="w-full border-gray-300 text-gray-700 hover:bg-gray-50"
            disabled={isResending || !email}
          >
            {isResending ? 'Sending...' : 'Resend verification email'}
          </Button>

          <div className="pt-4 border-t border-gray-200">
            <a
              href="/signin"
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
            >
              Return to sign in
              <ArrowRight className="ml-2 h-4 w-4" />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
