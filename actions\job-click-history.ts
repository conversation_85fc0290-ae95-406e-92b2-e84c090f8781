'use server';

import { createClient } from '@/utils/supabase/server';
import { v4 as uuidv4 } from 'uuid';
import { JobClickData, JobClickHistory } from '@/types/job-click';

/**
 * Tracks when a user clicks on a specific job within a daily_job file
 * This function creates an entry in the job_click_history table
 * and associates it with an existing download_file_tracker record
 *
 * @param clickData - Data about the job that was clicked
 * @returns An object indicating success or failure
 */
export async function trackJobClick(
  clickData: JobClickData
): Promise<{ success: boolean; data?: JobClickHistory; error?: string }> {
  const supabase = createClient();

  try {
    // Create a new entry in the job_click_history table
    const { data, error } = await supabase
      .from('job_click_history')
      .insert({
        id: uuidv4(),
        tracker_id: clickData.trackerId,
        keyword: clickData.keyword || null,
        job_title: clickData.jobTitle || null,
        company: clickData.company || null,
        clicked_at: new Date().toISOString(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error tracking job click:', error);
      return { success: false, error: error.message };
    }

    return { success: true, data };
  } catch (error) {
    console.error('Exception tracking job click:', error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : 'Unknown error tracking job click'
    };
  }
}

/**
 * Gets the click history for a specific file tracker
 *
 * @param trackerId - ID of the download_file_tracker record
 * @returns An array of job click history records
 */
export async function getJobClickHistory(
  trackerId: string
): Promise<JobClickHistory[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_click_history')
      .select('*')
      .eq('tracker_id', trackerId)
      .order('clicked_at', { ascending: false });

    if (error) {
      console.error('Error fetching job click history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching job click history:', error);
    return [];
  }
}

/**
 * Gets all job click history records
 *
 * @returns An array of all job click history records
 */
export async function getAllJobClickHistory(): Promise<JobClickHistory[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase
      .from('job_click_history')
      .select(
        `
        *,
        download_file_tracker (
          user_id,
          file_id
        )
      `
      )
      .order('clicked_at', { ascending: false });

    if (error) {
      console.error('Error fetching all job click history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching all job click history:', error);
    return [];
  }
}

/**
 * Gets popular jobs based on click activity
 *
 * @param limit - Number of popular jobs to return
 * @returns An array of popular jobs with click counts
 */
export async function getPopularJobs(
  limit = 10
): Promise<{ job_title: string; click_count: number }[]> {
  const supabase = createClient();

  try {
    const { data, error } = await supabase.rpc('get_popular_jobs', {
      limit_count: limit
    });

    if (error) {
      console.error('Error fetching popular jobs:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception fetching popular jobs:', error);
    return [];
  }
}

/**
 * Creates a job click history record associated with a file tracker.
 * This function attempts to create a new tracker first. If that fails due to a unique constraint
 * (meaning it already exists due to a concurrent operation), it then fetches the existing tracker.
 * The `clicked_at` timestamp of the tracker (new or existing) is set/updated.
 *
 * @param fileId - The ID of the file that was accessed
 * @param userId - The ID of the user accessing the file
 * @param jobDetails - Details about the specific job being clicked
 * @returns An object indicating success or failure
 */
export async function trackFileAndJobClick(
  fileId: string,
  userId: string,
  jobDetails: {
    keyword?: string;
    jobTitle?: string;
    company?: string;
  }
): Promise<{ success: boolean; error?: string }> {
  const supabase = createClient();
  const now = new Date().toISOString();
  let trackerId = uuidv4(); // Optimistically generate a new ID

  try {
    // 1. Attempt to INSERT a new download_file_tracker record.
    // Relies on unique constraint `unique_user_file` on (user_id, file_id).
    const { error: insertError } = await supabase
      .from('download_file_tracker')
      .insert({
        id: trackerId,
        user_id: userId.toString(), // Ensure string type
        file_id: fileId.toString(), // Ensure string type
        clicked_at: now
      });

    if (insertError) {
      // Check if the error is a unique constraint violation
      if (
        insertError.message.includes('unique_user_file') ||
        insertError.message.includes('23505')
      ) {
        // Try an alternative approach to fetch the tracker if it exists
        try {
          // First approach: direct query with explicit text comparison
          const { data: trackerData, error: queryError } = await supabase
            .from('download_file_tracker')
            .select('id')
            .filter('user_id', 'eq', userId.toString())
            .filter('file_id', 'eq', fileId.toString())
            .limit(1);

          if (queryError) {
            console.error('Error with filter approach:', queryError);
          } else if (trackerData && trackerData.length > 0) {
            trackerId = trackerData[0].id;

            // Update the clicked_at timestamp
            const { error: updateError } = await supabase
              .from('download_file_tracker')
              .update({ clicked_at: now })
              .eq('id', trackerId);

            if (updateError) {
              console.warn('Failed to update timestamp:', updateError);
            }
          } else {
            // Backup approach: try a raw SQL query to ensure correct data type handling
            const { data: rawQueryResult, error: rawQueryError } =
              await supabase.rpc('get_file_tracker_by_ids', {
                p_user_id: userId.toString(),
                p_file_id: fileId.toString()
              });

            if (rawQueryError) {
              console.error('RPC fallback failed:', rawQueryError);
            } else if (rawQueryResult) {
              trackerId = rawQueryResult.id;
            }
          }
        } catch (fetchError) {
          console.error('Exception during tracker lookup:', fetchError);
          // Continue with original trackerId
        }
      } else {
        // Another error occurred during insert, not a unique constraint violation
        console.error('Error inserting new tracker:', insertError);
        return { success: false, error: 'Failed to insert new file tracker' };
      }
    }

    // 2. Create the job click history record
    const { error: clickError } = await supabase
      .from('job_click_history')
      .insert({
        id: uuidv4(), // New UUID for the job click history itself
        tracker_id: trackerId, // The one we found or created
        keyword: jobDetails.keyword || null,
        job_title: jobDetails.jobTitle || null,
        company: jobDetails.company || null,
        clicked_at: now,
        created_at: now
      });

    if (clickError) {
      console.error('Error creating job click record:', clickError);
      return { success: false, error: 'Failed to create job click record' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in trackFileAndJobClick:', error);
    return {
      success: false,
      error: 'Unknown server error in trackFileAndJobClick'
    };
  }
}

/**
 * Gets an existing file tracker ID or creates a new one
 * This helps prevent duplicate download_file_tracker records
 *
 * @param fileId - The ID of the file
 * @param userId - The ID of the user
 * @returns The tracker ID and a flag indicating if it was newly created
 */
export async function getOrCreateFileTracker(
  fileId: string,
  userId: string
): Promise<{ trackerId: string; isNew: boolean; error?: string }> {
  const supabase = createClient();

  try {
    // First check if there's an existing tracker for this user and file
    const { data: existingTracker, error: queryError } = await supabase
      .from('download_file_tracker')
      .select('id')
      .eq('user_id', userId)
      .eq('file_id', fileId)
      .order('clicked_at', { ascending: false })
      .limit(1)
      .single();

    if (!queryError && existingTracker) {
      // Found an existing tracker
      return { trackerId: existingTracker.id, isNew: false };
    }

    // If no existing tracker or if there was a "no rows returned" error
    // Check for not finding any results (common 404 error case)
    if (!existingTracker || queryError) {
      // Create a new tracker
      const trackerId = uuidv4();
      const now = new Date().toISOString();

      const { error: insertError } = await supabase
        .from('download_file_tracker')
        .insert({
          id: trackerId,
          user_id: userId,
          file_id: fileId,
          clicked_at: now
        });

      if (insertError) {
        console.error('Error creating file tracker:', insertError);
        return {
          trackerId: '',
          isNew: false,
          error: 'Failed to create file tracker'
        };
      }

      return { trackerId, isNew: true };
    }

    // This code should not be reached, but included for completeness
    console.error('Unexpected state in getOrCreateFileTracker');
    return { trackerId: '', isNew: false, error: 'Unexpected error state' };
  } catch (error) {
    console.error('Exception in getOrCreateFileTracker:', error);
    return {
      trackerId: '',
      isNew: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
