'use client';

import Link from 'next/link';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { signUpWithOAuth, signUp } from '@/utils/supabase/auth';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Chrome, Linkedin, ArrowLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import type { Provider } from '@supabase/supabase-js';
import { getURL } from '@/utils/helpers';
import Particles from '@/components/magicui/particles';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';

// Form validation schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z
    .string()
    .min(6, 'Password must be at least 6 characters')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .regex(/[A-Z]/, 'Password must contain at least one capital letter'),
  company: z.string().optional(),
  terms: z.boolean().refine((val) => val === true, {
    message: 'You must accept the terms of service'
  })
});

type FormValues = z.infer<typeof formSchema>;

export default function SignUpPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  // Initialize form with React Hook Form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
      company: '',
      terms: false
    }
  });

  const onSubmit = async (values: FormValues) => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append('email', values.email);
      formData.append('password', values.password);
      formData.append('userType', 'Candidate'); // default type
      if (values.company) {
        formData.append('company', values.company);
      }

      const result = await signUp(formData);

      toast({
        title: result.title,
        description: result.description,
        variant: result.type === 'error' ? 'destructive' : undefined
      });

      if (result.type === 'info') {
        // Stay on the page — user will check email
      }
    } catch (error) {
      console.error('Sign up error:', error);
      toast({
        title: 'Sign Up Error',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOAuthSignUp = async (provider: Provider) => {
    setIsSubmitting(true);

    try {
      await signUpWithOAuth(provider, getURL('/auth/callback'));

      toast({
        title: 'Redirecting...',
        description: `Creating account with ${provider}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error during OAuth sign-up:', error);
      toast({
        title: 'OAuth Sign-up Failed',
        description: 'Please try again or use another sign-up method.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // OAuth providers configuration
  const oAuthProviders = [
    {
      name: 'google',
      displayName: 'Google',
      icon: <Chrome className="mr-2 h-4 w-4" />
    },
    {
      name: 'linkedin',
      displayName: 'LinkedIn',
      icon: <Linkedin className="mr-2 h-4 w-4" />
    }
  ] as const;

  return (
    <div className="flex min-h-[100dvh] bg-background">
      {/* Left Section with Illustration */}
      <div className="hidden lg:flex flex-col flex-1 bg-[#118073] text-white p-12 justify-between relative overflow-hidden">
        <div className="relative z-10">
          <div className="w-40 h-auto mb-8">
            <Image
              src="/footer_logo.png"
              alt="InternUp Logo"
              width={160}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="space-y-6 max-w-md">
            <h1 className="text-4xl font-bold">Start Your Journey</h1>
            <p className="text-xl">
              Create an account to get started with InternUp - connecting talent
              with opportunities.
            </p>
          </div>
        </div>

        <div className="relative z-10">
          <p className="text-sm opacity-80">
            {new Date().getFullYear()} InternUp. All rights reserved.
          </p>
        </div>

        {/* Decorative elements */}
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#16a38a] rounded-full filter blur-3xl opacity-20 -mr-20 -mb-20"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-[#4ade80] rounded-full filter blur-3xl opacity-10 -ml-40 -mt-40"></div>
        <Particles
          className="absolute inset-0"
          quantity={300}
          size={1}
          ease={1}
          refresh
        />
      </div>

      {/* Right Section with Form */}
      <div className="flex-1 px-4 py-12 sm:px-6 lg:px-8 overflow-y-auto">
        <div className="flex items-center justify-between mb-8">
          <Link
            href="/"
            className="rounded-md p-2 transition-colors hover:bg-muted"
            prefetch={false}
          >
            <ArrowLeft className="h-5 w-5" />
            <span className="sr-only">Back</span>
          </Link>
        </div>

        <div className="max-w-md mx-auto">
          <div className="space-y-6 text-center mb-8">
            <h1 className="text-3xl font-bold bg-[#36BA98] bg-clip-text text-transparent">
              Create an Account
            </h1>
            <p className="text-muted-foreground">
              Join our community and discover opportunities
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          autoComplete="email"
                          disabled={isSubmitting}
                          className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Password
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="••••••••"
                          type="password"
                          autoComplete="new-password"
                          disabled={isSubmitting}
                          className="border-muted-foreground/20 focus-visible:ring-[#118073]"
                          {...field}
                        />
                      </FormControl>
                      <div className="space-y-1 text-sm text-muted-foreground mt-2">
                        <p>Your password should have:</p>
                        <ul className="space-y-1 ml-5 list-disc">
                          <li className="text-[#4ade80]">
                            At least 6 characters
                          </li>
                          <li className="text-[#4ade80]">
                            At least one number
                          </li>
                          <li className="text-[#4ade80]">
                            At least one capital letter
                          </li>
                        </ul>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="terms"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 pt-2">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="border-muted-foreground/30 data-[state=checked]:bg-[#118073] data-[state=checked]:border-[#118073]"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>
                          I agree to the{' '}
                          <Link
                            href="#"
                            className="text-[#118073] hover:underline"
                          >
                            terms of service
                          </Link>
                        </FormLabel>
                        <FormMessage />
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-[#118073] hover:bg-[#16a38a] text-white transition-colors"
                size="lg"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating account...' : 'Create account'}
              </Button>
            </form>
          </Form>

          <div className="relative my-8">
            <Separator className="bg-muted-foreground/20" />
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>

          <div className="grid gap-3">
            {oAuthProviders.map((provider) => (
              <Button
                key={provider.name}
                variant="outline"
                className="w-full border-muted-foreground/20 hover:bg-[#e6f7ef] hover:text-[#118073] transition-colors"
                onClick={() => handleOAuthSignUp(provider.name as Provider)}
                disabled={isSubmitting}
              >
                {provider.icon}
                {provider.displayName}
              </Button>
            ))}
          </div>

          <div className="text-center mt-8">
            <Link
              href="/signin"
              className="text-[#118073] hover:text-[#16a38a] text-sm font-medium hover:underline transition-colors"
              prefetch={false}
            >
              Already have an account?{' '}
              <span className="font-semibold">Sign in</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
