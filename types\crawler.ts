export type DailyJob = {
  id: string
  crawler_file?: string | null
  downloader_nonvip?: string[] | null
  downloader_vip?: string[] | null
  file_type?: string | null
  notice?: string | null
  posting_date?: Date | null
  creation_date?: Date | null
  modified_date?: Date | null
  slug?: string | null
  creator?: string | null
  views_count?: number | null
  nonvip_count?: number | null
  vip_count?: number | null
}

export type DailyJobFormData = {
  posting_date: string
  file_type: string
  notice: string
  crawler_file: string
}

