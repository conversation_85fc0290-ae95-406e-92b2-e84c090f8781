import { Suspense } from 'react';
import type { JobFilters } from '@/types/startups';
import StartupJobsList from './startup-jobs-list';
import JobListSkeleton from '@/components/job-market/job-list-skeleton';

export default async function StartupJobsPage({
  searchParams
}: {
  searchParams: {
    page?: string;
    date?: string;
    title?: string;
    location?: string;
    sponsorship?: string;
    term?: string;
  };
}) {
  const currentPage = Number(searchParams.page) || 1;
  const pageSize = 10;
  const dateFilter = searchParams.date || '';

  const filters: JobFilters = {
    title: searchParams.title,
    location: searchParams.location,
    sponsorship: searchParams.sponsorship,
    term: searchParams.term
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Discover Startup Jobs at InternUp
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            We work with venture-backed tech startups that support international
            students for early career development.
          </p>
        </div>

        <Suspense fallback={<JobListSkeleton count={5} />}>
          <StartupJobsList
            currentPage={currentPage}
            pageSize={pageSize}
            dateFilter={dateFilter}
            filters={filters}
            searchParams={searchParams}
          />
        </Suspense>
      </div>
    </div>
  );
}
