'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { ProgressIndicator } from '../components/progress-indicator';
import { useOnboarding } from '../components/onboarding-provider';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  updateCandidatePreferences,
  updateCandidateProfile
} from '@/actions/admin/candidate';
import { useUser } from '@/hooks/useUser';

// Define extended jobPreferences type to include our custom fields
type ExtendedJobPreferences = {
  location: string;
  work_types: string[];
  jobType?: string[]; // Include this for backward compatibility
  salary: string;
  work_preferences?: string[];
  seeking_job?: boolean;
  seeking_training?: boolean;
  is_open_for_reverse_hiring?: boolean;
};

export default function JobPreferencesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data, updateJobPreferences, markStepAsCompleted } = useOnboarding();
  const { user } = useUser();

  // Define steps for progress indicator
  const steps = [
    '/onboarding/candidate/personal-info',
    '/onboarding/candidate/job-preferences',
    '/onboarding/candidate/about-you',
    '/onboarding/candidate/complete'
  ];

  // State for selected options
  const [location, setLocation] = useState('');
  const [workTypes, setWorkTypes] = useState<string[]>([]);
  const [jobInterests, setJobInterests] = useState<string[]>([]);
  const [salary, setSalary] = useState('');

  // State for current focus options (individual booleans matching DB schema)
  const [seekingJob, setSeekingJob] = useState(false);
  const [seekingTraining, setSeekingTraining] = useState(false);
  const [openForReverseHiring, setOpenForReverseHiring] = useState(false);

  // Load data from context on mount
  useEffect(() => {
    if (data.jobPreferences) {
      // Location is a string type, directly set it
      if (data.jobPreferences.location) {
        setLocation(data.jobPreferences.location);
      }

      // Cast to our extended type to access all properties
      const extendedPrefs =
        data.jobPreferences as unknown as ExtendedJobPreferences;

      // Map fields correctly based on database schema
      setWorkTypes(extendedPrefs.work_types || extendedPrefs.jobType || []);
      setSalary(extendedPrefs.salary || '');

      // Map job interests to work_preferences
      setJobInterests(extendedPrefs.work_preferences || []);

      // Map individual boolean flags
      setSeekingJob(extendedPrefs.seeking_job || false);
      setSeekingTraining(extendedPrefs.seeking_training || false);
      setOpenForReverseHiring(
        extendedPrefs.is_open_for_reverse_hiring || false
      );
    }
  }, [data.jobPreferences]);

  const workTypeSuggestions = [
    'Full Time',
    'Part Time',
    'Internship',
    'OPT/CPT'
  ];

  const jobInterestSuggestions = [
    'Business Analyst',
    'Data Scientist',
    'Product Manager',
    'Researcher',
    'Marketer',
    'Finance Analyst',
    'Software Developer',
    'Machine Learning Engineer',
    'LLM Engineer',
    'AI Engineer',
    'Back-end Dev',
    'Front-end Dev',
    'Database Engineer',
    'Full-stack',
    'Graphic Designer',
    'UI Designer',
    'UX Designer',
    'Operations Manager',
    'Quantitative Researcher',
    'DevOps Engineer',
    'Mechanical Engineer',
    'Robotics Engineer',
    'HR Assistant'
  ];

  // Tag management for work types and job interests
  const addTag = (tag: string, type: 'workType' | 'jobInterest') => {
    if (!tag.trim()) return;

    switch (type) {
      case 'workType':
        if (!workTypes.includes(tag)) {
          setWorkTypes([...workTypes, tag]);
        }
        break;
      case 'jobInterest':
        if (!jobInterests.includes(tag) && jobInterests.length < 3) {
          setJobInterests([...jobInterests, tag]);
        } else if (jobInterests.length >= 3) {
          toast({
            variant: 'destructive',
            title: 'Limit Reached',
            description: 'You can select at most 3 job interests',
            duration: 3000
          });
        }
        break;
    }
  };

  const removeTag = (tag: string, type: 'workType' | 'jobInterest') => {
    switch (type) {
      case 'workType':
        setWorkTypes(workTypes.filter((item) => item !== tag));
        break;
      case 'jobInterest':
        setJobInterests(jobInterests.filter((item) => item !== tag));
        break;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form
      if (!location || workTypes.length === 0) {
        throw new Error('Please select at least one location and work type.');
      }

      if (!user || !user.id) {
        throw new Error('User not authenticated. Please log in and try again.');
      }

      // Create preferences object that maps to database schema
      const preferences = {
        work_preferences: jobInterests,
        work_types: workTypes,
        seeking_job: seekingJob,
        seeking_training: seekingTraining,
        is_open_for_reverse_hiring: openForReverseHiring
      };

      // Additional candidate profile data that needs to go to the profile
      const profileData = {
        location: location // Location as a simple string
        // Any other profile fields would go here
      };

      // Update the context with form data for UI
      updateJobPreferences({
        ...preferences,
        ...profileData,
        salary: salary
      } as unknown as Partial<typeof data.jobPreferences>);

      // Save preferences to Supabase
      await updateCandidatePreferences(user.id, preferences);

      // Save profile data (including location) to Supabase
      await updateCandidateProfile(user.id, profileData);

      // Mark this step as completed
      markStepAsCompleted('/onboarding/candidate/job-preferences');

      // Show success toast
      toast({
        title: 'Preferences Saved',
        description: 'Your job preferences have been saved successfully.',
        duration: 3000
      });

      // Navigate to the next step
      router.push('/onboarding/candidate/about-you');
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Something went wrong. Please try again.',
        duration: 5000
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      {/* Header with progress indicator */}
      <ProgressIndicator steps={steps} />

      <div className="flex flex-1 p-6 md:p-12">
        {/* Left section with logo */}
        <div className="hidden lg:flex lg:w-1/2 flex-col justify-center items-center pr-12">
          <div className="flex flex-col items-center gap-8">
            <div className="flex items-center space-x-2">
              <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
                <Image
                  src="/favicon_new.png"
                  alt="logo"
                  width={25}
                  height={25}
                />
              </div>
              <span className="text-lg md:text-xl font-extrabold tracking-tightest">
                InternUp
              </span>
            </div>
            <div className="flex flex-col items-center gap-2 text-center">
              <h2 className="text-xl font-semibold">Connecting</h2>
              <h2 className="text-xl font-semibold">
                International <span className="text-[#36BA98]">Talent</span>
              </h2>
              <h2 className="text-xl font-semibold">
                with <span className="text-[#36BA98]">Opportunities</span>
              </h2>
            </div>
          </div>
        </div>

        {/* Right section with form */}
        <div className="w-full lg:w-1/2">
          <div className="max-w-md mx-auto">
            <h1 className="text-3xl font-bold mb-4">
              Set Your Job Search Preferences
            </h1>
            <p className="text-gray-700 mb-8">
              Share a bit more about your preferences and experience to unlock
              tailored opportunities and connect with the right companies.
            </p>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Location field - updated to be a simple text input */}
              <div className="space-y-2">
                <label htmlFor="location" className="block text-sm font-medium">
                  Current Physical Location
                  <span className="text-red-500">* </span>
                  <span className="text-xs text-gray-500">
                    (City, State, Country)
                  </span>
                </label>
                <div className="relative">
                  <input
                    id="location"
                    type="text"
                    value={location}
                    onChange={(e) => setLocation(e.target.value)}
                    placeholder="e.g. San Francisco, CA, USA"
                    className="w-full rounded-xl p-2 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#36BA98]"
                  />
                  <span className="absolute right-2 top-2 text-gray-400">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                  </span>
                </div>
              </div>

              {/* Work Type field */}
              <div className="space-y-2">
                <label htmlFor="workType" className="block text-sm font-medium">
                  Work Type<span className="text-red-500">*</span>
                </label>
                <Select onValueChange={(value) => addTag(value, 'workType')}>
                  <SelectTrigger className="w-full rounded-xl">
                    <SelectValue placeholder="Select a work type" />
                  </SelectTrigger>
                  <SelectContent>
                    {workTypeSuggestions.map((type) => (
                      <SelectItem
                        key={type}
                        value={type}
                        disabled={workTypes.includes(type)}
                      >
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Selected work types displayed as badges */}
                <div className="flex flex-wrap gap-2 mt-2">
                  {workTypes.map((item) => (
                    <Badge
                      key={item}
                      className="bg-[#36BA98] text-white hover:bg-[#2ca586] rounded-full px-3 py-1 flex items-center space-x-1"
                    >
                      <span>{item}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(item, 'workType')}
                        className="ml-1 rounded-full text-white hover:bg-[#20856b]"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Job Interests field - Changed to Selector */}
              <div className="space-y-2">
                <label
                  htmlFor="jobInterests"
                  className="block text-sm font-medium"
                >
                  Job Interests<span className="text-red-500">*</span>{' '}
                  <span className="text-xs text-gray-500">(max 3)</span>
                </label>
                <Select onValueChange={(value) => addTag(value, 'jobInterest')}>
                  <SelectTrigger className="w-full rounded-xl">
                    <SelectValue placeholder="Select a job interest" />
                  </SelectTrigger>
                  <SelectContent>
                    {jobInterestSuggestions.map((interest) => (
                      <SelectItem
                        key={interest}
                        value={interest}
                        disabled={
                          jobInterests.includes(interest) ||
                          jobInterests.length >= 3
                        }
                      >
                        {interest}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Selected job interests displayed as badges */}
                <div className="flex flex-wrap gap-2 mt-2">
                  {jobInterests.map((item) => (
                    <Badge
                      key={item}
                      className="bg-[#36BA98] text-white hover:bg-[#2ca586] rounded-full px-3 py-1 flex items-center space-x-1"
                    >
                      <span>{item}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(item, 'jobInterest')}
                        className="ml-1 rounded-full text-white hover:bg-[#20856b]"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Current Focus */}
              <div className="space-y-2">
                <label className="block text-sm font-medium mb-2">
                  Your Current Focus<span className="text-red-500">*</span>
                </label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="actively-looking"
                      checked={seekingJob}
                      onCheckedChange={(checked) =>
                        setSeekingJob(checked === true)
                      }
                    />
                    <label
                      htmlFor="actively-looking"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Actively Looking For Job Opportunities
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="career-coaching"
                      checked={seekingTraining}
                      onCheckedChange={(checked) =>
                        setSeekingTraining(checked === true)
                      }
                    />
                    <label
                      htmlFor="career-coaching"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Interested In Career Coaching
                    </label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="networking"
                      checked={openForReverseHiring}
                      onCheckedChange={(checked) =>
                        setOpenForReverseHiring(checked === true)
                      }
                    />
                    <label
                      htmlFor="networking"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Open to join the candidate pool for companies recruiting
                    </label>
                  </div>
                </div>
              </div>

              <div className="pt-4 flex justify-center">
                <Button
                  type="submit"
                  className="bg-[#36BA98] hover:bg-[#2da888] text-white px-12 py-2 rounded-xl"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Processing...' : 'Continue'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
