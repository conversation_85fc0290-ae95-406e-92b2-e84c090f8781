import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { getErrorRedirect } from "@/utils/helpers";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  
  // Check for error in the URL parameters
  const error = requestUrl.searchParams.get("error");
  const errorDescription = requestUrl.searchParams.get("error_description");
  
  if (error) {
    console.error(`OAuth error: ${error}, Description: ${errorDescription}`);
    return NextResponse.redirect(
      getErrorRedirect(`${requestUrl.origin}/signin`, error, errorDescription || "Authentication failed.")
    );
  }

  if (!code) {
    return NextResponse.redirect(
      getErrorRedirect(`${requestUrl.origin}/signin`, "No code", "Authentication failed.")
    );
  }

  // Simply redirect to the client-side handler with the code
  // Let the client handle all the authentication logic
  const callbackUrl = new URL(`/auth/callback`, requestUrl.origin);
  callbackUrl.searchParams.set("code", code);
  
  // Add optional parameters if they exist
  const userType = requestUrl.searchParams.get("user_type");
  const flow = requestUrl.searchParams.get("flow");
  
  if (userType) callbackUrl.searchParams.set("user_type", userType);
  if (flow) callbackUrl.searchParams.set("flow", flow);
  
  return NextResponse.redirect(callbackUrl);
}