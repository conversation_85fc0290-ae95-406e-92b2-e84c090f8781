'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useRouter, useSearchParams } from 'next/navigation';
import { Chrome, Linkedin, Loader2 } from 'lucide-react';
import { createClient } from '@/utils/supabase/client';
import { signUpWithOAuth } from '@/utils/supabase/auth';
import type { Provider } from '@supabase/supabase-js';

type MigrationStatus =
  | 'not_migrated'
  | 'migration_invited'
  | 'verification_pending'
  | 'migrated'
  | 'failed';

export default function MigratePassword() {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [email, setEmail] = useState<string | null>(null);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');

  useEffect(() => {
    async function initializeMigration() {
      const emailParam = searchParams.get('email');
      if (!emailParam) {
        toast({
          title: 'Error',
          description: 'Email parameter is missing from URL',
          variant: 'destructive'
        });
        router.push('/signin');
        return;
      }

      setEmail(emailParam);

      // Update migration status to invited if needed
      const supabase = createClient();
      const {
        data: { user }
      } = await supabase.auth.getUser();

      if (user?.id) {
        const { data: userData, error: fetchError } = await supabase
          .from('users')
          .select('migration_status')
          .eq('id', user.id)
          .single();

        if (!fetchError && userData) {
          // Only update if not already in a later stage
          if (userData.migration_status === 'not_migrated') {
            await supabase
              .from('users')
              .update({
                migration_status: 'migration_invited' as MigrationStatus,
                updated_at: new Date().toISOString()
              })
              .eq('id', user.id);
          } else if (userData.migration_status === 'migrated') {
            toast({
              title: 'Already Migrated',
              description: 'Your account has already been migrated.',
              variant: 'default'
            });
            router.push('/candidate');
            return;
          }
        }
      }

      setIsLoading(false);
    }

    initializeMigration();
  }, [searchParams, router, toast]);

  const validatePasswords = () => {
    if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters long');
      return false;
    }
    if (password !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return false;
    }
    setPasswordError('');
    return true;
  };
  const handleOAuthSignUp = async (provider: string) => {
    setIsSubmitting(true);

    try {
      await signUpWithOAuth(
        provider as Provider,
        location.origin + '/auth/callback'
      );

      toast({
        title: 'Redirecting...',
        description: `Creating account with ${provider}`,
        variant: 'default'
      });
    } catch (error) {
      console.error('Error during OAuth sign-up:', error);
      toast({
        title: 'OAuth Sign-up Failed',
        description: 'Please try again or use another sign-up method.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleSetPassword = async () => {
    if (isSubmitting || !email) return;

    if (!validatePasswords()) {
      return;
    }

    setIsSubmitting(true);

    const supabase = createClient();

    try {
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${location.origin}/auth/callback`
        }
      });

      if (signUpError) {
        throw signUpError;
      }

      // ✅ Mark as migrated
      const { error: updateError } = await supabase
        .from('users')
        .update({
          migration_status: 'migrated' as MigrationStatus,
          updated_at: new Date().toISOString()
        })
        .eq('email', email);

      if (updateError) {
        throw updateError;
      }

      toast({
        title: 'Check Your Email',
        description:
          'Please verify your email to complete the migration process.',
        variant: 'default'
      });

      // Redirect to signin after short delay
      setTimeout(() => {
        router.push(`/signin?email=${encodeURIComponent(email)}`);
      }, 2000);
    } catch (error) {
      console.error('Signup error:', error);
      toast({
        title: 'Migration Failed',
        description:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred during migration.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // OAuth providers configuration
  const oAuthProviders = [
    {
      name: 'google',
      displayName: 'Google',
      icon: <Chrome className="mr-2 h-4 w-4" />
    },
    {
      name: 'linkedin',
      displayName: 'LinkedIn',
      icon: <Linkedin className="mr-2 h-4 w-4" />
    }
  ] as const;

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
          <Loader2 className="animate-spin w-6 h-6 mx-auto mb-4 text-gray-600" />
          <p className="text-sm text-gray-700">Preparing migration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100">
      <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-6 text-gray-900">
          Set Your Password
        </h1>
        <div className="space-y-6">
          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-700">
              As part of our system upgrade, you'll need to migrate your account
              to continue using InternUp. Please set a new password to complete
              the migration.
            </p>
            <br />
            <p className="text-sm text-blue-700">
              Don't worry, all of your existing data has been safely transferred
              to the new system.
            </p>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email
              </label>
              <Input
                type="email"
                value={email || ''}
                disabled
                className="bg-gray-50 border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                New Password
              </label>
              <Input
                type="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setPasswordError('');
                }}
                placeholder="Enter your new password"
                className="border-gray-300"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password
              </label>
              <Input
                type="password"
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                  setPasswordError('');
                }}
                placeholder="Confirm your new password"
                className="border-gray-300"
              />
            </div>

            {passwordError && (
              <p className="text-sm text-red-600">{passwordError}</p>
            )}

            <Button
              onClick={handleSetPassword}
              className="w-full bg-[#118073] hover:bg-[#16a38a] text-white transition-colors flex items-center justify-center gap-2"
              disabled={isSubmitting || !email}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Setting Password...</span>
                </>
              ) : (
                'Set Password'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
