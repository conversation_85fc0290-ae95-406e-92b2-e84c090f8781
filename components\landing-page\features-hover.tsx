import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Filter, Calendar, Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import Image from 'next/image';

const mainFeature = {
  title: 'Visa Sponsorship Made Simple',
  subFeatures: [
    {
      icon: Filter,
      title: 'Filter Out the Guesswork',
      description: 'Only see roles that offer visa sponsorship upfront'
    },
    {
      icon: Calendar,
      title: 'Skip the Frustration',
      description:
        "No more wasted time on interviews with companies that don't sponsor"
    },
    {
      icon: Send,
      title: 'Connect with Confidence',
      description:
        'Focus on opportunities that support international talent and fit your career goals'
    }
  ]
};

const features = [
  {
    titleParts: [
      { text: 'AI-Powered', className: 'text-[#92FDE1]' },
      { text: 'Career', className: 'text-[#FFFFFF]' },
      { text: 'Coaching', className: 'text-[#FFFFFF]' }
    ],
    description:
      'Our AI Career Coach and Mental Health Coach are here to guide and support you every step of the way',
    icon: '/features-icons/Regenerate.png',
    className: 'bg-[#118073] text-white hover:bg-teal-800'
  },
  {
    titleParts: [
      { text: 'Exclusive', className: 'text-neutralBlack font-medium' },
      { text: 'Referrals', className: 'text-neutralBlack font-medium' },
      { text: 'Faster Hiring', className: 'text-[#0DBA8D] font-medium' }
    ],
    description:
      'Complete your profile and let employers approach you with opportunities that match your skills',
    icon: '/features-icons/Arrow_Reload_02.png'
  },
  {
    titleParts: [
      { text: 'Reverse', className: 'text-neutralBlack font-medium' },
      { text: 'Recruiting', className: 'text-neutralBlack font-medium' },
      { text: 'Let Jobs Find You', className: 'text-[#0DBA8D] font-medium' }
    ],
    description:
      'Complete your profile and let employers approach you with opportunities that match your skills',
    icon: '/features-icons/User_Check.png'
  },
  {
    titleParts: [
      { text: 'Supportive', className: 'text-white' },
      { text: 'Global', className: 'text-[#92FDE1]' },
      { text: 'Community', className: 'text-[#92FDE1]' }
    ],
    description:
      'Be part of a group of 2,000+ members sharing tips, connections, and opportunities',
    icon: '/features-icons/Globe.png',
    className: 'bg-[#118073] text-white hover:bg-teal-800'
  }
];

export default function FeaturesSection() {
  return (
    <section
      id="features"
      className="container space-y-6 bg-gradient-to-b from-white to-gray-50/50 py-8 dark:from-zinc-900 dark:to-zinc-900/50 md:py-12 lg:py-24 rounded-[32px] md:rounded-[48px] mb-10 px-4 md:px-8"
    >
      <div className="mx-auto flex max-w-full flex-col items-center space-y-4 text-center">
        <h2 className="text-2xl leading-[1.1] sm:text-3xl md:text-5xl font-bold px-2">
          What We Offer To Accelerate Your Career
        </h2>
        <p className="max-w-[90%] md:max-w-[65%] leading-normal text-muted-foreground text-sm sm:text-lg sm:leading-7 px-2">
          Your all-in-one platform to find visa-sponsored roles, connect with
          employers, and access tools and support to land your dream job faster.
        </p>
      </div>

      <div className="mx-auto max-w-[64rem] space-y-6 md:space-y-8">
        <Card className="group/bento py-6 md:py-10 px-4 md:px-6 bg-[#F6F6F6] transition-all duration-300 hover:shadow-xl overflow-hidden rounded-[20px]">
          <CardHeader>
            <CardTitle className="text-2xl md:text-[40px] font-normal text-darkestGreen dark:text-teal-500">
              {mainFeature.title}
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            {mainFeature.subFeatures.map((subFeature, index) => (
              <div key={index} className="flex flex-col items-start gap-2">
                <div className="rounded-full p-2">
                  <subFeature.icon className="h-8 w-8 md:h-10 md:w-10 text-teal-500 dark:text-teal-100" />
                </div>
                <h3 className="font-semibold text-neutralBlack">
                  {subFeature.title}
                </h3>
                <p className="text-sm text-neutralBlack">
                  {subFeature.description}
                </p>
              </div>
            ))}
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          {features.map((feature, i) => (
            <Card
              key={i}
              className={cn(
                'group/bento py-6 md:py-10 px-4 md:px-6 bg-[#F6F6F6] transition-all duration-300 hover:shadow-xl relative overflow-hidden rounded-[20px]',
                feature.className
              )}
            >
              <CardHeader>
                <CardTitle className="text-2xl md:text-4xl font-bold space-y-[0.5px]">
                  {feature.titleParts.map((part, index) => (
                    <span key={index} className={`block ${part.className}`}>
                      {part.text}
                    </span>
                  ))}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm max-w-full md:max-w-[70%]">
                  {feature.description}
                </p>
                <Image
                  src={feature.icon || '/placeholder.svg'}
                  alt={feature.titleParts.map((part) => part.text).join(' ')}
                  width={44}
                  height={44}
                  className={cn(
                    'absolute bottom-4 right-4 h-8 w-8 md:h-10 md:w-10 transition-transform duration-300 group-hover/bento:scale-110'
                  )}
                />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
