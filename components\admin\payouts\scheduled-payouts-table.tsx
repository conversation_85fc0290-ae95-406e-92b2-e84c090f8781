'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/ui/use-toast';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  AlertCircle, 
  CalendarDays, 
  CheckCircle, 
  ChevronLeft, 
  ChevronRight, 
  Clock, 
  Edit, 
  Loader2, 
  MoreHorizontal, 
  PlusCircle, 
  RefreshCw, 
  Trash, 
  DollarSign 
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { getInsiders } from '@/actions/admin/insider';
// import { createPayoutSchedule } from '@/actions/insider-payouts';

// Placeholder function that would normally fetch payout schedules
async function getPayoutSchedules() {
  // In a real implementation, this would fetch from the server
  // For now, we'll return sample data
  return {
    data: [
      {
        id: '1',
        insider_id: '123',
        insider_name: 'John Doe',
        insider_email: '<EMAIL>',
        insider_company: 'Acme Inc',
        frequency: 'monthly',
        payout_day: 15,
        next_run_at: new Date(2025, 4, 15).toISOString(),
        is_active: true,
        created_at: new Date(2025, 3, 1).toISOString(),
      },
      {
        id: '2',
        insider_id: '456',
        insider_name: 'Jane Smith',
        insider_email: '<EMAIL>',
        insider_company: 'Globex Corp',
        frequency: 'biweekly',
        payout_day: 1,
        next_run_at: new Date(2025, 4, 1).toISOString(),
        is_active: true,
        created_at: new Date(2025, 3, 15).toISOString(),
      },
    ],
    totalPages: 1,
    totalCount: 2
  };
}

export default function ScheduledPayoutsTable() {
  const router = useRouter();
  const { toast } = useToast();
  
  const [schedules, setSchedules] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showNewScheduleDialog, setShowNewScheduleDialog] = useState(false);
  const [showEditScheduleDialog, setShowEditScheduleDialog] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState<any>(null);
  
  // New schedule form state
  const [insiders, setInsiders] = useState<any[]>([]);
  const [loadingInsiders, setLoadingInsiders] = useState(false);
  const [selectedInsider, setSelectedInsider] = useState('');
  const [frequency, setFrequency] = useState('monthly');
  const [payoutDay, setPayoutDay] = useState('1');
  const [submitting, setSubmitting] = useState(false);
  
  // Load schedules
  useEffect(() => {
    async function loadSchedules() {
      try {
        setLoading(true);
        const response = await getPayoutSchedules();
        setSchedules(response.data);
        setTotalPages(response.totalPages);
      } catch (error) {
        console.error('Error loading payout schedules:', error);
        toast({
          title: "Error",
          description: "Failed to load payout schedules. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    }
    
    loadSchedules();
  }, [toast]);
  
  // Handle refresh
  const handleRefresh = async () => {
    try {
      setRefreshing(true);
      const response = await getPayoutSchedules();
      setSchedules(response.data);
      setTotalPages(response.totalPages);
      toast({
        title: "Refreshed",
        description: "Payout schedules have been refreshed.",
      });
    } catch (error) {
      console.error('Error refreshing payout schedules:', error);
      toast({
        title: "Error",
        description: "Failed to refresh payout schedules. Please try again.",
        variant: "destructive"
      });
    } finally {
      setRefreshing(false);
    }
  };
  
  // Handle pagination
  const handlePaginationChange = (page: number) => {
    setCurrentPage(page);
  };
  
  // Handle edit schedule
  const handleEditSchedule = (schedule: any) => {
    setSelectedSchedule(schedule);
    setSelectedInsider(schedule.insider_id);
    setFrequency(schedule.frequency);
    setPayoutDay(schedule.payout_day.toString());
    setShowEditScheduleDialog(true);
  };
  
  // Handle delete schedule
  const handleDeleteSchedule = async (scheduleId: string) => {
    try {
      // In a real implementation, this would call an API to delete the schedule
      // For now, we'll just remove it from the local state
      setSchedules(schedules.filter(schedule => schedule.id !== scheduleId));
      toast({
        title: "Schedule deleted",
        description: "The payout schedule has been deleted.",
      });
    } catch (error) {
      console.error('Error deleting payout schedule:', error);
      toast({
        title: "Error",
        description: "Failed to delete payout schedule. Please try again.",
        variant: "destructive"
      });
    }
  };
  
  // Handle new schedule dialog open
  const handleNewScheduleDialogOpen = async () => {
    try {
      setLoadingInsiders(true);
      setShowNewScheduleDialog(true);
      
      // Load insiders
      const insidersResponse = await getInsiders({ page: 1 });
      setInsiders(insidersResponse.data);
    } catch (error) {
      console.error('Error loading insiders:', error);
      toast({
        title: "Error",
        description: "Failed to load insiders. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingInsiders(false);
    }
  };
  
  // Handle create new schedule
  const handleCreateSchedule = async () => {
    if (!selectedInsider) {
      toast({
        title: "Error",
        description: "Please select an insider.",
        variant: "destructive"
      });
      return;
    }
    
    try {
      setSubmitting(true);
      
      // In a real implementation, this would call the server action
      // await setPayoutSchedule(
      //   selectedInsider,
      //   frequency as any,
      //   parseInt(payoutDay),
      //   {},
      //   true
      // );
      
      // Add new schedule to the list (for demo purposes)
      const insider = insiders.find(i => i.id === selectedInsider);
      const newSchedule = {
        id: crypto.randomUUID(),
        insider_id: selectedInsider,
        insider_name: `${insider?.first_name} ${insider?.last_name}`,
        insider_email: insider?.user_email,
        insider_company: insider?.public_firm,
        frequency,
        payout_day: parseInt(payoutDay),
        next_run_at: calculateNextRunDate(frequency as any, parseInt(payoutDay)).toISOString(),
        is_active: true,
        created_at: new Date().toISOString(),
      };
      
      setSchedules([newSchedule, ...schedules]);
      
      // Reset form and close dialog
      setSelectedInsider('');
      setFrequency('monthly');
      setPayoutDay('1');
      setShowNewScheduleDialog(false);
      
      toast({
        title: "Schedule created",
        description: "The payout schedule has been created.",
      });
    } catch (error) {
      console.error('Error creating payout schedule:', error);
      toast({
        title: "Error",
        description: "Failed to create payout schedule. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };
  
  // Helper function to calculate next run date
  function calculateNextRunDate(
    frequency: 'weekly' | 'biweekly' | 'monthly' | 'quarterly',
    payoutDay: number
  ): Date {
    const now = new Date();
    const result = new Date(now);
    
    switch (frequency) {
      case 'weekly':
        // payoutDay 0-6 (Sunday-Saturday)
        const currentDay = now.getDay();
        const daysToAdd = (payoutDay - currentDay + 7) % 7;
        result.setDate(now.getDate() + (daysToAdd === 0 ? 7 : daysToAdd));
        break;
        
      case 'biweekly':
        // First calculate next weekly occurrence
        const biweeklyCurrentDay = now.getDay();
        const biweeklyDaysToAdd = (payoutDay - biweeklyCurrentDay + 7) % 7;
        result.setDate(now.getDate() + (biweeklyDaysToAdd === 0 ? 7 : biweeklyDaysToAdd));
        
        // If today is the payout day, add two weeks instead of one
        if (biweeklyCurrentDay === payoutDay) {
          result.setDate(result.getDate() + 7);
        }
        break;
        
      case 'monthly':
        // payoutDay 1-31 (day of month)
        result.setMonth(now.getMonth() + 1);
        result.setDate(Math.min(payoutDay, getDaysInMonth(result.getFullYear(), result.getMonth())));
        
        // If the calculated date is in the past (happens when current day > payoutDay), move to next month
        if (result < now) {
          result.setMonth(result.getMonth() + 1);
          result.setDate(Math.min(payoutDay, getDaysInMonth(result.getFullYear(), result.getMonth())));
        }
        break;
        
      case 'quarterly':
        // First set to the next occurrence of the day in a month
        result.setMonth(now.getMonth() + 1);
        result.setDate(Math.min(payoutDay, getDaysInMonth(result.getFullYear(), result.getMonth())));
        
        // Then adjust to next quarter if needed
        const targetMonth = Math.floor(result.getMonth() / 3) * 3 + 3;
        result.setMonth(targetMonth);
        result.setDate(Math.min(payoutDay, getDaysInMonth(result.getFullYear(), result.getMonth())));
        
        // If the calculated date is in the past, move to next quarter
        if (result < now) {
          result.setMonth(result.getMonth() + 3);
          result.setDate(Math.min(payoutDay, getDaysInMonth(result.getFullYear(), result.getMonth())));
        }
        break;
    }
    
    // Reset time to beginning of day
    result.setHours(0, 0, 0, 0);
    
    return result;
  }
  
  // Helper to get days in month
  function getDaysInMonth(year: number, month: number): number {
    return new Date(year, month + 1, 0).getDate();
  }
  
  // Get available payout days based on frequency
  const getPayoutDayOptions = () => {
    if (frequency === 'weekly' || frequency === 'biweekly') {
      // Days of the week
      return [
        { value: '0', label: 'Sunday' },
        { value: '1', label: 'Monday' },
        { value: '2', label: 'Tuesday' },
        { value: '3', label: 'Wednesday' },
        { value: '4', label: 'Thursday' },
        { value: '5', label: 'Friday' },
        { value: '6', label: 'Saturday' },
      ];
    } else {
      // Days of the month (1-28 to be safe)
      return Array.from({ length: 28 }, (_, i) => ({
        value: (i + 1).toString(),
        label: (i + 1).toString(),
      }));
    }
  };
  
  if (loading) {
    return (
      <div className="flex justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <Button 
          onClick={handleRefresh} 
          variant="outline" 
          size="sm"
          disabled={refreshing}
        >
          {refreshing ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          Refresh
        </Button>
        
        <Button onClick={handleNewScheduleDialogOpen} size="sm">
          <PlusCircle className="h-4 w-4 mr-2" />
          New Schedule
        </Button>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Insider</TableHead>
              <TableHead>Frequency</TableHead>
              <TableHead>Payout Day</TableHead>
              <TableHead>Next Payout</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {schedules.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  No payout schedules found
                </TableCell>
              </TableRow>
            ) : (
              schedules.map((schedule) => (
                <TableRow key={schedule.id}>
                  <TableCell className="font-medium">
                    <div className="flex flex-col">
                      <span>{schedule.insider_name}</span>
                      <span className="text-xs text-muted-foreground mt-1">
                        {schedule.insider_email}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {schedule.insider_company}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {schedule.frequency}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {schedule.frequency === 'weekly' || schedule.frequency === 'biweekly' ? (
                      // Convert day number to day name
                      ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][schedule.payout_day]
                    ) : (
                      // Day of month
                      `Day ${schedule.payout_day}`
                    )}
                  </TableCell>
                  <TableCell>
                    {new Date(schedule.next_run_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </TableCell>
                  <TableCell>
                    {/* <Badge
                      variant={schedule.is_active ? 'success' : 'secondary'}
                    >
                      {schedule.is_active ? 'Active' : 'Inactive'}
                    </Badge> */}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          onClick={() => handleEditSchedule(schedule)}
                        >
                          <Edit className="h-4 w-4 mr-2" /> Edit Schedule
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => router.push(`/admin/payouts/process/${schedule.id}`)}
                        >
                          <DollarSign className="h-4 w-4 mr-2" /> Process Now
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => handleDeleteSchedule(schedule.id)}
                          className="text-red-600"
                        >
                          <Trash className="h-4 w-4 mr-2" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {totalPages > 1 && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePaginationChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePaginationChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      )}
      
      {/* Create New Schedule Dialog */}
      <Dialog open={showNewScheduleDialog} onOpenChange={setShowNewScheduleDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Payout Schedule</DialogTitle>
            <DialogDescription>
              Set up an automatic payout schedule for an insider.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6 py-4">
            <div className="space-y-2">
              <Label htmlFor="insider">Select Insider</Label>
              {loadingInsiders ? (
                <div className="flex justify-center py-4">
                  <Loader2 className="h-6 w-6 animate-spin text-primary" />
                </div>
              ) : (
                <select
                  id="insider"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  value={selectedInsider}
                  onChange={(e) => setSelectedInsider(e.target.value)}
                >
                  <option value="">Select an insider...</option>
                  {insiders.map((insider) => (
                    <option key={insider.id} value={insider.id}>
                      {insider.first_name} {insider.last_name} - {insider.public_firm}
                    </option>
                  ))}
                </select>
              )}
            </div>
            
            <div className="space-y-2">
              <Label>Frequency</Label>
              <RadioGroup
                value={frequency}
                onValueChange={setFrequency}
                className="grid grid-cols-2 gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="weekly" id="weekly" />
                  <Label htmlFor="weekly">Weekly</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="biweekly" id="biweekly" />
                  <Label htmlFor="biweekly">Bi-weekly</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="monthly" id="monthly" />
                  <Label htmlFor="monthly">Monthly</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="quarterly" id="quarterly" />
                  <Label htmlFor="quarterly">Quarterly</Label>
                </div>
              </RadioGroup>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="payoutDay">
                {frequency === 'weekly' || frequency === 'biweekly' 
                  ? 'Payout Day of Week' 
                  : 'Payout Day of Month'}
              </Label>
              <select
                id="payoutDay"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={payoutDay}
                onChange={(e) => setPayoutDay(e.target.value)}
              >
                {getPayoutDayOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            <Alert>
              <CalendarDays className="h-4 w-4" />
              <AlertTitle>Automated Payouts</AlertTitle>
              <AlertDescription>
                Each payout will need manual approval before processing. You'll receive notifications when payouts are due.
              </AlertDescription>
            </Alert>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowNewScheduleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateSchedule} disabled={submitting || !selectedInsider}>
              {submitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Schedule
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Edit Schedule Dialog - would be similar to Create with pre-filled values */}
      <Dialog open={showEditScheduleDialog} onOpenChange={setShowEditScheduleDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Payout Schedule</DialogTitle>
            <DialogDescription>
              Update the payout schedule for this insider.
            </DialogDescription>
          </DialogHeader>
          
          {/* Form would be similar to create with pre-filled values */}
          <div className="py-4">
            <p className="text-center text-muted-foreground">
              Edit functionality would be implemented here...
            </p>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditScheduleDialog(false)}>
              Cancel
            </Button>
            <Button onClick={() => setShowEditScheduleDialog(false)}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
