'use client';
import { useEffect } from 'react';
import { redirect } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useUser } from '@/hooks/useUser';

export default function DashboardPage() {
  const { user, loading } = useUser();
  const router = useRouter();

  useEffect(() => {
    // First check if user is logged in
    if (!loading && !user) {
      router.push('/signin');
      return;
    }

    // Then redirect based on user type
    if (!loading && user) {
      const userType = user.user_type?.toLowerCase() || 'candidate';
      if (userType === 'candidate') {
        router.push('/candidate');
        return;
      } else if (userType === 'company') {
        router.push('/company');
        return;
      } else if (userType === 'insider') {
        router.push('/insider');
        return;
      } else if (userType === 'admin') {
        router.push('/admin');
        return;
      }else{
        router.push('/');
        return;
      }
    }
  }, [user, loading, router]);

  // If we're still loading, show a loading indicator
  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    );
  }

  // If the user is not logged in, redirect to signin
  if (!user && !loading) {
    return redirect('/signin');
  }

  // Return null as the useEffect will handle all redirects
  return null;
}
