import { bubbleClient } from '@/utils/bubble/client';
import { createClient } from '@/utils/supabase/client';
import { checkUserExistsByEmail } from '@/utils/supabase/admin';
import { getURL } from '@/utils/helpers';
import type { Provider } from '@supabase/supabase-js';

export interface MigrationCheckResult {
  needsMigration: boolean;
  existsInAuth: boolean;
  existsInProfiles: boolean;
  userProfile?: any; // Profile data from database if found
  error?: string;
}

// MigrationCheckOptions is no longer needed as we now directly check
// user existence with the admin API

/**
 * Checks if a user needs migration (exists in profiles but not in auth)
 * @param email User email to check
 * @returns Object with migration status and user profile if found
 */
export async function checkUserNeedsMigration(
  email: string
): Promise<MigrationCheckResult> {
  try {
    // Create client for profiles check
    const supabase = createClient();
    
    // Check if user exists in auth system using admin API
    const userExistsInAuth = await checkUserExistsByEmail(email);
    
    // Check if user exists in profiles table
    const { data: profileUser, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .maybeSingle();
    
    console.log('Profile data:', profileUser);
    
    if (profileError) {
      throw profileError;
    }
    
    const userExistsInProfiles = !!profileUser;
    console.log('Auth exists:', userExistsInAuth, 'Profile exists:', userExistsInProfiles);
    
    // User needs migration if they exist in profiles table but not in auth
    return {
      needsMigration: !userExistsInAuth && userExistsInProfiles,
      existsInAuth: userExistsInAuth,
      existsInProfiles: userExistsInProfiles,
      userProfile: profileUser || undefined
    };
  } catch (error) {
    console.error('Error checking migration status:', error);
    return {
      needsMigration: false,
      existsInAuth: false,
      existsInProfiles: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Sends a magic link to user for migrating their account
 * @param email User email
 * @returns Success/error status
 */
export async function sendMigrationMagicLink(email: string) {
  try {
    const supabase = createClient();
    
    // Get redirect URL for after login
    const redirectTo = `${getURL()}/auth/set-password`;
    
    // Send magic link
    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: redirectTo
      }
    });
    
    if (error) {
      throw error;
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error sending migration magic link:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Login with OAuth for migration
 * @param provider OAuth provider
 * @param email User email
 * @returns Success/error status
 */
export async function migrateWithOAuth(provider: Provider, email: string) {
  try {
    const supabase = createClient();
    const redirectTo = `${getURL()}/auth/callback?migration=true&email=${encodeURIComponent(email)}`;
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent'
        }
      }
    });
    
    if (error) {
      throw error;
    }
    
    return { success: true };
  } catch (error) {
    console.error('Error during OAuth migration:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
