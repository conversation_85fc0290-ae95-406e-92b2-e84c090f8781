// components/mobile-nav.tsx

'use client';

import * as React from 'react';
import Link from 'next/link';
import { MainNavItem } from 'types';
import { cn } from '@/lib/utils';
import { Icons } from '@/components/icons';
import { buttonVariants } from '@/components/ui/button';
import { ModeToggle } from '@/components/mode-toggle';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import Image from 'next/image';
import { signOut } from '@/utils/bubble/auth';
import { useToast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

interface MobileNavProps {
  items: MainNavItem[];
  children?: React.ReactNode;
  user?: boolean;
  userType?: string | null;
  onClose?: () => void;
}

export function MobileNav({
  items,
  children,
  user,
  userType,
  onClose
}: MobileNavProps) {
  const { toast } = useToast();
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      const result = await signOut();
      toast({
        title: 'Signed out',
        description: 'See you soon!'
      });
      // Handle redirection if a path is provided and router exists
      if (result.redirectPath && router) {
        router.push(result.redirectPath);
      }
      window.location.reload();
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  return (
    <div
      className={cn(
        'fixed inset-0 top-0 z-50 w-full h-full bg-background/95 backdrop-blur-sm'
      )}
    >
      <div className="relative z-20 grid gap-6 rounded-md bg-background p-6 text-popover-foreground shadow-md max-w-[100vw] h-full overflow-y-auto">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-2">
            <div className="bg-slate-50 dark:bg-slate-900 p-1 rounded-full">
              <Image src="/favicon_new.png" alt="logo" width={25} height={25} />
            </div>
            <span className="font-bold text-lg">InternUp</span>
          </Link>
          <button
            onClick={onClose}
            className="rounded-full p-2 hover:bg-muted flex items-center justify-center"
          >
            <Icons.close className="h-5 w-5" />
            <span className="sr-only">Close</span>
          </button>
        </div>

        <nav className="grid grid-flow-row auto-rows-max text-sm gap-2 mt-2">
          {items.map((item, index) =>
            item.items && item.items.length > 0 ? (
              <Accordion
                type="single"
                collapsible
                key={index}
                className="w-full"
              >
                <AccordionItem
                  value={`item-${index}`}
                  className="border-b border-muted/30"
                >
                  <AccordionTrigger
                    className={cn(
                      'flex w-full items-center rounded-md py-3 text-base font-medium hover:bg-muted/50',
                      item.disabled && 'cursor-not-allowed opacity-60'
                    )}
                  >
                    {item.title}
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="pl-4 flex flex-col space-y-1">
                      {item.items.map((subItem, subIndex) => (
                        <Link
                          key={subIndex}
                          href={subItem.href || '#'}
                          onClick={onClose}
                          className={cn(
                            'flex w-full items-center rounded-md py-2 px-2 text-sm font-medium hover:bg-muted',
                            subItem.disabled && 'cursor-not-allowed opacity-60'
                          )}
                        >
                          {subItem.title}
                        </Link>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ) : (
              <Link
                key={index}
                href={item.href || '#'}
                onClick={onClose}
                className={cn(
                  'flex w-full items-center rounded-md py-3 px-2 text-base font-medium hover:bg-muted/50 border-b border-muted/30',
                  item.disabled && 'cursor-not-allowed opacity-60'
                )}
              >
                {item.title}
              </Link>
            )
          )}
        </nav>

        <div className="mt-auto pt-4 border-t border-border">
          <div className="flex flex-col gap-2">
            <ModeToggle />
            <Link
              href={
                user
                  ? (() => {
                      switch (userType?.toLowerCase()) {
                        case 'admin':
                          return '/admin';
                        case 'candidate':
                          return '/candidate';
                        case 'insider':
                          return '/insider';
                        case 'company':
                          return '/company';
                        default:
                          return '/';
                      }
                    })()
                  : '/signin'
              }
              onClick={onClose}
              className={cn(
                buttonVariants({ variant: 'default', size: 'default' }),
                'w-full justify-center'
              )}
            >
              {user ? 'Dashboard' : 'Login'}
            </Link>
            {!user && (
              <Link
                href="/signup"
                onClick={onClose}
                className={cn(
                  buttonVariants({ variant: 'outline', size: 'default' }),
                  'w-full justify-center'
                )}
              >
                Sign Up
              </Link>
            )}
            {user && (
              <button
                onClick={() => {
                  handleSignOut();
                  onClose?.();
                }}
                className={cn(
                  buttonVariants({ variant: 'outline', size: 'default' }),
                  'w-full justify-center text-destructive hover:text-destructive'
                )}
              >
                Logout
              </button>
            )}
          </div>
        </div>
        {children}
      </div>
    </div>
  );
}
